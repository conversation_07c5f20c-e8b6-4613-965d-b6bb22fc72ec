[{"name": "CFPropertyList", "version": "3.0.7", "platform": "ruby", "checksum": "c45721614aca8d5eb6fa216f2ec28ec38de1a94505e9766a20e98745492c3c4c"}, {"name": "RedCloth", "version": "4.3.4", "platform": "ruby", "checksum": "5231b2fdd91a933915cba330e5fd1a74025e77b56f57b7404c7191ebf2812297"}, {"name": "acme-client", "version": "2.0.25", "platform": "ruby", "checksum": "e0bba7b9f785fd9ffe0933f8733ca81357ac46e4a979cb4f84806ab88fee0f31"}, {"name": "actioncable", "version": "*******", "platform": "ruby", "checksum": "3d957adc9d1d2ddb5ac8ed8791dc35b273c722f2dca2644f415bd24ba64c7425"}, {"name": "actionmailbox", "version": "*******", "platform": "ruby", "checksum": "7f784a3685a877ca0937bf28f989bbafbffd0f3966d724e41bf9319f881487ef"}, {"name": "actionmailer", "version": "*******", "platform": "ruby", "checksum": "d37c8ab094f3b73c3fbcbbf41d2e31fc15b607178569a58057ed878c2063a6e6"}, {"name": "actionpack", "version": "*******", "platform": "ruby", "checksum": "ccd2f96ffb378060dd02e86318bca3faae1ecf483603a525fabfd84197c86a6e"}, {"name": "actiontext", "version": "*******", "platform": "ruby", "checksum": "8e80623cf206f077f4b671846ba74b0cb154b2a306a6569d3c4b3deb22e2b701"}, {"name": "actionview", "version": "*******", "platform": "ruby", "checksum": "5bf67e9716fbd159f09cbc8cf87f4813d3e8725f0197a7321910e9dc8c165b07"}, {"name": "<PERSON><PERSON><PERSON>", "version": "*******", "platform": "ruby", "checksum": "e706383862084022d531eee64f74ac4b5fd751f160a7138d3a3c1018b2facb55"}, {"name": "activemodel", "version": "*******", "platform": "ruby", "checksum": "6898b91af028d725729f65d8e0f6ccfef5993e085ed70d5b93c42ba1bf7384dd"}, {"name": "activerecord", "version": "*******", "platform": "ruby", "checksum": "e6b1e1499018f1c3ffd9f7828a8560588da1f5bd85dc2b7a95e49c5467cda800"}, {"name": "activestorage", "version": "*******", "platform": "ruby", "checksum": "0b28d0c191b03162e83d3bf6875c3692ab48abd1e371bb0b428136dd8509ae66"}, {"name": "activesupport", "version": "*******", "platform": "ruby", "checksum": "c54e84bb3d9027f1f372fb8f68203538fcfe0d5ff42801774c03974daa15bef0"}, {"name": "addressable", "version": "2.8.7", "platform": "ruby", "checksum": "462986537cf3735ab5f3c0f557f14155d778f4b43ea4f485a9deb9c8f7c58232"}, {"name": "aes_key_wrap", "version": "1.1.0", "platform": "ruby", "checksum": "b935f4756b37375895db45669e79dfcdc0f7901e12d4e08974d5540c8e0776a5"}, {"name": "<PERSON><PERSON><PERSON>", "version": "3.0.0", "platform": "ruby", "checksum": "74991b8e3d3257eeea996b47069abb8da2006c84a144255123e8dffd1c86b230"}, {"name": "aliyun-sdk", "version": "0.8.0", "platform": "ruby", "checksum": "65915d3f9b528082253d1f9ad0e4d13d6b552933fe49251c68c6915cd4d75b9d"}, {"name": "amatch", "version": "0.4.1", "platform": "ruby", "checksum": "d3ff15226a2e627c72802e94579db829e5e10c96cf89d329494caec5889145f7"}, {"name": "android_key_attestation", "version": "0.3.0", "platform": "ruby", "checksum": "467eb01a99d2bb48ef9cf24cc13712669d7056cba5a52d009554ff037560570b"}, {"name": "apollo_upload_server", "version": "2.1.6", "platform": "ruby", "checksum": "dcec4072258e6518b0b82e03b485efbddde946813543c14184fc81952d6bcdb2"}, {"name": "app_store_connect", "version": "0.38.0", "platform": "ruby", "checksum": "30f624109298ed009a36408edac264a5200d967d28d960f3e37f02e9cab25ce6"}, {"name": "arr-pm", "version": "0.0.12", "platform": "ruby", "checksum": "fdff482f75239239201f4d667d93424412639aad0b3b0ad4d827e7c637e0ad39"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "2.0.23", "platform": "ruby", "checksum": "52208807f237dfa0ca29882f8b13d60b820496116ad191cf197ca56f2b7fddf3"}, {"name": "asciidoctor-include-ext", "version": "0.4.0", "platform": "ruby", "checksum": "406adb9d2fbfc25536609ca13b787ed704dc06a4e49d6709b83f3bad578f7878"}, {"name": "asci<PERSON>ctor-kroki", "version": "0.10.0", "platform": "ruby", "checksum": "8e4225d88f120e2e7b5d3f5ddb67c5e69496d7344a16c57db5036ac900123062"}, {"name": "asciidoctor-plantuml", "version": "0.0.16", "platform": "ruby", "checksum": "407e47cd1186ded5ccc75f0c812e5524c26c571d542247c5132abb8f47bd1793"}, {"name": "ast", "version": "2.4.2", "platform": "ruby", "checksum": "1e280232e6a33754cde542bc5ef85520b74db2aac73ec14acef453784447cc12"}, {"name": "async", "version": "2.27.0", "platform": "ruby", "checksum": "6369a93f6f286c090f01eeb35e9ac3d4a6d50e53ecf66547ba2a1c3aacb1c451"}, {"name": "atlassian-jwt", "version": "0.2.1", "platform": "ruby", "checksum": "2fd2d87418773f2e140c038cb22e049069708aff2bd0a423a7e1740574e97823"}, {"name": "attr_encrypted", "version": "4.2.0", "platform": "ruby", "checksum": "7e5c80159e6e38ed40dc4e2e65c4f57234fe1f376bddc40c8b773bfb9b81ad51"}, {"name": "attr_required", "version": "1.0.2", "platform": "ruby", "checksum": "f0ebfc56b35e874f4d0ae799066dbc1f81efefe2364ca3803dc9ea6a4de6cb99"}, {"name": "awesome_print", "version": "1.9.2", "platform": "ruby", "checksum": "e99b32b704acff16d768b3468680793ced40bfdc4537eb07e06a4be11133786e"}, {"name": "awrence", "version": "1.2.1", "platform": "ruby", "checksum": "dd1d214c12a91f449d1ef81d7ee3babc2816944e450752e7522c65521872483e"}, {"name": "aws-eventstream", "version": "1.3.0", "platform": "ruby", "checksum": "f1434cc03ab2248756eb02cfa45e900e59a061d7fbdc4a9fd82a5dd23d796d3f"}, {"name": "aws-partitions", "version": "1.1001.0", "platform": "ruby", "checksum": "2979f3317d3a757508d35d0f322839f422cbc8459589b7cc4a3889d0085a8307"}, {"name": "aws-sdk-cloudformation", "version": "1.133.0", "platform": "ruby", "checksum": "490393d6e66848a5dd48f536f650de4237a6082f195ef8ea1e27de551e9ca321"}, {"name": "aws-sdk-core", "version": "3.226.3", "platform": "ruby", "checksum": "7c71d0ddbd5577e3389070cd50d9e7fb90726bd8263beced8889c84b75892010"}, {"name": "aws-sdk-kms", "version": "1.76.0", "platform": "ruby", "checksum": "e7f75013cba9ba357144f66bbc600631c192e2cda9dd572794be239654e2cf49"}, {"name": "aws-sdk-s3", "version": "1.193.0", "platform": "ruby", "checksum": "ccb954977c43886df7fa2d70a067ed68fc07c54d575de36f5086247a2b113bde"}, {"name": "aws-sigv4", "version": "1.9.1", "platform": "ruby", "checksum": "7753e320c39f80f82f9e0883b30de0e7b99e756adbaedc80c50b6ad59d49c379"}, {"name": "axe-core-api", "version": "4.10.3", "platform": "ruby", "checksum": "6e10f3ed1c031804f16e8154d9d5dc658564d10850cee860e125fe665c3f0148"}, {"name": "axe-core-rspec", "version": "4.10.3", "platform": "ruby", "checksum": "ca21d0111e2d0fcd0f1da922c9071337336732aa6a3a8dc21bed94c9a701527e"}, {"name": "axiom-types", "version": "0.1.1", "platform": "ruby", "checksum": "c1ff113f3de516fa195b2db7e0a9a95fd1b08475a502ff660d04507a09980383"}, {"name": "babosa", "version": "2.0.0", "platform": "ruby", "checksum": "a6218db8a4dc8fd99260dde8bc3d5fa1a0c52178196e236ebb31e41fbdcdb8a6"}, {"name": "backport", "version": "1.2.0", "platform": "ruby", "checksum": "912c7dfdd9ee4625d013ddfccb6205c3f92da69a8990f65c440e40f5b2fc7f75"}, {"name": "base32", "version": "0.3.4", "platform": "ruby", "checksum": "cb9810ab7c79862ed6ead254b3a44fa2535d088396cd412eef38bdc206055aba"}, {"name": "base64", "version": "0.2.0", "platform": "ruby", "checksum": "0f25e9b21a02a0cc0cea8ef92b2041035d39350946e8789c562b2d1a3da01507"}, {"name": "batch-loader", "version": "2.0.5", "platform": "ruby", "checksum": "964bf638b8f498bab40abaafc6f89c057b2e02aa25b64fc1ec12872ad6bff213"}, {"name": "bcrypt", "version": "3.1.20", "platform": "java", "checksum": "8236dff31f6f36cffe334939e4ea59c41e1c94e2e246c01783575fa6df40373f"}, {"name": "bcrypt", "version": "3.1.20", "platform": "ruby", "checksum": "8410f8c7b3ed54a3c00cd2456bf13917d695117f033218e2483b2e40b0784099"}, {"name": "benchmark", "version": "0.4.0", "platform": "ruby", "checksum": "0f12f8c495545e3710c3e4f0480f63f06b4c842cc94cec7f33a956f5180e874a"}, {"name": "benchmark-ips", "version": "2.14.0", "platform": "ruby", "checksum": "b72bc8a65d525d5906f8cd94270dccf73452ee3257a32b89fbd6684d3e8a9b1d"}, {"name": "benchmark-malloc", "version": "0.2.0", "platform": "ruby", "checksum": "37c68f0435261634026f584d79956a35325a3027e3e6b4cc8d7575aa10537e6b"}, {"name": "benchmark-memory", "version": "0.2.0", "platform": "ruby", "checksum": "ca1e436433b09535ee8f64f80600a5edb407cff1f6ac70e089ca238118e6ab5c"}, {"name": "benchmark-perf", "version": "0.6.0", "platform": "ruby", "checksum": "fe2b01959f3de0f9dd34820d54ef881eb4f3589fccb7d17b63068ac92d7f9621"}, {"name": "benchmark-trend", "version": "0.4.0", "platform": "ruby", "checksum": "de5a02a9f443babefbbd97784759820decee8554a0c273d859c02a0990845d81"}, {"name": "better_errors", "version": "2.10.1", "platform": "ruby", "checksum": "f798f1bac93f3e775925b7fcb24cffbcf0bb62ee2210f5350f161a6b75fc0a73"}, {"name": "bigdecimal", "version": "3.1.7", "platform": "java", "checksum": "955f5c7aa90136874b494655e42ed70d81382abb0f49f1b42f374a1660e33c63"}, {"name": "bigdecimal", "version": "3.1.7", "platform": "ruby", "checksum": "e799b369a0005fc6d62eed7ef19139ac9bc319cc51470c637b9dcdf593600133"}, {"name": "bindata", "version": "2.4.11", "platform": "ruby", "checksum": "c38e0c99ffcd80c10a0a7ae6c8586d2fe26bf245cbefac90bec8764523220f6a"}, {"name": "binding_of_caller", "version": "1.0.0", "platform": "ruby", "checksum": "3aad25d1d538fc6e7972978f9bf512ccd992784009947c81633bea776713161d"}, {"name": "bootsnap", "version": "1.18.6", "platform": "ruby", "checksum": "0ae2393c1e911e38be0f24e9173e7be570c3650128251bf06240046f84a07d00"}, {"name": "browser", "version": "5.3.1", "platform": "ruby", "checksum": "62745301701ff2c6c5d32d077bb12532b20be261929dcb52c6781ed0d5658b3c"}, {"name": "builder", "version": "3.2.4", "platform": "ruby", "checksum": "99caf08af60c8d7f3a6b004029c4c3c0bdaebced6c949165fe98f1db27fbbc10"}, {"name": "bullet", "version": "8.0.8", "platform": "ruby", "checksum": "b4b9905eb6b803d9a0ba620944ed79c8bb27ff3ca90ef8f8e39ff21db5b7c542"}, {"name": "byebug", "version": "12.0.0", "platform": "ruby", "checksum": "d4a150d291cca40b66ec9ca31f754e93fed8aa266a17335f71bb0afa7fca1a1e"}, {"name": "capybara", "version": "3.40.0", "platform": "ruby", "checksum": "42dba720578ea1ca65fd7a41d163dd368502c191804558f6e0f71b391054aeef"}, {"name": "capybara-screenshot", "version": "1.0.26", "platform": "ruby", "checksum": "816b9370a07752097c82a05f568aaf5d3b7f45c3db5d3aab2014071e1b3c0c77"}, {"name": "carrierwave", "version": "1.3.4", "platform": "ruby", "checksum": "81772dabd1830edbd7f4526d2ae2c79f974f1d48900c3f03f7ecb7c657463a21"}, {"name": "cbor", "version": "0.5.9.8", "platform": "ruby", "checksum": "9ee097fc58d9bc5e406d112cd2d4e112c7354ec16f8b6ff34e4732c1e44b4eb7"}, {"name": "character_set", "version": "1.8.0", "platform": "java", "checksum": "2d94ac33d6569434cf1ba464012b5e98010f5dafbd7b750e8d7db79f4c8eb8f7"}, {"name": "character_set", "version": "1.8.0", "platform": "ruby", "checksum": "2b7317462adaedff0bd1576ae86d71bc5efe133a5d0b7c257021b00fe3153f51"}, {"name": "charlock_holmes", "version": "0.7.9", "platform": "ruby", "checksum": "b49e8a11ce1921e2c5b65511bb864ae51720ce9bd1c336ccf0e89e6c8ae62db0"}, {"name": "chef-config", "version": "18.3.0", "platform": "ruby", "checksum": "c183a2ff41da8d63b1e4a60853c9c701a053ab9afe13df767a578db5f07072df"}, {"name": "chef-utils", "version": "18.3.0", "platform": "ruby", "checksum": "827f7aace26ba9f5f8aca45059644205cc715baded80229f1fd5518d21970701"}, {"name": "chunky_png", "version": "1.4.0", "platform": "ruby", "checksum": "89d5b31b55c0cf4da3cf89a2b4ebc3178d8abe8cbaf116a1dba95668502fdcfe"}, {"name": "circuitbox", "version": "2.0.0", "platform": "ruby", "checksum": "496e9c1e76496e1e141490085f6cdcc4a8dedc72da8361bef69d8c5423b4da14"}, {"name": "citrus", "version": "3.0.2", "platform": "ruby", "checksum": "4ec2412fc389ad186735f4baee1460f7900a8e130ffe3f216b30d4f9c684f650"}, {"name": "claide", "version": "1.1.0", "platform": "ruby", "checksum": "6d3c5c089dde904d96aa30e73306d0d4bd444b1accb9b3125ce14a3c0183f82e"}, {"name": "claide-plugins", "version": "0.9.2", "platform": "ruby", "checksum": "c7ea78bc067ab23bce8515497cdcdcb8f01c86dadfbe13c44644e382922c1c2e"}, {"name": "click_house-client", "version": "0.5.1", "platform": "ruby", "checksum": "10022af0f6ae529daa3ae32e86ca62f5a236fa5fdb7893f1bd4a3133137e2d69"}, {"name": "coderay", "version": "1.1.3", "platform": "ruby", "checksum": "dc530018a4684512f8f38143cd2a096c9f02a1fc2459edcfe534787a7fc77d4b"}, {"name": "coercible", "version": "1.0.0", "platform": "ruby", "checksum": "5081ad24352cc8435ce5472bc2faa30260c7ea7f2102cc6a9f167c4d9bffaadc"}, {"name": "colored2", "version": "3.1.2", "platform": "ruby", "checksum": "b13c2bd7eeae2cf7356a62501d398e72fde78780bd26aec6a979578293c28b4a"}, {"name": "commonmarker", "version": "0.23.11", "platform": "ruby", "checksum": "9d1d35d358740151bce29235aebfecc63314fb57dd89a83e72d4061b4fe3d2bf"}, {"name": "concurrent-ruby", "version": "1.3.5", "platform": "ruby", "checksum": "813b3e37aca6df2a21a3b9f1d497f8cbab24a2b94cab325bffe65ee0f6cbebc6"}, {"name": "connection_pool", "version": "2.5.3", "platform": "ruby", "checksum": "cfd74a82b9b094d1ce30c4f1a346da23ee19dc8a062a16a85f58eab1ced4305b"}, {"name": "console", "version": "1.29.2", "platform": "ruby", "checksum": "afd9b75a1b047059dda22df0e3c0a386e96f50f6752c87c4b00b1a9fcbe77cd6"}, {"name": "cork", "version": "0.3.0", "platform": "ruby", "checksum": "a0a0ac50e262f8514d1abe0a14e95e71c98b24e3378690e5d044daf0013ad4bc"}, {"name": "cose", "version": "1.3.0", "platform": "ruby", "checksum": "63247c66a5bc76e53926756574fe3724cc0a88707e358c90532ae2a320e98601"}, {"name": "countries", "version": "4.0.1", "platform": "ruby", "checksum": "d32e8a3c0b22949f1a41ea6d9005f5168ffce226f8fe077d1d6be785fffa81c5"}, {"name": "coverband", "version": "6.1.5", "platform": "ruby", "checksum": "45173be9b00f70c1700d5a0d5bde1d553288862741dc5c602c58d3b3fb0f8225"}, {"name": "crack", "version": "0.4.3", "platform": "ruby", "checksum": "5318ba8cd9cf7e0b5feb38948048503ba4b1fdc1b6ff30a39f0a00feb6036b29"}, {"name": "crass", "version": "1.0.6", "platform": "ruby", "checksum": "dc516022a56e7b3b156099abc81b6d2b08ea1ed12676ac7a5657617f012bd45d"}, {"name": "creole", "version": "0.5.0", "platform": "ruby", "checksum": "951701e2d80760f156b1cb2a93471ca97c076289becc067a33b745133ed32c03"}, {"name": "css_parser", "version": "1.14.0", "platform": "ruby", "checksum": "f2ce6148cd505297b07bdbe7a5db4cce5cf530071f9b732b9a23538d6cdc0113"}, {"name": "cssbundling-rails", "version": "1.4.3", "platform": "ruby", "checksum": "53aecd5a7d24ac9c8fcd92975acd0e830fead4ee4583d3d3d49bb64651946e41"}, {"name": "csv", "version": "3.3.0", "platform": "ruby", "checksum": "0bbd1defdc31134abefed027a639b3723c2753862150f4c3ee61cab71b20d67d"}, {"name": "cvss-suite", "version": "3.3.0", "platform": "ruby", "checksum": "54199fc1e0d5d833b1cb161453439143665c644cd3be2d21c2eee57fa06ed843"}, {"name": "danger", "version": "9.4.2", "platform": "ruby", "checksum": "43e552c6731030235a30fdeafe703d2e2ab9c30917154489cb0ecd9ad3259d80"}, {"name": "danger-gitlab", "version": "8.0.0", "platform": "ruby", "checksum": "497dd7d0f6513913de651019223d8058cf494df10acbd17de92b175dfa04a3a8"}, {"name": "database_cleaner-active_record", "version": "2.2.1", "platform": "ruby", "checksum": "be47005de91e48f97841f8220f7cbd57b13682073016caac7ce332e6c77bbfcb"}, {"name": "database_cleaner-core", "version": "2.0.1", "platform": "ruby", "checksum": "8646574c32162e59ed7b5258a97a208d3c44551b854e510994f24683865d846c"}, {"name": "date", "version": "3.4.1", "platform": "java", "checksum": "74740d914c65a922a15657c25ff0e203c16f1d0f7aa910a9ebed712afe9819c4"}, {"name": "date", "version": "3.4.1", "platform": "ruby", "checksum": "bf268e14ef7158009bfeaec40b5fa3c7271906e88b196d958a89d4b408abe64f"}, {"name": "deb_version", "version": "1.0.2", "platform": "ruby", "checksum": "c21f911d7f2fd1d61219caae254fc078e6598e477fdff8a05a18bec6c72ee713"}, {"name": "debug", "version": "1.11.0", "platform": "ruby", "checksum": "1425db64cfa0130c952684e3dc974985be201dd62899bf4bbe3f8b5d6cf1aef2"}, {"name": "debug_inspector", "version": "1.1.0", "platform": "ruby", "checksum": "eaa5a2d0195e1d65fb4164e8e7e466cca2e7eb53bc5e608cf12b8bf02c3a8606"}, {"name": "deckar01-task_list", "version": "2.3.4", "platform": "ruby", "checksum": "66abdc7e009ea759732bb53867e1ea42de550e2aa03ac30a015cbf42a04c1667"}, {"name": "declarative", "version": "0.0.20", "platform": "ruby", "checksum": "8021dd6cb17ab2b61233c56903d3f5a259c5cf43c80ff332d447d395b17d9ff9"}, {"name": "declarative_policy", "version": "2.0.1", "platform": "ruby", "checksum": "5ac5a67fc87edad6ef89b12ff8916520c8d11cb95e16529c259c93ef0ec3e6e8"}, {"name": "deprecation_toolkit", "version": "2.2.4", "platform": "ruby", "checksum": "b1dad75eaeccd22327ee98f6a0b9d4dcd2e13274ff9069cf6b31d9879dcb2526"}, {"name": "derailed_benchmarks", "version": "2.2.1", "platform": "ruby", "checksum": "654280664fded41c9cd8fc27fc0fcfaf096023afab90eb4ac1185ba70c5d4439"}, {"name": "descendants_tracker", "version": "0.0.4", "platform": "ruby", "checksum": "e9c41dd4cfbb85829a9301ea7e7c48c2a03b26f09319db230e6479ccdc780897"}, {"name": "devfile", "version": "0.4.7", "platform": "aarch64-linux", "checksum": "30c9fd3c33949eb37fa2124c2984af807017b3b1b998574c2d082d32d3625349"}, {"name": "devfile", "version": "0.4.7", "platform": "arm64-darwin", "checksum": "16627fd15a6f004d3353822cdc9b6f54eff7c7ad4039e465072e2ab43337f663"}, {"name": "devfile", "version": "0.4.7", "platform": "ruby", "checksum": "7e7d7990a418ed3e36c364e49f52dbda43b6a240952f2e36e9fd643d8cd606d8"}, {"name": "devfile", "version": "0.4.7", "platform": "x86_64-linux", "checksum": "cbec7cd08871f61766a5fb9a34a9743f2a1a8c8234bd04a35a995d8fa7d7b70b"}, {"name": "device_detector", "version": "1.1.3", "platform": "ruby", "checksum": "c5fe3fe42cab2e8aa01f193b2074b8bb1510373ce47127206f28c7dea75a9c79"}, {"name": "devise", "version": "4.9.4", "platform": "ruby", "checksum": "920042fe5e704c548aa4eb65ebdd65980b83ffae67feb32c697206bfd975a7f8"}, {"name": "devise-two-factor", "version": "4.1.1", "platform": "ruby", "checksum": "c95f5b07533e62217aaed3c386874d94e2d472fb5f2b6598afe8600fc17a8b95"}, {"name": "diff-lcs", "version": "1.5.0", "platform": "ruby", "checksum": "49b934001c8c6aedb37ba19daec5c634da27b318a7a3c654ae979d6ba1929b67"}, {"name": "diffy", "version": "3.4.4", "platform": "ruby", "checksum": "79384ab5ca82d0e115b2771f0961e27c164c456074bd2ec46b637ebf7b6e47e3"}, {"name": "digest-crc", "version": "0.6.5", "platform": "ruby", "checksum": "5ca456f3352dc5ff17eb95deb3dd5a79dc79f8bf751d8005abca5b7b9b252124"}, {"name": "docile", "version": "1.4.0", "platform": "ruby", "checksum": "5f1734bde23721245c20c3d723e76c104208e1aa01277a69901ce770f0ebb8d3"}, {"name": "domain_name", "version": "0.5.20190701", "platform": "ruby", "checksum": "000a600454cb4a344769b2f10b531765ea7bd3a304fe47ed12e5ca1eab969851"}, {"name": "doorkeeper", "version": "5.8.2", "platform": "ruby", "checksum": "a73d07aeaf590b1e7e2a35390446f23131c9f37bc0561653e514d3973f4d50d3"}, {"name": "doorkeeper-device_authorization_grant", "version": "1.0.3", "platform": "ruby", "checksum": "94c3ac12a0d50942850ecd58ed64298b397a5e903e8880cb68d4085600932679"}, {"name": "doorkeeper-openid_connect", "version": "1.8.11", "platform": "ruby", "checksum": "52a9a9c03176f5fa54d04b8f5378902beff126e3423fa447288b168276b7f6d3"}, {"name": "dotenv", "version": "2.7.6", "platform": "ruby", "checksum": "2451ed5e8e43776d7a787e51d6f8903b98e446146c7ad143d5678cc2c409d547"}, {"name": "drb", "version": "2.2.3", "platform": "ruby", "checksum": "0b00d6fdb50995fe4a45dea13663493c841112e4068656854646f418fda13373"}, {"name": "dry-cli", "version": "1.0.0", "platform": "ruby", "checksum": "28ead169f872954dd08910eb8ead59cf86cd18b4aab321e8eeefe945749569f0"}, {"name": "dry-core", "version": "1.0.1", "platform": "ruby", "checksum": "f32f4245e0f54e787f3708584ed8f7545aaf8dd99072e36f169312468ec5450d"}, {"name": "dry-inflector", "version": "1.0.0", "platform": "ruby", "checksum": "6ad22361ca2d6f3f001ae3037ffcfea01163f644280d13a9195d3c3a94dd1626"}, {"name": "dry-logic", "version": "1.5.0", "platform": "ruby", "checksum": "99ed2180f1970c3d8247004f277a01dffbe8e82cf6680de9c7209312d86cd416"}, {"name": "dry-types", "version": "1.7.1", "platform": "ruby", "checksum": "12165841145a18dd22151f143707b90c8093f71e5ae06ee0f2301f5321f8cdb8"}, {"name": "dumb_delegator", "version": "1.0.0", "platform": "ruby", "checksum": "ff5e411816d2d8ad8e260b269e712ae3839dddb0f9f8e18d3b1a3fe08f6d2e94"}, {"name": "duo_api", "version": "1.4.0", "platform": "ruby", "checksum": "06a6b406184e6e4b14af7389ac3990e667fb8509a1feba7de3af2f78d98c0877"}, {"name": "ed25519", "version": "1.4.0", "platform": "java", "checksum": "10410f0538bc35eead91fa82bc4be48c0a3593573cd7f03d941ce1c0b7129bfd"}, {"name": "ed25519", "version": "1.4.0", "platform": "ruby", "checksum": "16e97f5198689a154247169f3453ef4cfd3f7a47481fde0ae33206cdfdcac506"}, {"name": "elasticsearch", "version": "7.17.11", "platform": "ruby", "checksum": "ed080f085d939f21d07f424ebcea95326e4bdb5f770a8f33aac699374f2ffc86"}, {"name": "elasticsearch-api", "version": "7.17.11", "platform": "ruby", "checksum": "fed8f7b64493c97cf3984a33396a798204b54b8e1b01c5b6c099fa3fd4209107"}, {"name": "elasticsearch-model", "version": "7.2.1", "platform": "ruby", "checksum": "8b5c4b57664bb29f4854fa39603b5ccecfbf9b22fee87bcd16917321dae6a20b"}, {"name": "elasticsearch-rails", "version": "7.2.1", "platform": "ruby", "checksum": "0750dc0e956358d9a3a0912a8186c266ef19f8de0b178c61996ed1a6998156e4"}, {"name": "elasticsearch-transport", "version": "7.17.11", "platform": "ruby", "checksum": "d18057d5295e4c39fe80084ede9e00e9c0e0d74580348985f8677b2fb7f70f03"}, {"name": "email_reply_trimmer", "version": "0.1.6", "platform": "ruby", "checksum": "9fede222ce660993e4e2e3dad282535ceb7914e246eb8302c19aa9e021f7326e"}, {"name": "email_spec", "version": "2.3.0", "platform": "ruby", "checksum": "df23be7a131186f7a3d5be3b35eaac9196f9ac13bd26c9c3d59341e13d852d11"}, {"name": "email_validator", "version": "2.2.4", "platform": "ruby", "checksum": "5ab238095bec7aef9389f230e9e0c64c5081cdf91f19d6c5cecee0a93af20604"}, {"name": "encryptor", "version": "3.0.0", "platform": "ruby", "checksum": "abf23f94ab4d864b8cea85b43f3432044a60001982cda7c33c1cd90da8db1969"}, {"name": "erubi", "version": "1.12.0", "platform": "ruby", "checksum": "27bedb74dfb1e04ff60674975e182d8ca787f2224f2e8143268c7696f42e4723"}, {"name": "escape_utils", "version": "1.3.0", "platform": "ruby", "checksum": "dffb7010922880ace6ceed642156c64e2a64620f27e0849f43bc4f68fd3c2c09"}, {"name": "et-orbi", "version": "1.2.11", "platform": "ruby", "checksum": "d26e868cc21db88280a9ec1a50aa3da5d267eb9b2037ba7b831d6c2731f5df64"}, {"name": "ethon", "version": "0.16.0", "platform": "ruby", "checksum": "bba0da1cea8ac3e1f5cdd7cb1cb5fc78d7ac562c33736f18f0c3eb2b63053d9e"}, {"name": "excon", "version": "1.3.0", "platform": "ruby", "checksum": "d83d3bc2a46a74f969406071e0acc32971381a01d7382a79fa75529fb28046be"}, {"name": "execjs", "version": "2.8.1", "platform": "ruby", "checksum": "6d939919cfd81bcc4d6556f322c3995a70cfe4289ea0bd3b1f999b489c323088"}, {"name": "expgen", "version": "0.1.1", "platform": "ruby", "checksum": "4e6a0f65b210a201d6045debb3e62a24e33251a49f81a11b067d303a60d3a239"}, {"name": "expression_parser", "version": "0.9.0", "platform": "ruby", "checksum": "2b56db3cffc48c3337f4f29f5bc2374c86e7ba29acb40269c74bb55af9f868a4"}, {"name": "extended-markdown-filter", "version": "0.7.0", "platform": "ruby", "checksum": "c8eeef7409fbae18c6b407cd3e4eeb5d25c35cb08fe1ac06f375df3db2d4f138"}, {"name": "factory_bot", "version": "6.5.0", "platform": "ruby", "checksum": "6374b3a3593b8077ee9856d553d2e84d75b47b912cc24eafea4062f9363d2261"}, {"name": "factory_bot_rails", "version": "6.5.0", "platform": "ruby", "checksum": "4a7b61635424a57cc60412a18b72b9dcfb02fabfce2c930447a01dce8b37c0a2"}, {"name": "faraday", "version": "2.13.4", "platform": "ruby", "checksum": "c719ff52cfd0dbaeca79dd83ed3aeea3f621032abf8bc959d1c05666157cac26"}, {"name": "faraday-follow_redirects", "version": "0.3.0", "platform": "ruby", "checksum": "d92d975635e2c7fe525dd494fcd4b9bb7f0a4a0ec0d5f4c15c729530fdb807f9"}, {"name": "faraday-http-cache", "version": "2.5.0", "platform": "ruby", "checksum": "64b7366d66e508e1c3dd855ebb20ce9da429330e412a23d9ebbc0a7a7b227463"}, {"name": "faraday-multipart", "version": "1.1.1", "platform": "ruby", "checksum": "77a18ff40149030fd1aef55bb4fc7a67ce46419a8a3fcd010e28c2526e8d8903"}, {"name": "faraday-net_http", "version": "3.1.0", "platform": "ruby", "checksum": "1627be414960d0131691190ff524506ba6607402a50fb6eccda9e64ca60f859f"}, {"name": "faraday-net_http_persistent", "version": "2.1.0", "platform": "ruby", "checksum": "b41720b13f56dae77114d9de54baef2d76d0b06ab40d695b2a98e254b56ade0b"}, {"name": "faraday-retry", "version": "2.2.1", "platform": "ruby", "checksum": "4146fed14549c0580bf14591fca419a40717de0dd24f267a8ec2d9a728677608"}, {"name": "faraday-typhoeus", "version": "1.1.0", "platform": "ruby", "checksum": "24c6147c213818dde3ebc50ae47ab92f9a7e554903aa362707126f749c6890e7"}, {"name": "faraday_middleware-aws-sigv4", "version": "1.0.1", "platform": "ruby", "checksum": "a001ea4f687ca1c60bad8f2a627196905ce3dbf285e461dc153240e92eaabe8f"}, {"name": "fast_blank", "version": "1.0.1", "platform": "java", "checksum": "90d82106b0e4aa19ac24ba1604c79a0c5a4c471601e800c9b2b072938a6d9a92"}, {"name": "fast_blank", "version": "1.0.1", "platform": "ruby", "checksum": "269fc30414fed4e6403bc4a49081e1ea539f8b9226e59276ed1efaefabaa17ea"}, {"name": "fast_gettext", "version": "4.1.0", "platform": "ruby", "checksum": "8e6b612676d601209662d2cd793ed4a067f834c8ca65ede793bacc9bcc1c2763"}, {"name": "ffaker", "version": "2.24.0", "platform": "ruby", "checksum": "43e3f8c12d602fbc70398be8bd1ac841a031eabdc12af01507e76a8d675a5d52"}, {"name": "ffi", "version": "1.17.2", "platform": "aarch64-linux-gnu", "checksum": "c910bd3cae70b76690418cce4572b7f6c208d271f323d692a067d59116211a1a"}, {"name": "ffi", "version": "1.17.2", "platform": "aarch64-linux-musl", "checksum": "69e6556b091d45df83e6c3b19d3c54177c206910965155a6ec98de5e893c7b7c"}, {"name": "ffi", "version": "1.17.2", "platform": "arm-linux-gnu", "checksum": "d4a438f2b40224ae42ec72f293b3ebe0ba2159f7d1bd47f8417e6af2f68dbaa5"}, {"name": "ffi", "version": "1.17.2", "platform": "arm-linux-musl", "checksum": "977dfb7f3a6381206dbda9bc441d9e1f9366bf189a634559c3b7c182c497aaa3"}, {"name": "ffi", "version": "1.17.2", "platform": "arm64-darwin", "checksum": "54dd9789be1d30157782b8de42d8f887a3c3c345293b57ffb6b45b4d1165f813"}, {"name": "ffi", "version": "1.17.2", "platform": "java", "checksum": "94c8516d7c97b21915497b994e41f69e7e8e21d5fc085c498b68e52044e191ec"}, {"name": "ffi", "version": "1.17.2", "platform": "ruby", "checksum": "297235842e5947cc3036ebe64077584bff583cd7a4e94e9a02fdec399ef46da6"}, {"name": "ffi", "version": "1.17.2", "platform": "x64-mingw-ucrt", "checksum": "15d2da54ee578657a333a6059ed16eaba1cbd794ceecd15944825b65c8381ac0"}, {"name": "ffi", "version": "1.17.2", "platform": "x64-mingw32", "checksum": "c67b84b1bd54b680bcc23b516e87c96b4585cad3ca9e0afea953c9a9cb90243d"}, {"name": "ffi", "version": "1.17.2", "platform": "x86-linux-gnu", "checksum": "95d8f9ebea23c39888e2ab85a02c98f54acb2f4e79b829250d7267ce741dc7b0"}, {"name": "ffi", "version": "1.17.2", "platform": "x86-linux-musl", "checksum": "41741449bab2b9530f42a47baa5c26263925306fad0ac2d60887f51af2e3b24c"}, {"name": "ffi", "version": "1.17.2", "platform": "x86-mingw32", "checksum": "5052e800045e95acdcd2c404777d5296751e66553c12ff4bf20f29ddcc9e4139"}, {"name": "ffi", "version": "1.17.2", "platform": "x86_64-da<PERSON><PERSON>", "checksum": "981f2d4e32ea03712beb26e55e972797c2c5a7b0257955d8667ba58f2da6440e"}, {"name": "ffi", "version": "1.17.2", "platform": "x86_64-linux-gnu", "checksum": "05d2026fc9dbb7cfd21a5934559f16293815b7ce0314846fee2ac8efbdb823ea"}, {"name": "ffi", "version": "1.17.2", "platform": "x86_64-linux-musl", "checksum": "97c0eb3981414309285a64dc4d466bd149e981c279a56371ef811395d68cb95c"}, {"name": "ffi-compiler", "version": "1.0.1", "platform": "ruby", "checksum": "019f389b078a2fec9de7f4f65771095f80a447e34436b4588bcb629e2a564c30"}, {"name": "ffi-yajl", "version": "2.6.0", "platform": "ruby", "checksum": "69baa612273991e4c79667464eb25f3feb169899aab33929a33b03234af24336"}, {"name": "ffi-yajl", "version": "2.6.0", "platform": "universal-java", "checksum": "1159a093c51d75d67578e31d2ed429f2481ef7e73021c5d667ebf6ab481a0d21"}, {"name": "fiber-annotation", "version": "0.2.0", "platform": "ruby", "checksum": "7abfadf1d119f508867d4103bf231c0354d019cc39a5738945dec2edadaf6c03"}, {"name": "fiber-local", "version": "1.1.0", "platform": "ruby", "checksum": "c885f94f210fb9b05737de65d511136ea602e00c5105953748aa0f8793489f06"}, {"name": "fiber-storage", "version": "0.1.2", "platform": "ruby", "checksum": "02f72742fd3e5818165b5455b57f5b536cf68908233531cdc6ee894be2c9ae2c"}, {"name": "find_a_port", "version": "1.0.1", "platform": "ruby", "checksum": "605d6a84b5e6f138da2b06c87c5a4a0231e4fdc9b9a92022d9caa361f77d5ceb"}, {"name": "flipper", "version": "0.28.3", "platform": "ruby", "checksum": "fd5c4a649434643358736d84429e903cf9678d1927c3ab14bc7525ed4062f869"}, {"name": "flipper-active_record", "version": "0.28.3", "platform": "ruby", "checksum": "c6a91c265da8062b75cf575708e325b931e248e5f81582fea8c4cdf461ad1920"}, {"name": "flipper-active_support_cache_store", "version": "0.28.3", "platform": "ruby", "checksum": "111ae20579920206f001d4b33a9d96770fd23c709beeb6ca136c9ebc96fbd9fd"}, {"name": "fog-aliyun", "version": "0.4.0", "platform": "ruby", "checksum": "8f2334604beb781eafbb9cd5f50141fbb2c7eb77c7f2b01f45c2e04db0e5cc38"}, {"name": "fog-aws", "version": "3.33.0", "platform": "ruby", "checksum": "ce4bcd58e1d6bde866aefb40f5ec5225687591e0fc572817d4a99b15ef0aa7a2"}, {"name": "fog-core", "version": "2.6.0", "platform": "ruby", "checksum": "3fe08aa83a23cddce42f4ba412040c08f890d7ff04c175c0ee59119371245be6"}, {"name": "fog-google", "version": "1.25.0", "platform": "ruby", "checksum": "58aea3b0abf5938ed6ca2a3b51282cfe7038403e2eeb70bdb0a1b998307ce18f"}, {"name": "fog-json", "version": "1.2.0", "platform": "ruby", "checksum": "dd4f5ab362dbc72b687240bba9d2dd841d5dfe888a285797533f85c03ea548fe"}, {"name": "fog-local", "version": "0.9.0", "platform": "ruby", "checksum": "2bb5ffb153343a2d641018e2571155ebabd11908a7133504ead7ee03586a6a1c"}, {"name": "fog-xml", "version": "0.1.5", "platform": "ruby", "checksum": "52b9fea10701461dd3eaf9d9839702169b418dbbf50426786b9b74fade373bd6"}, {"name": "formatador", "version": "0.2.5", "platform": "ruby", "checksum": "80821869ddacb79e72870ff4bb1531efacd278c04f2df26bc6b4529ee13582bd"}, {"name": "forwardable", "version": "1.3.3", "platform": "ruby", "checksum": "f17df4bd6afa6f46a003217023fe5716ef88ce261f5c4cf0edbdeed6470cafac"}, {"name": "fugit", "version": "1.11.2", "platform": "ruby", "checksum": "4c2e234f750c78d4514d0ca343a0b923847eac3846976fdb23ed4245d8fde6fe"}, {"name": "<PERSON><PERSON><PERSON>", "version": "0.9.0", "platform": "ruby", "checksum": "542efa80f2bcaadbdc402c2f0b572f2e335a1d53e375aecad68bbb3d86860c0f"}, {"name": "gapic-common", "version": "0.20.0", "platform": "ruby", "checksum": "af304704b440f7a2a1e8ce6ecce109a67b79fa173f36f11b513b8a35ce509366"}, {"name": "gdk-toogle", "version": "0.9.5", "platform": "ruby", "checksum": "38b8972576d324c0905e5a2935592c21ec36bedb4bf1e6d195257ee20ebad249"}, {"name": "gemoji", "version": "3.0.1", "platform": "ruby", "checksum": "80553f2f4932a7a95fb1b3c7c63f7dd937e7c8c610164bbdea28fd06eba5f36d"}, {"name": "get_process_mem", "version": "0.2.7", "platform": "ruby", "checksum": "4afd3c3641dd6a817c09806c7d6d509d8a9984512ac38dea8b917426bbf77eba"}, {"name": "gettext", "version": "3.5.1", "platform": "ruby", "checksum": "03ec7f71ea7e2cf1fdcd5e08682e98b81601922fdbee890b7bc6f63b0e1a512a"}, {"name": "gettext_i18n_rails", "version": "1.13.0", "platform": "ruby", "checksum": "d4a4739d928b6ce52a2d694d33a831dcb06c7c8e197b3172fc73dfaa20ac8ee6"}, {"name": "git", "version": "1.19.1", "platform": "ruby", "checksum": "b0a422d9f6517353c48a330d6114de4db9e0c82dbe7202964a1d9f1fbc827d70"}, {"name": "g<PERSON>y", "version": "18.2.1", "platform": "ruby", "checksum": "7409135d7dbc29ec8021e694420963e1da86dcc45252065fce813c19cb5c9bb3"}, {"name": "gitlab", "version": "4.19.0", "platform": "ruby", "checksum": "3f645e3e195dbc24f0834fbf83e8ccfb2056d8e9712b01a640aad418a6949679"}, {"name": "gitlab-chronic", "version": "0.10.6", "platform": "ruby", "checksum": "a244d11a1396d2aac6ae9b2f326adf1605ec1ad20c29f06e8b672047d415a9ac"}, {"name": "gitlab-cloud-connector", "version": "1.29.0", "platform": "ruby", "checksum": "26a48c1936757e1d3c13634d0a1a1cd9733824f9db245f546c29598dadcb089a"}, {"name": "gitlab-crystalball", "version": "1.1.1", "platform": "ruby", "checksum": "0464a113b0809e0e9fa7c0100bb6634fe38465af95aa04efa49541d64250b8ed"}, {"name": "gitlab-danger<PERSON>les", "version": "4.10.0", "platform": "ruby", "checksum": "0adb9cfec58ffce42f68b1aef528503bdc89aed3994ba461c67e1d9246513e1c"}, {"name": "gitlab-experiment", "version": "0.9.1", "platform": "ruby", "checksum": "f230ee742154805a755d5f2539dc44d93cdff08c5bbbb7656018d61f93d01f48"}, {"name": "gitlab-fog-azure-rm", "version": "2.2.0", "platform": "ruby", "checksum": "31aa7c2170f57874053144e7f716ec9e15f32e71ffbd2c56753dce46e2e78ba9"}, {"name": "gitlab-glfm-markdown", "version": "0.0.33", "platform": "aarch64-linux-gnu", "checksum": "7e123252d61aaf23647a768554bc16a5d1bf96e0453fa9a63f89ce40c4a9939e"}, {"name": "gitlab-glfm-markdown", "version": "0.0.33", "platform": "aarch64-linux-musl", "checksum": "7b74848f3d4d54c97e30e8067af73463d053e407361e2f57f21506944ebdec84"}, {"name": "gitlab-glfm-markdown", "version": "0.0.33", "platform": "arm64-darwin", "checksum": "fc33ef745bec550af0d0d234166b745ce80cb1bce2869cde9f0b5b3d11e98207"}, {"name": "gitlab-glfm-markdown", "version": "0.0.33", "platform": "ruby", "checksum": "ac5ab6e6f3b7d2e0f6f2a927da2fcd14b73958c15a70dc69a1b9135d14a6215f"}, {"name": "gitlab-glfm-markdown", "version": "0.0.33", "platform": "x86_64-da<PERSON><PERSON>", "checksum": "fda1b69e95ce2e2e5efeba42ffa34eb2617baeed21614bb18f2c2ef27e1dd728"}, {"name": "gitlab-glfm-markdown", "version": "0.0.33", "platform": "x86_64-linux-gnu", "checksum": "90843d124994481c3f91011d726a1632ada08aa65a9a4d6c4ae84351354ec710"}, {"name": "gitlab-glfm-markdown", "version": "0.0.33", "platform": "x86_64-linux-musl", "checksum": "563e5eb0d1ab2f3ab1b2422e33fb131b506179517e9a3dd9dced0545a23e3e4f"}, {"name": "gitlab-kas-grpc", "version": "18.2.1", "platform": "ruby", "checksum": "971519aaaf9b34097c9a919312361c9686997fab22e09cfb50d951ed5ab245e2"}, {"name": "gitlab-labkit", "version": "0.40.0", "platform": "ruby", "checksum": "cf5f83cf80fcdd2734e08d9deaa51543f001ea7c45eeff7d303ef8b456598c2b"}, {"name": "gitlab-license", "version": "2.6.0", "platform": "ruby", "checksum": "2c1f8ae73835640ec77bf758c1d0c9730635043c01cf77902f7976e826d7d016"}, {"name": "gitlab-mail_room", "version": "0.0.27", "platform": "ruby", "checksum": "05c07db892094cf5747ea00afb0a95c5a5406e05f34ae779f4388f2ddf962316"}, {"name": "gitlab-markup", "version": "2.0.0", "platform": "ruby", "checksum": "951a1c871463a8f329e6c002b2da337cd547febcc1e33d84df4a212419fba02e"}, {"name": "gitlab-net-dns", "version": "0.15.0", "platform": "ruby", "checksum": "d229aae205055b86b2ad166981257eb589ce6d6a146aa79b3ea2b1e5d9741f46"}, {"name": "gitlab-sdk", "version": "0.3.1", "platform": "ruby", "checksum": "48ba49084f4ab92df7c7ef9f347020d9dfdf6ed9c1e782b67264e98ffe6ea710"}, {"name": "gitlab-secret_detection", "version": "0.33.3", "platform": "ruby", "checksum": "c8e15454683a5c51c6221972094472094e11a3c690f91943e1f827332e72fc40"}, {"name": "gitlab-security_report_schemas", "version": "0.1.3.min15.0.0.max15.2.3", "platform": "ruby", "checksum": "3c744a2cddb4ef22ce525a987fae5e3d364cca0f8aa84710ec07a200970217bd"}, {"name": "gitlab-styles", "version": "13.1.0", "platform": "ruby", "checksum": "46c7c5729616355868b7b40a4ffcd052b36346076042abe8cafaee1688cbf2c1"}, {"name": "gitlab_chronic_duration", "version": "0.12.0", "platform": "ruby", "checksum": "0d766944d415b5c831f176871ee8625783fc0c5bfbef2d79a3a616f207ffc16d"}, {"name": "gitlab_omniauth-ldap", "version": "2.3.0", "platform": "ruby", "checksum": "167036fe37c2711f2e1d2047260766e4c9b31ac37dfc873b101bcd4ea2a3a3b4"}, {"name": "gitlab_quality-test_tooling", "version": "2.20.0", "platform": "ruby", "checksum": "2c49c41e9815cb4e065646688b9a2dc066c5cac17ee35b391bc68c40f30bb1f7"}, {"name": "globalid", "version": "1.1.0", "platform": "ruby", "checksum": "b337e1746f0c8cb0a6c918234b03a1ddeb4966206ce288fbb57779f59b2d154f"}, {"name": "gon", "version": "6.5.0", "platform": "ruby", "checksum": "2226e3c921f26bde69b4586660bb67e3252b3a8a3caaa955a77212188a5d81ab"}, {"name": "google-apis-androidpublisher_v3", "version": "0.85.0", "platform": "ruby", "checksum": "122cee4d1c9e0d54e2b40f1211cd25f4e70a8d4565f30305ed77b9e1cace2536"}, {"name": "google-apis-bigquery_v2", "version": "0.90.0", "platform": "ruby", "checksum": "8b622912b45fb7ab75f0600a062db17b7d25a057265366750512e574ec333d7b"}, {"name": "google-apis-cloudbilling_v1", "version": "0.22.0", "platform": "ruby", "checksum": "db2b72aebdc2664fd5095264a160cf757119ba3a83a036817b78d0d2ad7886fd"}, {"name": "google-apis-cloudresourcemanager_v1", "version": "0.31.0", "platform": "ruby", "checksum": "f0a472a228c0b9b592741380ce79ead2458ea0066a4b5a78635818b9b62efbbf"}, {"name": "google-apis-compute_v1", "version": "0.129.0", "platform": "ruby", "checksum": "b767d4564519fc47fc86b10159ec27ad515292e92b979b10720b02fd3b06f5d3"}, {"name": "google-apis-container_v1", "version": "0.100.0", "platform": "ruby", "checksum": "5a5ae56036e384830263de6d48ed9b9f9ef26818b28b6b027206432e718d1853"}, {"name": "google-apis-container_v1beta1", "version": "0.89.0", "platform": "ruby", "checksum": "459acd4ba57e2e7789d0564531134a39b8cab08bbbc55b4a376379eb9d89908c"}, {"name": "google-apis-core", "version": "0.18.0", "platform": "ruby", "checksum": "96b057816feeeab448139ed5b5c78eab7fc2a9d8958f0fbc8217dedffad054ee"}, {"name": "google-apis-dns_v1", "version": "0.36.0", "platform": "ruby", "checksum": "5dd273d78ab37d03d1bc07837186f79ad0399e9f2b8b1ec2629ed682ea347d47"}, {"name": "google-apis-iam_v1", "version": "0.73.0", "platform": "ruby", "checksum": "6f181165f161dd4d53e98c412d345d262114b2e26ef790d57a754f1fcf436a49"}, {"name": "google-apis-iamcredentials_v1", "version": "0.15.0", "platform": "ruby", "checksum": "e9a256a6d80fbfc77d44bd7e65bc94b9e1e9863a00e6d413edc0102d6cb5551b"}, {"name": "google-apis-monitoring_v3", "version": "0.54.0", "platform": "ruby", "checksum": "677fe1dce5b4cc937813303b020962fffb86f50a1f61f6422516937b5ad46128"}, {"name": "google-apis-pubsub_v1", "version": "0.45.0", "platform": "ruby", "checksum": "1dfe4614c781250a0d4491be43e134936d5c08adc75a843e27d4bb66ba3cb205"}, {"name": "google-apis-serviceusage_v1", "version": "0.28.0", "platform": "ruby", "checksum": "5f0b7e023647e7da07f6bce6ada0a6b1aafdb545a1ae985dbac921b76d11b062"}, {"name": "google-apis-sqladmin_v1beta4", "version": "0.41.0", "platform": "ruby", "checksum": "551553b6481879f1cd39fb83cc2a2c2ea9334afc4bf261b96900dd559f96749d"}, {"name": "google-apis-storage_v1", "version": "0.29.0", "platform": "ruby", "checksum": "9dab410ab4671ed8ebf736d7eb4f4641fa072ad32a23d34c252c10d71f1683cc"}, {"name": "google-cloud-artifact_registry-v1", "version": "0.11.0", "platform": "ruby", "checksum": "ba80d2dce9767e663931ded7929b7f8bf5983a6e2ea68078e27e7ca9a940783e"}, {"name": "google-cloud-big<PERSON>y", "version": "1.52.1", "platform": "ruby", "checksum": "20635badc4c7fa2dca3aab38ad03d95ddf7eb2c9e996c0e63cd94fe561e7fa13"}, {"name": "google-cloud-common", "version": "1.1.0", "platform": "ruby", "checksum": "738db08fd144b4fe37b4578ffd63308b64a86fd59f6979d240048f917a6fb5fb"}, {"name": "google-cloud-compute-v1", "version": "2.6.0", "platform": "ruby", "checksum": "b96059b33ffc2f25644d20161a0c1aa1331197073c2e44786b18f8b670f1141e"}, {"name": "google-cloud-core", "version": "1.7.0", "platform": "ruby", "checksum": "748028a48530ea5bce159722eb7a02cd0562f1c52f0569e9ed69da3cba6b4f35"}, {"name": "google-cloud-env", "version": "2.2.1", "platform": "ruby", "checksum": "3c6062aee0b5c863b83f3ce125ea7831507aadf1af7c0d384b74a116c4f649cf"}, {"name": "google-cloud-errors", "version": "1.3.0", "platform": "ruby", "checksum": "450b681e24c089a20721a01acc4408bb4a7b0df28c175aaab488da917480d64b"}, {"name": "google-cloud-location", "version": "0.6.0", "platform": "ruby", "checksum": "386c99ca156e5cac413731c055d7d9c55629860129ad7658a2bf39ea5004d2d0"}, {"name": "google-cloud-storage", "version": "1.45.0", "platform": "ruby", "checksum": "f280abda4e608f9e91433f9dd907be4a45cdbf251ffeb275d713548e515c6300"}, {"name": "google-cloud-storage_transfer", "version": "1.2.0", "platform": "ruby", "checksum": "132901f50889e02a0d378e6117c6408cbfc4fdbd15c9d31fabec4f4189ef1658"}, {"name": "google-cloud-storage_transfer-v1", "version": "0.8.0", "platform": "ruby", "checksum": "9dbef80275db556e046bb24139ca6559affe641d1e38b2537b8caaf2f8896176"}, {"name": "google-logging-utils", "version": "0.1.0", "platform": "ruby", "checksum": "70950b1e49314273cf2e167adb47b62af7917a4691b580da7e9be67b9205fcd5"}, {"name": "google-protobuf", "version": "3.25.8", "platform": "aarch64-linux", "checksum": "5869d1a31f39ee3361e85f3ef3db0512c19f0e0c75cd69d7303c177e17590044"}, {"name": "google-protobuf", "version": "3.25.8", "platform": "arm64-darwin", "checksum": "e294affc4fb25c8bc7edd264f0ba490d42dce3afff08db1e08fb7bb44cc57488"}, {"name": "google-protobuf", "version": "3.25.8", "platform": "java", "checksum": "1b8dd949116795653347f95d7975ce2897de2adf721647c10bf54d30ab87fd1e"}, {"name": "google-protobuf", "version": "3.25.8", "platform": "ruby", "checksum": "102c8500502e224a5daa7447bdd2c458a25a6c7b0bf5d8496a559ada131952b7"}, {"name": "google-protobuf", "version": "3.25.8", "platform": "x64-mingw-ucrt", "checksum": "e99e7a19c958235e4a4f1407ec7af3b420542ee50bb1f46a9c5ed1cc2e4068b5"}, {"name": "google-protobuf", "version": "3.25.8", "platform": "x64-mingw32", "checksum": "9703ed3454f9bea1fa733b3a670006e430e1418c7ddf0e9874636e77f0ddf009"}, {"name": "google-protobuf", "version": "3.25.8", "platform": "x86-linux", "checksum": "288ffaaec53c9f666582cd0a5d42395fea6b7c15458c1f9d64a295ca174c0233"}, {"name": "google-protobuf", "version": "3.25.8", "platform": "x86-mingw32", "checksum": "e322e20487a037760937df9086c9720e60eb050ace7eb66cedd99c2fc2e96072"}, {"name": "google-protobuf", "version": "3.25.8", "platform": "x86_64-da<PERSON><PERSON>", "checksum": "9c9e8c1c760eeb314fd6bcba1b0fb1e64c473fc9e2040c8992df61874974c108"}, {"name": "google-protobuf", "version": "3.25.8", "platform": "x86_64-linux", "checksum": "07783d910635e2a60eaf929fe3e45ea4d657d023be3816bda99a21c14f7be959"}, {"name": "googleapis-common-protos", "version": "1.4.0", "platform": "ruby", "checksum": "da2380fb5ab1563580816c74e8d684ac17512c3654c829a3ee84f6d6139de382"}, {"name": "googleapis-common-protos-types", "version": "1.20.0", "platform": "ruby", "checksum": "5e374b06bcfc7e13556e7c0d87b99f1fa3d42de6396a1de3d8fc13aefb4dd07f"}, {"name": "googleauth", "version": "1.14.0", "platform": "ruby", "checksum": "62e7de11791890c3d3dc70582dfd9ab5516530e4e4f56d96451fd62c76475149"}, {"name": "gpgme", "version": "2.0.25", "platform": "ruby", "checksum": "9242408b28720513145deb6150f25f5fe5149f3728ebaea635050cc3fc84dc34"}, {"name": "grape", "version": "2.0.0", "platform": "ruby", "checksum": "3aeff94c17e84ccead4ff98833df691e7da0c108878cc128ca31f80c1047494a"}, {"name": "grape-entity", "version": "1.0.1", "platform": "ruby", "checksum": "e00f9e94e407aff77aa2945d741f544d07e48501927942988799913151d02634"}, {"name": "grape-path-helpers", "version": "2.0.1", "platform": "ruby", "checksum": "ad5216e52c6e796738a9118087352ab4c962900dbad1d8f8c0f96e093c6702d7"}, {"name": "grape-swagger", "version": "2.1.2", "platform": "ruby", "checksum": "8ad7bd53c8baee704575808875dba8c08d269c457db3cf8f1b8a2a1dbf827294"}, {"name": "grape-swagger-entity", "version": "0.5.5", "platform": "ruby", "checksum": "a2a0eb28964b1a56775a3571358a9f0a300b703dbaee1ee535adb2a7bed7ece6"}, {"name": "grape_logging", "version": "1.8.4", "platform": "ruby", "checksum": "efcc3e322dbd5d620a68f078733b7db043cf12680144cd03c982f14115c792d1"}, {"name": "graphlyte", "version": "1.0.0", "platform": "ruby", "checksum": "b5af4ab67dde6e961f00ea1c18f159f73b52ed11395bb4ece297fe628fa1804d"}, {"name": "graphql", "version": "2.5.11", "platform": "ruby", "checksum": "1169ffc6e215fd4d60056455b672c40a0cafa0607262049c2cca343b0f6bdb5c"}, {"name": "graphql-docs", "version": "5.2.0", "platform": "ruby", "checksum": "44d41724529f531adf9265ded7478b74b0c4b927cddc8b9f114337a73f32de08"}, {"name": "grpc", "version": "1.74.1", "platform": "aarch64-linux-gnu", "checksum": "5c9eaeff04ea0abd45ab92ebcc3b70a0a24f36faa4d8ca585ff5939a77856e82"}, {"name": "grpc", "version": "1.74.1", "platform": "aarch64-linux-musl", "checksum": "ddb14a8911fe63ea0514664c11e3f122233d746e71dcc5955ae1dd1af697af60"}, {"name": "grpc", "version": "1.74.1", "platform": "arm64-darwin", "checksum": "82506da28f9d97cdfddde34d566af528bf15157daaa2946cbefb1be8743f8375"}, {"name": "grpc", "version": "1.74.1", "platform": "ruby", "checksum": "e73a3a4996c14f4016c75f0b577b937535341a4095f7f9db151a82a49e371e8b"}, {"name": "grpc", "version": "1.74.1", "platform": "x64-mingw-ucrt", "checksum": "9d51afe0ed769f5d098dabbfc51505c5229705f5814a0bc781915eeedbef9ab7"}, {"name": "grpc", "version": "1.74.1", "platform": "x86-linux-gnu", "checksum": "a702fa12d9a558096dac575276c2195e3ad5f822b105d7aa5a817f18687cde33"}, {"name": "grpc", "version": "1.74.1", "platform": "x86-linux-musl", "checksum": "f26774c02a1e7eb0ebddecd59ffd623db00f5cea810eb9ff9d693e11b01734ac"}, {"name": "grpc", "version": "1.74.1", "platform": "x86-mingw32", "checksum": "603fe76b5152eb61ae093c151588227291d72575b56fb40b74d9a2f77a8d1774"}, {"name": "grpc", "version": "1.74.1", "platform": "x86_64-da<PERSON><PERSON>", "checksum": "b7b31e4d4a268bd0818da54bbabbfa2dfae8d903f092d4dcbda9e24bd014fc95"}, {"name": "grpc", "version": "1.74.1", "platform": "x86_64-linux-gnu", "checksum": "44741365c7d3c4cadb4431186ac73d896a7ac9d03fa7d76564ae1423d325784a"}, {"name": "grpc", "version": "1.74.1", "platform": "x86_64-linux-musl", "checksum": "832f204b85b3cb0fadfd709aa4ca2b451b2ba6858838a72cc55177c47bb31644"}, {"name": "grpc-google-iam-v1", "version": "1.5.0", "platform": "ruby", "checksum": "cea356d150dac69751f6a4c71f1571c8022c69d9f4ce9c18139200932c19374e"}, {"name": "grpc_reflection", "version": "0.1.1", "platform": "ruby", "checksum": "bc47df12f794a407633b5a9eb27fd95118a78d701c325256fff3c9e50819097b"}, {"name": "gssapi", "version": "1.3.1", "platform": "ruby", "checksum": "c51cf30842ee39bd93ce7fc33e20405ff8a04cda9dec6092071b61258284aee1"}, {"name": "guard", "version": "2.16.2", "platform": "ruby", "checksum": "71ba7abaddecc8be91ab77bbaf78f767246603652ebbc7b976fda497ebdc8fbb"}, {"name": "guard-compat", "version": "1.2.1", "platform": "ruby", "checksum": "3ad21ab0070107f92edfd82610b5cdc2fb8e368851e72362ada9703443d646fe"}, {"name": "guard-rsp<PERSON>", "version": "4.7.3", "platform": "ruby", "checksum": "a47ba03cbd1e3c71e6ae8645cea97e203098a248aede507461a43e906e2f75ca"}, {"name": "haml", "version": "5.2.2", "platform": "ruby", "checksum": "6e759246556145642ef832d670fc06f9bd8539159a0e600847a00291dd7aae0c"}, {"name": "haml_lint", "version": "0.64.0", "platform": "ruby", "checksum": "0b9738a9fff57bf7478c72b34d7f4b45d0721239270625939b7cda88c28273bf"}, {"name": "hamlit", "version": "3.0.3", "platform": "java", "checksum": "a34ec87be4ec7525ffc50e8ae7b3b3140a3906eb10bae2ee26eba97f4d7e9089"}, {"name": "hamlit", "version": "3.0.3", "platform": "ruby", "checksum": "5beafd7834a0f99fd3c041a7dfd3cfa3688159bddc905083c1866f2519f5ceea"}, {"name": "hana", "version": "1.3.7", "platform": "ruby", "checksum": "5425db42d651fea08859811c29d20446f16af196308162894db208cac5ce9b0d"}, {"name": "<PERSON><PERSON><PERSON>", "version": "1.2.0", "platform": "ruby", "checksum": "c984f13e115bfc9953332e8e83bd9d769cfde9944e2d54e07eb9df7b76e140b5"}, {"name": "hashie", "version": "5.0.0", "platform": "ruby", "checksum": "9d6c4e51f2a36d4616cbc8a322d619a162d8f42815a792596039fc95595603da"}, {"name": "health_check", "version": "3.1.0", "platform": "ruby", "checksum": "10146508237dc54ed7e24c292d8ba7fb8f9590cf26c66e325b947438c4103b57"}, {"name": "heapy", "version": "0.2.0", "platform": "ruby", "checksum": "74141e845d61ffc7c1e8bf8b127c8cf94544ec7a1181aec613288682543585ea"}, {"name": "html-pipeline", "version": "2.14.3", "platform": "ruby", "checksum": "8a1d4d7128b2141913387cac0f8ba898bb6812557001acc0c2b46910f59413a0"}, {"name": "html2text", "version": "0.4.0", "platform": "ruby", "checksum": "b1becfa0b9150739633f7dc6d8637a49d7e38c3223bcb3afa3cebf59960afdc5"}, {"name": "htmlbeautifier", "version": "1.4.2", "platform": "ruby", "checksum": "9de0c98480fe80d795ed5734a11f183563cd969686f25a04609c0f5a446fa5f8"}, {"name": "htmlentities", "version": "4.3.4", "platform": "ruby", "checksum": "125a73c6c9f2d1b62100b7c3c401e3624441b663762afa7fe428476435a673da"}, {"name": "http", "version": "5.1.1", "platform": "ruby", "checksum": "fcaec14a4f82de6d2f9cb978c07326814c6c2b42b8974f6ec166ff19c645ebaf"}, {"name": "http-accept", "version": "1.7.0", "platform": "ruby", "checksum": "c626860682bfbb3b46462f8c39cd470fd7b0584f61b3cc9df5b2e9eb9972a126"}, {"name": "http-cookie", "version": "1.0.5", "platform": "ruby", "checksum": "73756d46c7dbdc7023deecdb8a171348ea95a1b99810b31cfe8b4fb4e9a6318f"}, {"name": "http-form_data", "version": "2.3.0", "platform": "ruby", "checksum": "cc4eeb1361d9876821e31d7b1cf0b68f1cf874b201d27903480479d86448a5f3"}, {"name": "httparty", "version": "0.23.1", "platform": "ruby", "checksum": "3ac1dd62f2010f6ece551716f5ceec2b2012011d89f1751917ab7f724e966b55"}, {"name": "httpclient", "version": "2.8.3", "platform": "ruby", "checksum": "2951e4991214464c3e92107e46438527d23048e634f3aee91c719e0bdfaebda6"}, {"name": "i18n", "version": "1.14.4", "platform": "ruby", "checksum": "c7deedead0866ea9102975a4eab7968f53de50793a0c211a37808f75dd187551"}, {"name": "i18n_data", "version": "0.13.1", "platform": "ruby", "checksum": "e5aa99b09a69b463bb0443fc1f9540351a49f3d1541c5e91316bafa035c63f66"}, {"name": "icalendar", "version": "2.10.3", "platform": "ruby", "checksum": "0ebfc2672f9fa77b86b4d8c0e25e9b2319aad45a33319fed06d0be8ddd0cd485"}, {"name": "ice_cube", "version": "0.16.4", "platform": "ruby", "checksum": "da117e5de24bdc33931be629f9b55048641924442c7e9b72fedc05e5592531b7"}, {"name": "ice_nine", "version": "0.11.2", "platform": "ruby", "checksum": "5d506a7d2723d5592dc121b9928e4931742730131f22a1a37649df1c1e2e63db"}, {"name": "imagen", "version": "0.2.0", "platform": "ruby", "checksum": "369fe912078877dba92615ebfc6f35a7d833e31f24f47bdd3ad5371a4139e24b"}, {"name": "influxdb-client", "version": "3.2.0", "platform": "ruby", "checksum": "dc1e8ec80542f64c9f31af6d9bfa4c147474bf32b9179a7f0cab970793b8e1f2"}, {"name": "invisible_captcha", "version": "2.3.0", "platform": "ruby", "checksum": "309ee5a5e891ecfb732c85b12f1aa9252a648df6f2761b3b41205e824e30ff15"}, {"name": "io-console", "version": "0.8.0", "platform": "java", "checksum": "3cc6fd5c66e587145c1fdf8dc40c2e3d851e90722a5d0cc3f38da352f06fe1bd"}, {"name": "io-console", "version": "0.8.0", "platform": "ruby", "checksum": "cd6a9facbc69871d69b2cb8b926fc6ea7ef06f06e505e81a64f14a470fddefa2"}, {"name": "io-event", "version": "1.12.1", "platform": "ruby", "checksum": "3bca6df9ec535835a97733c1d93d28a194b2266c40b501d870f314d7a6cbc3cf"}, {"name": "ipaddress", "version": "0.8.3", "platform": "ruby", "checksum": "85640c4f9194c26937afc8c78e3074a8e7c97d5d1210358d1440f01034d006f5"}, {"name": "irb", "version": "1.15.1", "platform": "ruby", "checksum": "d9bca745ac4207a8b728a52b98b766ca909b86ff1a504bcde3d6f8c84faae890"}, {"name": "jaeger-client", "version": "1.1.0", "platform": "ruby", "checksum": "cb5e9b9bbee6ee8d6a82d03d947a5b04543d8c0a949c22e484254f18d8a458a8"}, {"name": "jaro_winkler", "version": "1.6.1", "platform": "java", "checksum": "e4f64bc73edbd6210861be99691d890cddb34d77b97d0615995c06bc26ee6cdb"}, {"name": "jaro_winkler", "version": "1.6.1", "platform": "ruby", "checksum": "c056b61bbf7f1fc0151bde7c8f589a2d666d42d0cdb889395b9b73b328e1b393"}, {"name": "jira-ruby", "version": "2.3.0", "platform": "ruby", "checksum": "abf26e6bff4a8ea40bae06f7df6276a5776905c63fb2070934823ca54f62eb62"}, {"name": "jmespath", "version": "1.6.2", "platform": "ruby", "checksum": "238d774a58723d6c090494c8879b5e9918c19485f7e840f2c1c7532cf84ebcb1"}, {"name": "js_regex", "version": "3.13.0", "platform": "ruby", "checksum": "bda9e25eebd0b48c0e927c611be0be8c5ae0a7d4491ebdb3d1c94413588c1901"}, {"name": "json", "version": "2.13.1", "platform": "java", "checksum": "1b46467e40628654bddd540490e8f5f5a113cdd1565ae86f14c19f6a5225c5ff"}, {"name": "json", "version": "2.13.1", "platform": "ruby", "checksum": "8e7cf4bb44a789ac5fc62869c7187986251c3172d9bdfa60b92c63d6ecab0ffc"}, {"name": "json-jwt", "version": "1.16.6", "platform": "ruby", "checksum": "ab451f9cd8743cecc4137f4170806046c1d8a6d4ee6e8570e0b5c958409b266c"}, {"name": "json-schema", "version": "5.2.2", "platform": "ruby", "checksum": "60beae0ed79ca9c552854c9ebfd44f50f77bd0c3144526d46afec384509940d5"}, {"name": "json_schemer", "version": "2.3.0", "platform": "ruby", "checksum": "9f1fa173b859ca520f15e9e8d08b0892ffca80b78dd8221feb3e360ff4cdeb35"}, {"name": "jsonb_accessor", "version": "1.4", "platform": "java", "checksum": "2c5590d33d89c7b929d5cf38ae3d2c52658bf6f84f03b06ede5c88e9d76f3451"}, {"name": "jsonb_accessor", "version": "1.4", "platform": "ruby", "checksum": "010e087cb843e76b6a624d68af918a8efd6b0ff2ae25b6f0acebdfea45f776ab"}, {"name": "jsonpath", "version": "1.1.2", "platform": "ruby", "checksum": "6804124c244d04418218acb85b15c7caa79c592d7d6970195300428458946d3a"}, {"name": "jwt", "version": "2.10.2", "platform": "ruby", "checksum": "31e1ee46f7359883d5e622446969fe9c118c3da87a0b1dca765ce269c3a0c4f4"}, {"name": "kaminari", "version": "1.2.2", "platform": "ruby", "checksum": "c4076ff9adccc6109408333f87b5c4abbda5e39dc464bd4c66d06d9f73442a3e"}, {"name": "kaminari-actionview", "version": "1.2.2", "platform": "ruby", "checksum": "1330f6fc8b59a4a4ef6a549ff8a224797289ebf7a3a503e8c1652535287cc909"}, {"name": "kaminari-activerecord", "version": "1.2.2", "platform": "ruby", "checksum": "0dd3a67bab356a356f36b3b7236bcb81cef313095365befe8e98057dd2472430"}, {"name": "kaminari-core", "version": "1.2.2", "platform": "ruby", "checksum": "3bd26fec7370645af40ca73b9426a448d09b8a8ba7afa9ba3c3e0d39cdbb83ff"}, {"name": "knapsack", "version": "4.0.0", "platform": "ruby", "checksum": "a9422688751989d09a40b4bf7f959a71a3bfe7bc49d3cd610c2fcfb6e45482b8"}, {"name": "kramdown", "version": "2.5.1", "platform": "ruby", "checksum": "87bbb6abd9d3cebe4fc1f33e367c392b4500e6f8fa19dd61c0972cf4afe7368c"}, {"name": "kramdown-parser-gfm", "version": "1.1.0", "platform": "ruby", "checksum": "fb39745516427d2988543bf01fc4cf0ab1149476382393e0e9c48592f6581729"}, {"name": "kubeclient", "version": "4.12.0", "platform": "ruby", "checksum": "8610b90f8c767303a633b0aafa53d9f61af03f5d9fca96fc0f21380843c309bd"}, {"name": "language_server-protocol", "version": "3.17.0.3", "platform": "ruby", "checksum": "3d5c58c02f44a20d972957a9febe386d7e7468ab3900ce6bd2b563dd910c6b3f"}, {"name": "launchy", "version": "2.5.2", "platform": "ruby", "checksum": "8aa0441655aec5514008e1d04892c2de3ba57bd337afb984568da091121a241b"}, {"name": "lefthook", "version": "1.12.3", "platform": "ruby", "checksum": "e89c29a62727585e659ff612dc1a050dbf2e9bdfaf4a137a60511c57eea90a5f"}, {"name": "letter_opener", "version": "1.10.0", "platform": "ruby", "checksum": "2ff33f2e3b5c3c26d1959be54b395c086ca6d44826e8bf41a14ff96fdf1bdbb2"}, {"name": "letter_opener_web", "version": "3.0.0", "platform": "ruby", "checksum": "3f391efe0e8b9b24becfab5537dfb17a5cf5eb532038f947daab58cb4b749860"}, {"name": "libyajl2", "version": "2.1.0", "platform": "ruby", "checksum": "aa5df6c725776fc050c8418450de0f7c129cb7200b811907c4c0b3b5c0aea0ef"}, {"name": "license_finder", "version": "7.2.1", "platform": "ruby", "checksum": "179ead19b64b170638b72fd16024233813673ac9d20d5ba75ae0b4444887ef14"}, {"name": "licensee", "version": "9.18.0", "platform": "ruby", "checksum": "3e83db984fb7e4e51c98fea0e434138dcb6112f8c26dc7693734a4f8df99df77"}, {"name": "listen", "version": "3.9.0", "platform": "ruby", "checksum": "db9e4424e0e5834480385197c139cb6b0ae0ef28cc13310cfd1ca78377d59c67"}, {"name": "llhttp-ffi", "version": "0.4.0", "platform": "ruby", "checksum": "e5f7327db3cf8007e648342ef76347d6e0ae545a8402e519cca9c886eb37b001"}, {"name": "locale", "version": "2.1.4", "platform": "ruby", "checksum": "522f9973ef3eee64aac9bca06d21db2fba675fa3d2cf61d21f42d1ca18a9f780"}, {"name": "lockbox", "version": "1.4.1", "platform": "ruby", "checksum": "92a5aad33bd3da8ee0ce5b2ddde4c343e98f644e3b0dbc06157661acbc597260"}, {"name": "logger", "version": "1.7.0", "platform": "ruby", "checksum": "196edec7cc44b66cfb40f9755ce11b392f21f7967696af15d274dde7edff0203"}, {"name": "lograge", "version": "0.11.2", "platform": "ruby", "checksum": "4cbd1554b86f545d795eff15a0c24fd25057d2ac4e1caa5fc186168b3da932ef"}, {"name": "loofah", "version": "2.24.1", "platform": "ruby", "checksum": "655a30842b70ec476410b347ab1cd2a5b92da46a19044357bbd9f401b009a337"}, {"name": "lookbook", "version": "2.3.4", "platform": "ruby", "checksum": "16484c9eb514ac0c23c4b59cfd5a52697141d35056e3a9c2a22b314c1b887605"}, {"name": "lru_redux", "version": "1.1.0", "platform": "ruby", "checksum": "ee71d0ccab164c51de146c27b480a68b3631d5b4297b8ffe8eda1c72de87affb"}, {"name": "lumberjack", "version": "1.2.7", "platform": "ruby", "checksum": "a5c6aae6b4234f1420dbcd80b23e3bca0817bd239440dde097ebe3fa63c63b1f"}, {"name": "mail", "version": "2.8.1", "platform": "ruby", "checksum": "ec3b9fadcf2b3755c78785cb17bc9a0ca9ee9857108a64b6f5cfc9c0b5bfc9ad"}, {"name": "marcel", "version": "1.0.4", "platform": "ruby", "checksum": "0d5649feb64b8f19f3d3468b96c680bae9746335d02194270287868a661516a4"}, {"name": "marginalia", "version": "1.11.1", "platform": "ruby", "checksum": "cb63212ab63e42746e27595e912cb20408a1a28bcd0edde55d15b7c45fa289cf"}, {"name": "matrix", "version": "0.4.2", "platform": "ruby", "checksum": "71083ccbd67a14a43bfa78d3e4dc0f4b503b9cc18e5b4b1d686dc0f9ef7c4cc0"}, {"name": "memory_profiler", "version": "1.1.0", "platform": "ruby", "checksum": "79a17df7980a140c83c469785905409d3027ca614c42c086089d128b805aa8f8"}, {"name": "method_source", "version": "1.0.0", "platform": "ruby", "checksum": "d779455a2b5666a079ce58577bfad8534f571af7cec8107f4dce328f0981dede"}, {"name": "metrics", "version": "0.12.1", "platform": "ruby", "checksum": "42ec8eeadb92a57549a72bdd1baf86d4270089bc598917b93cf9cb6f95fcc29c"}, {"name": "mime-types", "version": "3.5.1", "platform": "ruby", "checksum": "85d772fb6cf21f999ac8085998192fb9dd5d16e86ec4c69c5e79ac3003420d61"}, {"name": "mime-types-data", "version": "3.2023.1003", "platform": "ruby", "checksum": "0f7b96d4e54d17752ed78398dca9402359ccaeb391aa0c0e5b305bedaf025b7a"}, {"name": "mini_histogram", "version": "0.3.1", "platform": "ruby", "checksum": "6a114b504e4618b0e076cc672996036870f7cc6f16b8e5c25c0c637726d2dd94"}, {"name": "mini_magick", "version": "4.12.0", "platform": "ruby", "checksum": "67302fa84e63f1002b71416a8466968ed0f33d22f5d42962a0c09a9f1c3a906a"}, {"name": "mini_mime", "version": "1.1.2", "platform": "ruby", "checksum": "a54aec0cc7438a03a850adb00daca2bdb60747f839e28186994df057cea87151"}, {"name": "mini_portile2", "version": "2.8.8", "platform": "ruby", "checksum": "8e47136cdac04ce81750bb6c09733b37895bf06962554e4b4056d78168d70a75"}, {"name": "minitest", "version": "5.11.3", "platform": "ruby", "checksum": "78e18aa2c49c58e9bc53c54a0b900e87ad0a96394e92fbbfa58d3ff860a68f45"}, {"name": "mixlib-cli", "version": "2.1.8", "platform": "ruby", "checksum": "e6f27be34d580f6ed71731ca46b967e57793a627131c1f6e1ed2dad39ea3bdf9"}, {"name": "mixlib-config", "version": "3.0.27", "platform": "ruby", "checksum": "d7748b1898e4f16502afec1de00b5ad65c6de405114b1b0c65ec61b1a9100148"}, {"name": "mixlib-log", "version": "3.2.3", "platform": "ruby", "checksum": "2a1d3fa83522a320edd493827c901b773fb5d185fac7efd81d028d8e1166a768"}, {"name": "mixlib-shellout", "version": "3.2.7", "platform": "ruby", "checksum": "46f6d1f9c77e689a443081c5cac336203343f0f2224db06b80d39ae4cd797c7e"}, {"name": "mixlib-shellout", "version": "3.2.7", "platform": "universal-mingw32", "checksum": "4d7bea07e347cc8de2b4bc22f4d8f84d7bb8165cf900d26b532d0d9fa4928a19"}, {"name": "mixlib-shellout", "version": "3.2.7", "platform": "x64-mingw-ucrt", "checksum": "de01743f678b66c275ea5f40749cde6c056651d1bb6d320711779394d2eec654"}, {"name": "mize", "version": "0.6.1", "platform": "ruby", "checksum": "4031558979ff5426fda24c75a149b4e4c0faf4cacf2fae8938f83866ab94b780"}, {"name": "msgpack", "version": "1.5.4", "platform": "java", "checksum": "05b3bd16a65dddc64c878634b7ecb9cd613569ca3dd6e480d7295626a0a3f562"}, {"name": "msgpack", "version": "1.5.4", "platform": "ruby", "checksum": "a53db320fba40f58c07c5b66ed9fd4d73cbe8eba4cb28fe9e3218444341a4e09"}, {"name": "multi_json", "version": "1.14.1", "platform": "ruby", "checksum": "d971296c0eacea289d31e4a7ab7ac5eda97262c62bbc8c110de4f5e36425c577"}, {"name": "multi_xml", "version": "0.6.0", "platform": "ruby", "checksum": "d24393cf958adb226db884b976b007914a89c53ad88718e25679d7008823ad52"}, {"name": "multipart-post", "version": "2.2.3", "platform": "ruby", "checksum": "462979de2971b8df33c2ee797fd497731617241f9dcd93960cc3caccb2dd13d8"}, {"name": "murmurhash3", "version": "0.1.7", "platform": "ruby", "checksum": "370a2ce2e9ab0711e51554e530b5f63956927a6554a296855f42a1a4a5ed0936"}, {"name": "<PERSON><PERSON>", "version": "3.0.0", "platform": "ruby", "checksum": "6d3569aa3c3b2f048c60626f48d9b2d561cc8d2ef269296943b03da181c08b67"}, {"name": "mustermann-grape", "version": "1.0.2", "platform": "ruby", "checksum": "6f5309d6a338f801f211c644e8c2d3cc2577a8693f9cd51dadfdb29c1260f5fe"}, {"name": "mutex_m", "version": "0.3.0", "platform": "ruby", "checksum": "cfcb04ac16b69c4813777022fdceda24e9f798e48092a2b817eb4c0a782b0751"}, {"name": "nap", "version": "1.1.0", "platform": "ruby", "checksum": "949691660f9d041d75be611bb2a8d2fd559c467537deac241f4097d9b5eea576"}, {"name": "nenv", "version": "0.3.0", "platform": "ruby", "checksum": "d9de6d8fb7072228463bf61843159419c969edb34b3cef51832b516ae7972765"}, {"name": "net-http", "version": "0.6.0", "platform": "ruby", "checksum": "9621b20c137898af9d890556848c93603716cab516dc2c89b01a38b894e259fb"}, {"name": "net-http-persistent", "version": "4.0.5", "platform": "ruby", "checksum": "6e42880b347e650ffeaf679ae59c9d5a6ed8a22cda6e1b959d9c270050aefa8e"}, {"name": "net-imap", "version": "0.5.9", "platform": "ruby", "checksum": "d95905321e1bd9f294ffc7ff8697be218eee1ec96c8504c0960964d0a0be33fc"}, {"name": "net-ldap", "version": "0.17.1", "platform": "ruby", "checksum": "52571b55f9157120833ac1667f2969ce0139251811d0a9b64657c1c135069cf9"}, {"name": "net-ntp", "version": "2.1.3", "platform": "ruby", "checksum": "5bc73f4102bde0d1872bd3b293608ae99d9f5007d744f21919c6a565eda9267d"}, {"name": "net-pop", "version": "0.1.2", "platform": "ruby", "checksum": "848b4e982013c15b2f0382792268763b748cce91c9e91e36b0f27ed26420dff3"}, {"name": "net-protocol", "version": "0.2.2", "platform": "ruby", "checksum": "aa73e0cba6a125369de9837b8d8ef82a61849360eba0521900e2c3713aa162a8"}, {"name": "net-scp", "version": "4.0.0", "platform": "ruby", "checksum": "b32ded0d48c88ce70844a063e4e14efb44a95e51a9e0c0bfb0c54b4313b622ea"}, {"name": "net-smtp", "version": "0.3.3", "platform": "ruby", "checksum": "3d51dcaa981b74aff2d89cbe89de4503bc2d682365ea5176366e950a0d68d5b0"}, {"name": "net-ssh", "version": "7.3.0", "platform": "ruby", "checksum": "172076c4b30ce56fb25a03961b0c4da14e1246426401b0f89cba1a3b54bf3ef0"}, {"name": "netrc", "version": "0.11.0", "platform": "ruby", "checksum": "de1ce33da8c99ab1d97871726cba75151113f117146becbe45aa85cb3dabee3f"}, {"name": "nio4r", "version": "2.7.0", "platform": "java", "checksum": "3f2e515e928ceeef7668e1f64fc3bfef1417a5ec0908d8e69f2c6d486284e04d"}, {"name": "nio4r", "version": "2.7.0", "platform": "ruby", "checksum": "9586a685eca8246d6406e712a525e705d15bb88f709d78fc3f141e864df97276"}, {"name": "nkf", "version": "0.2.0", "platform": "java", "checksum": "3e6f022d1743a863bf05e936c7c2110be07ba1c593ea974df75d89e8bf7cc967"}, {"name": "nkf", "version": "0.2.0", "platform": "ruby", "checksum": "fbc151bda025451f627fafdfcb3f4f13d0b22ae11f58c6d3a2939c76c5f5f126"}, {"name": "no_proxy_fix", "version": "0.1.2", "platform": "ruby", "checksum": "4e9b4c31bb146de7fcf347dc1087bb13ac2039b56d50aa019e61036256abcd00"}, {"name": "<PERSON>kogiri", "version": "1.18.9", "platform": "aarch64-linux-gnu", "checksum": "5bcfdf7aa8d1056a7ad5e52e1adffc64ef53d12d0724fbc6f458a3af1a4b9e32"}, {"name": "<PERSON>kogiri", "version": "1.18.9", "platform": "aarch64-linux-musl", "checksum": "55e9e6ca46c4ad1715e313f407d8481d15be1e3b65d9f8e52ba1c124d01676a7"}, {"name": "<PERSON>kogiri", "version": "1.18.9", "platform": "arm-linux-gnu", "checksum": "fe611ae65880e445a9c0f650d52327db239f3488626df4173c05beafd161d46e"}, {"name": "<PERSON>kogiri", "version": "1.18.9", "platform": "arm-linux-musl", "checksum": "935605e14c0ba17da18d203922440bf6c0676c602659278d855d4622d756a324"}, {"name": "<PERSON>kogiri", "version": "1.18.9", "platform": "arm64-darwin", "checksum": "eea3f1f06463ff6309d3ff5b88033c4948d0da1ab3cc0a3a24f63c4d4a763979"}, {"name": "<PERSON>kogiri", "version": "1.18.9", "platform": "java", "checksum": "1fe5b7aa4a054eda689a969bb4e03999960a6ea806582d327207d687168bceb5"}, {"name": "<PERSON>kogiri", "version": "1.18.9", "platform": "ruby", "checksum": "ac5a7d93fd0e3cef388800b037407890882413feccca79eb0272a2715a82fa33"}, {"name": "<PERSON>kogiri", "version": "1.18.9", "platform": "x64-mingw-ucrt", "checksum": "6b4fc1523aa0370c78653e38c94cb50e7f3ab786425de66ba7ad24222c1164a3"}, {"name": "<PERSON>kogiri", "version": "1.18.9", "platform": "x86_64-da<PERSON><PERSON>", "checksum": "e0d2deb03d3d7af8016e8c9df5ff4a7d692159cefb135cbb6a4109f265652348"}, {"name": "<PERSON>kogiri", "version": "1.18.9", "platform": "x86_64-linux-gnu", "checksum": "b52f5defedc53d14f71eeaaf990da66b077e1918a2e13088b6a96d0230f44360"}, {"name": "<PERSON>kogiri", "version": "1.18.9", "platform": "x86_64-linux-musl", "checksum": "e69359d6240c17e64cc9f43970d54f13bfc7b8cc516b819228f687e953425e69"}, {"name": "notiffany", "version": "0.1.3", "platform": "ruby", "checksum": "d37669605b7f8dcb04e004e6373e2a780b98c776f8eb503ac9578557d7808738"}, {"name": "numerizer", "version": "0.2.0", "platform": "ruby", "checksum": "e58076d5ee5370417b7e52d9cb25836d62acd1b8d9a194c308707986c1705d7b"}, {"name": "o<PERSON>h", "version": "0.5.6", "platform": "ruby", "checksum": "4085fe28e0c5e2434135e00a6555294fd2a4ff96a98d1bdecdcd619fc6368dff"}, {"name": "oauth2", "version": "2.0.10", "platform": "ruby", "checksum": "8f132679598d21885d4bcc68d7e7e6ef0a29f9a782abca00d67d884280dc3a42"}, {"name": "observer", "version": "0.1.2", "platform": "ruby", "checksum": "d8a3107131ba661138d748e7be3dbafc0d82e732fffba9fccb3d7829880950ac"}, {"name": "octokit", "version": "9.2.0", "platform": "ruby", "checksum": "4fa47ff35ce654127edf2c836ab9269bcc8829f5542dc1e86871f697ce7f4316"}, {"name": "ohai", "version": "18.1.18", "platform": "ruby", "checksum": "42ee8196945cb935fdeec93ba7aaee757d1d552f7b933912a1f25863c3cc1ff0"}, {"name": "oj", "version": "3.16.11", "platform": "ruby", "checksum": "2aab609d2bc896529bd3c70d737f591c13932a640ba6164a0f7e414efdb052b1"}, {"name": "oj-introspect", "version": "0.8.0", "platform": "ruby", "checksum": "5cbb15309d60294881e5c2f65ceb22e3b5798f26d0a1e65ae47a6342b87d9264"}, {"name": "omniauth", "version": "2.1.3", "platform": "ruby", "checksum": "8d24e2e55c41926c96e4a93fd566bc026dfd6f2c850408748e89945a565956c2"}, {"name": "omniauth-alicloud", "version": "3.0.0", "platform": "ruby", "checksum": "9c5c4f3abb40d774b946015f177d503fbde99b2b57c0858284c25cc39369013e"}, {"name": "omniauth-atlassian-oauth2", "version": "0.2.0", "platform": "ruby", "checksum": "eb07574a188ab8a03376ce288bce86bc2dd4a1382ffa5781cb5e2b7bc15d76c9"}, {"name": "omniauth-auth0", "version": "3.1.1", "platform": "ruby", "checksum": "3d9e83377b37394db077cf27082d29ccff93158f072d92fc59f1e88798c6c2b2"}, {"name": "omniauth-azure-activedirectory-v2", "version": "2.0.0", "platform": "ruby", "checksum": "c484cedd52cd233e3c216c4b3ed667ec07d20e51c550a613b65a0f90fe8ad072"}, {"name": "omniauth-github", "version": "2.0.1", "platform": "ruby", "checksum": "8ff8e70ac6d6db9d52485eef52cfa894938c941496e66b52b5e2773ade3ccad4"}, {"name": "omniauth-google-oauth2", "version": "1.1.1", "platform": "ruby", "checksum": "4496f126e84eaf760f9c6a5c69e5e7511f98092d7f25ad79fd2c0ae5e09b5039"}, {"name": "omniauth-oauth2", "version": "1.8.0", "platform": "ruby", "checksum": "b2f8e9559cc7e2d4efba57607691d6d2b634b879fc5b5b6ccfefa3da85089e78"}, {"name": "omniauth-oauth2-generic", "version": "0.2.8", "platform": "ruby", "checksum": "ce6e8539019d5ebf2f48867072b9f248f148bb4cbe7166dee655865abfae7613"}, {"name": "omniauth-saml", "version": "2.2.4", "platform": "ruby", "checksum": "5acc45aee728ecc5c8f9f02b56c424cb359b62d9a25fb8039f15432c1a61f6eb"}, {"name": "omniauth-shibboleth-redux", "version": "2.0.0", "platform": "ruby", "checksum": "e9b353fd103405fcc8549e8510b9cad857acf0b286d764fac5dba8a93ab8ffe1"}, {"name": "omniauth_openid_connect", "version": "0.8.0", "platform": "ruby", "checksum": "1f2f3890386e2a742221cee0d2e903b78d874e6fab9ea3bfa31c1462f4793d25"}, {"name": "open4", "version": "1.3.4", "platform": "ruby", "checksum": "a1df037310624ecc1ea1d81264b11c83e96d0c3c1c6043108d37d396dcd0f4b1"}, {"name": "openid_connect", "version": "2.3.1", "platform": "ruby", "checksum": "5d808380cff80d78e3d3d54cfaebe2d6461d835c674faa29e2314a402c1b2182"}, {"name": "opensearch-ruby", "version": "3.4.0", "platform": "ruby", "checksum": "0a8621686bed3c59b4c23e08cbaef873685a3fe4568e9d2703155ca92b8ca05d"}, {"name": "openssl", "version": "3.3.0", "platform": "java", "checksum": "1755479b8f17a507f0d01020365b4ba96484c033ae88aef410f69d3240261657"}, {"name": "openssl", "version": "3.3.0", "platform": "ruby", "checksum": "ff3a573fc97ab30f69483fddc80029f91669bf36532859bd182d1836f45aee79"}, {"name": "openssl-signature_algorithm", "version": "1.3.0", "platform": "ruby", "checksum": "a3b40b5e8276162d4a6e50c7c97cdaf1446f9b2c3946a6fa2c14628e0c957e80"}, {"name": "opentelemetry-api", "version": "1.2.5", "platform": "ruby", "checksum": "ab3d9a0566cd2ee068ade40e840bc973383ab8568e693c0c5712f0c789122cc9"}, {"name": "opentelemetry-common", "version": "0.21.0", "platform": "ruby", "checksum": "fe891a44583a20bc3217b324aec76d066504494951682d391cfd57d40cd01c98"}, {"name": "opentelemetry-exporter-otlp", "version": "0.29.1", "platform": "ruby", "checksum": "3244c5f489f4ee1900dbb197a15e8ed49d0c777aaa52a5cd1d1fb3cb0a190eb0"}, {"name": "opentelemetry-helpers-sql-obfuscation", "version": "0.1.0", "platform": "ruby", "checksum": "bc6ef1373dbcf979647091b3bfc99d7b6fb9669f74c3ae184f58b48adfc8d432"}, {"name": "opentelemetry-instrumentation-action_mailer", "version": "0.2.0", "platform": "ruby", "checksum": "88f2dd8cff27886e84bbf522b698f0bf86b83f0f0adb0d3d27b3fa8211b1cb0e"}, {"name": "opentelemetry-instrumentation-action_pack", "version": "0.10.0", "platform": "ruby", "checksum": "2d821a45be4c2a281cfa42ec96562268b50ff5d5fac77386e7e39e792bceab02"}, {"name": "opentelemetry-instrumentation-action_view", "version": "0.7.3", "platform": "ruby", "checksum": "6da829154e751bd88f5369b97a6346e12c3583a784f58178ccaa0b46d301ea21"}, {"name": "opentelemetry-instrumentation-active_job", "version": "0.7.8", "platform": "ruby", "checksum": "281b3d9bace7aac9f1e121b75f294e7ee8beaedd1f20405539c54df2e6763942"}, {"name": "opentelemetry-instrumentation-active_record", "version": "0.8.1", "platform": "ruby", "checksum": "be580547b5653f05c0b12cb34d8d9a22e480292ee3d1214f74c57e2d38ca4a91"}, {"name": "opentelemetry-instrumentation-active_support", "version": "0.6.0", "platform": "ruby", "checksum": "4fe71e2be21135c4a6eb8086998c508deec50080900829fb695ee01b93b507e0"}, {"name": "opentelemetry-instrumentation-aws_sdk", "version": "0.7.0", "platform": "ruby", "checksum": "a3c374b04bb7e0fa8b5452e57ca6a9c8c1487f6a9dd643c95a605b3cec5a19b5"}, {"name": "opentelemetry-instrumentation-base", "version": "0.22.3", "platform": "ruby", "checksum": "f61c434f0406cdc878bc188f67e644f94dba4be553d2fd21b2d1faa82731605f"}, {"name": "opentelemetry-instrumentation-concurrent_ruby", "version": "0.21.4", "platform": "ruby", "checksum": "04efc8114459bbd5d104b559c413aef42e12a1a489e41df2b7b89eb1f88714ce"}, {"name": "opentelemetry-instrumentation-ethon", "version": "0.21.9", "platform": "ruby", "checksum": "20f30473788e160106cc2ecaa23772e4f1237651278db10d9cff535f11a8435c"}, {"name": "opentelemetry-instrumentation-excon", "version": "0.22.5", "platform": "ruby", "checksum": "ec0f1b5b5a808e65d75bc3914c90108eba6c29dd6eb997c6de6ba3ffc3a989f4"}, {"name": "opentelemetry-instrumentation-faraday", "version": "0.24.7", "platform": "ruby", "checksum": "36df85d6d944d287a45726634e9a67d3426ab6e7aaf88623937551e3c6b2ebab"}, {"name": "opentelemetry-instrumentation-grape", "version": "0.2.0", "platform": "ruby", "checksum": "a1225d7301b7eee8eb98ae1fa90fe879c5a54d3631df731d658863a4fe5517b6"}, {"name": "opentelemetry-instrumentation-graphql", "version": "0.28.4", "platform": "ruby", "checksum": "c2cfccba8d443733d4a42a0c5c2c301daa34f1b382b0da0166255280c702dc6c"}, {"name": "opentelemetry-instrumentation-http", "version": "0.23.5", "platform": "ruby", "checksum": "71f1b4f9a722a7447e75b731835b832536739d683fbc6b61532cb318d1635e1e"}, {"name": "opentelemetry-instrumentation-http_client", "version": "0.22.8", "platform": "ruby", "checksum": "411f45781b897fc1a21a04af4d9de7e2e9b7259b8a500f5c230709c9de8f456b"}, {"name": "opentelemetry-instrumentation-net_http", "version": "0.22.8", "platform": "ruby", "checksum": "023bd68b6824b8fae5cf905f4c80f734c32395ac2d7911cadba126779730042e"}, {"name": "opentelemetry-instrumentation-pg", "version": "0.29.1", "platform": "ruby", "checksum": "8cbfc1728cd7522da58ff5e7bf5eba4e5b149ecad41076a68e7af983889a8252"}, {"name": "opentelemetry-instrumentation-rack", "version": "0.25.0", "platform": "ruby", "checksum": "539c8b4f6b818e16495e9016126537b4d1cc53292219f1acc35006fe30ce243d"}, {"name": "opentelemetry-instrumentation-rails", "version": "0.33.1", "platform": "ruby", "checksum": "510699dc0acde3ad41ebfd10938413b69ecda5b2d27bb09bda49804b23216d96"}, {"name": "opentelemetry-instrumentation-rake", "version": "0.2.2", "platform": "ruby", "checksum": "fbde8a6aab77c09bf0f94d914dd26dcf2e23ec67e2300f06a1cb8294a97d8020"}, {"name": "opentelemetry-instrumentation-redis", "version": "0.25.7", "platform": "ruby", "checksum": "2ea0f2d45fe1af0689aeadc08f5b335a2b6d9463de9d855fd25313d3c5b42fe3"}, {"name": "opentelemetry-instrumentation-sidekiq", "version": "0.25.7", "platform": "ruby", "checksum": "d6a6e2cadddfda0a0b641f9dc918e35a77bfc62bc90b80776f5194bd55e0df31"}, {"name": "opentelemetry-registry", "version": "0.3.0", "platform": "ruby", "checksum": "116ab6114a706340900718298c126f720e50b1ef3cfdbe5997611ff232fe6822"}, {"name": "opentelemetry-sdk", "version": "1.6.0", "platform": "ruby", "checksum": "b2d960f54c5aa19e2a1ba6b102796f9386bacffe2a0b5dddb21211ec8d4fc492"}, {"name": "opentelemetry-semantic_conventions", "version": "1.10.0", "platform": "ruby", "checksum": "13d24c1071736004a6c09113ee9fe163a25daa0defe6ab279a42cac7b92b1b76"}, {"name": "opentracing", "version": "0.5.0", "platform": "ruby", "checksum": "deb5d7abe6b0e7631d866d8cb5ee7bb9352650a504a32f61591302bc510b9286"}, {"name": "optimist", "version": "3.0.1", "platform": "ruby", "checksum": "336b753676d6117cad9301fac7e91dab4228f747d4e7179891ad3a163c64e2ed"}, {"name": "org-ruby", "version": "0.9.12", "platform": "ruby", "checksum": "93cbec3a4470cb9dca6a4a98dc276a6434ea9d9e7bc2d42ea33c3aedd5d1c974"}, {"name": "orm_adapter", "version": "0.5.0", "platform": "ruby", "checksum": "aa5d0be5d540cbb46d3a93e88061f4ece6a25f6e97d6a47122beb84fe595e9b9"}, {"name": "os", "version": "1.1.4", "platform": "ruby", "checksum": "57816d6a334e7bd6aed048f4b0308226c5fb027433b67d90a9ab435f35108d3f"}, {"name": "ostruct", "version": "0.6.1", "platform": "ruby", "checksum": "09a3fb7ecc1fa4039f25418cc05ae9c82bd520472c5c6a6f515f03e4988cb817"}, {"name": "pact", "version": "1.64.0", "platform": "ruby", "checksum": "0bdd848888580db7f29a7d8789b861b8067370c0ac0eb36ffc4bb7ff3f5f6ea8"}, {"name": "pact-mock_service", "version": "3.11.2", "platform": "ruby", "checksum": "42d9e54546a10229d7915da5383daeb322764c2876a84b4ea521f53c6f1fba51"}, {"name": "pact-support", "version": "1.20.0", "platform": "ruby", "checksum": "41c343a3124fb379684b9ad9f1a0766c5fa18d3b78d433a52e5552d8b9475871"}, {"name": "paper_trail", "version": "16.0.0", "platform": "ruby", "checksum": "e9b9f0fb1b8b590c8231cfa931b282ba92f90e066e393930a5e1c61ae4c5019d"}, {"name": "parallel", "version": "1.27.0", "platform": "ruby", "checksum": "4ac151e1806b755fb4e2dc2332cbf0e54f2e24ba821ff2d3dcf86bf6dc4ae130"}, {"name": "parser", "version": "3.3.9.0", "platform": "ruby", "checksum": "94d6929354b1a6e3e1f89d79d4d302cc8f5aa814431a6c9c7e0623335d7687f2"}, {"name": "parslet", "version": "1.8.2", "platform": "ruby", "checksum": "08d1ab3721cd3f175bfbee8788b2ddff71f92038f2d69bd65454c22bb9fbd98a"}, {"name": "pastel", "version": "0.8.0", "platform": "ruby", "checksum": "481da9fb7d2f6e6b1a08faf11fa10363172dc40fd47848f096ae21209f805a75"}, {"name": "pdf-core", "version": "0.10.0", "platform": "ruby", "checksum": "0a5d101e2063c01e3f941e1ee47cbb97f1adfc1395b58372f4f65f1300f3ce91"}, {"name": "peek", "version": "1.1.0", "platform": "ruby", "checksum": "d6501ead8cde46d8d8ed0d59eb6f0ba713d0a41c11a2c4a81447b2dce37b3ecc"}, {"name": "pg", "version": "1.6.1", "platform": "aarch64-linux", "checksum": "2dc057589c4df67240bd52c68303a00a91299329bc7573b7447faee42331e214"}, {"name": "pg", "version": "1.6.1", "platform": "aarch64-linux-musl", "checksum": "24e0c42594601c5021d3243e0e5e6e74d70de83b152d5bcb616ae49d6cac84c0"}, {"name": "pg", "version": "1.6.1", "platform": "aarch64-mingw-ucrt", "checksum": "0e9b3365a0d379e9f2d8ba029c58f8e7c1282c3053cda08cab887d5f1bcff3ea"}, {"name": "pg", "version": "1.6.1", "platform": "arm64-darwin", "checksum": "3b502915de30cf5983d62aabae927cb7c3628b0ca46cdf3a6b888af4ff7f42b3"}, {"name": "pg", "version": "1.6.1", "platform": "ruby", "checksum": "e210a75e5f702954537e73bb82f90dfbe0c6d9273c018cd0e93e779181028e6b"}, {"name": "pg", "version": "1.6.1", "platform": "x64-mingw-ucrt", "checksum": "4cda2eaf3c243d90592819661d4aa58fc76d4d2f86b83104b0460c9ed6c0cdbe"}, {"name": "pg", "version": "1.6.1", "platform": "x64-mingw32", "checksum": "95dd02a231c510b94d4165594affa1bea97e7d785d2bb8e690f099dfa4717043"}, {"name": "pg", "version": "1.6.1", "platform": "x86-mingw32", "checksum": "6ce6684292ce1fd06a1f8624025cf4c3f69096bba41215c332c223a22e76efd3"}, {"name": "pg", "version": "1.6.1", "platform": "x86_64-da<PERSON><PERSON>", "checksum": "c8930170622c39ee24b318a2265655b5f8f34628444ee00e4ae44068865309f7"}, {"name": "pg", "version": "1.6.1", "platform": "x86_64-linux", "checksum": "6ac0d5c8efafc3f22a7eca2a264300037598fabe27a88e5029bc0e6d90caeb1f"}, {"name": "pg", "version": "1.6.1", "platform": "x86_64-linux-musl", "checksum": "419a8e971ef122fb758a296424cb245a369c05a797b6c4787902d7d30eefa494"}, {"name": "pg_query", "version": "6.1.0", "platform": "ruby", "checksum": "8b005229e209f12c5887c34c60d0eb2a241953b9475b53a9840d24578532481e"}, {"name": "plist", "version": "3.7.0", "platform": "ruby", "checksum": "703ca90a7cb00e8263edd03da2266627f6741d280c910abbbac07c95ffb2f073"}, {"name": "png_quantizator", "version": "0.2.1", "platform": "ruby", "checksum": "6023d4d064125c3a7e02929c95b7320ed6ac0d7341f9e8de0c9ea6576ef3106b"}, {"name": "pp", "version": "0.6.2", "platform": "ruby", "checksum": "947ec3120c6f92195f8ee8aa25a7b2c5297bb106d83b41baa02983686577b6ff"}, {"name": "prawn", "version": "2.5.0", "platform": "ruby", "checksum": "f4e20e3b4f30bf5b9ae37dad15eb421831594553aa930b2391b0fa0a99c43cb6"}, {"name": "prawn-svg", "version": "0.37.0", "platform": "ruby", "checksum": "271bdd032c066777b2e76fe971b570e24cb6f4890d5658588106e8aa5b6e2830"}, {"name": "premailer", "version": "1.23.0", "platform": "ruby", "checksum": "f0d7f6ba299559c96ddf982aa5263f85e5617c86437c8d8ffff120813b2d7efb"}, {"name": "premailer-rails", "version": "1.12.0", "platform": "ruby", "checksum": "c13815d161b9bc7f7d3d81396b0bb0a61a90fa9bd89931548bf4e537c7710400"}, {"name": "prettyprint", "version": "0.2.0", "platform": "ruby", "checksum": "2bc9e15581a94742064a3cc8b0fb9d45aae3d03a1baa6ef80922627a0766f193"}, {"name": "prime", "version": "0.1.3", "platform": "ruby", "checksum": "baf031c50d6ce923594913befc8ac86a3251bffb9d6a5e8b03687962054e53e3"}, {"name": "prism", "version": "1.2.0", "platform": "ruby", "checksum": "24ff9cd3232346e68052659f14c9a618022ea98935f774df465206aba5c06d2f"}, {"name": "proc_to_ast", "version": "0.1.0", "platform": "ruby", "checksum": "92a73fa66e2250a83f8589f818b0751bcf227c68f85916202df7af85082f8691"}, {"name": "prometheus-client-mmap", "version": "1.2.10", "platform": "aarch64-linux-gnu", "checksum": "a014e7748ba72928e58538f155b18479ce315396e9c7ab2115a80ea0a86001ac"}, {"name": "prometheus-client-mmap", "version": "1.2.10", "platform": "aarch64-linux-musl", "checksum": "ba6c4b091c8a249b207ed7e482bcf58095b2631d29c3e51a463ab7f6cc01b8bd"}, {"name": "prometheus-client-mmap", "version": "1.2.10", "platform": "arm64-darwin", "checksum": "1c9956cd3db419330b5797cb7e0ab4a1684351d82da38c14291be39d067d62d7"}, {"name": "prometheus-client-mmap", "version": "1.2.10", "platform": "ruby", "checksum": "46d4ce67a05cd77910ebe9a64682206fd1c8f5fd2e4da7373713e16dc43e1444"}, {"name": "prometheus-client-mmap", "version": "1.2.10", "platform": "x86_64-da<PERSON><PERSON>", "checksum": "4ca9c299079fe9e52a27cf34095357e2547c8cae9741728ffa502c814f61512e"}, {"name": "prometheus-client-mmap", "version": "1.2.10", "platform": "x86_64-linux-gnu", "checksum": "6eee9f81a5a34829bbbca20ccd75597af4a2538f1109d1efebee9a3c8b40390e"}, {"name": "prometheus-client-mmap", "version": "1.2.10", "platform": "x86_64-linux-musl", "checksum": "a47e54d4c28196806b49f00ef9182da42812269d1ebd3e9bb4cb1ca8d68300da"}, {"name": "pry", "version": "0.14.2", "platform": "java", "checksum": "fd780670977ba04ff7ee32dabd4d02fe4bf02e977afe8809832d5dca1412862e"}, {"name": "pry", "version": "0.14.2", "platform": "ruby", "checksum": "c4fe54efedaca1d351280b45b8849af363184696fcac1c72e0415f9bdac4334d"}, {"name": "pry-byebug", "version": "3.11.0", "platform": "ruby", "checksum": "0b0abb7d309bc7f00044d512a3c8567274f7012b944b38becc8440439a1cea72"}, {"name": "pry-rails", "version": "0.3.11", "platform": "ruby", "checksum": "a69e28e24a34d75d1f60bcf241192a54253f8f7ef8a62cba1e75750a9653593d"}, {"name": "pry-shell", "version": "0.6.4", "platform": "ruby", "checksum": "ad024882d29912b071a7de65ebea538b242d2dc1498c60c7c2352ef94769f208"}, {"name": "psych", "version": "5.2.3", "platform": "java", "checksum": "3e5425b9e8a2f41cc2707d5ef14fdc1ae908abbafb12fe45727bd63900056585"}, {"name": "psych", "version": "5.2.3", "platform": "ruby", "checksum": "84a54bb952d14604fea22d99938348814678782f58b12648fcdfa4d2fce859ee"}, {"name": "public_suffix", "version": "6.0.1", "platform": "ruby", "checksum": "61d44e1cab5cbbbe5b31068481cf16976dd0dc1b6b07bd95617ef8c5e3e00c6f"}, {"name": "puma", "version": "6.6.1", "platform": "java", "checksum": "b5384f7320e3ceb5aa41c729f5ebbde360394aa8f77fcb82a092d63f8512c9c4"}, {"name": "puma", "version": "6.6.1", "platform": "ruby", "checksum": "b9b56e4a4ea75d1bfa6d9e1972ee2c9f43d0883f011826d914e8e37b3694ea1e"}, {"name": "pyu-ruby-sasl", "version": "0.0.3.3", "platform": "ruby", "checksum": "5683a6bc5738db5a1bf5ceddeaf545405fb241b4184dd4f2587e679a7e9497e5"}, {"name": "raabro", "version": "1.4.0", "platform": "ruby", "checksum": "d4fa9ff5172391edb92b242eed8be802d1934b1464061ae5e70d80962c5da882"}, {"name": "racc", "version": "1.8.1", "platform": "java", "checksum": "54f2e6d1e1b91c154013277d986f52a90e5ececbe91465d29172e49342732b98"}, {"name": "racc", "version": "1.8.1", "platform": "ruby", "checksum": "4a7f6929691dbec8b5209a0b373bc2614882b55fc5d2e447a21aaa691303d62f"}, {"name": "rack", "version": "2.2.17", "platform": "ruby", "checksum": "5fe02a1ca80d6fb2271dba00985ee2962d6f5620b6f46dfed89f5301ac4699dd"}, {"name": "rack-accept", "version": "0.4.5", "platform": "ruby", "checksum": "66247b5449db64ebb93ae2ec4af4764b87d1ae8a7463c7c68893ac13fa8d4da2"}, {"name": "rack-attack", "version": "6.7.0", "platform": "ruby", "checksum": "3ca47e8f66cd33b2c96af53ea4754525cd928ed3fa8da10ee6dad0277791d77c"}, {"name": "rack-cors", "version": "2.0.2", "platform": "ruby", "checksum": "415d4e1599891760c5dc9ef0349c7fecdf94f7c6a03e75b2e7c2b54b82adda1b"}, {"name": "rack-oauth2", "version": "2.2.1", "platform": "ruby", "checksum": "c73aa87c508043e2258f02b4fb110cacba9b37d2ccf884e22487d014a120d1a5"}, {"name": "rack-protection", "version": "2.2.2", "platform": "ruby", "checksum": "fd41414dbabbec274af0bdb1f72a48504449de4d979782c9af38cbb5dfff3299"}, {"name": "rack-proxy", "version": "0.7.7", "platform": "ruby", "checksum": "446a4b57001022145d5c3ba73b775f66a2260eaf7420c6907483141900395c8a"}, {"name": "rack-session", "version": "1.0.2", "platform": "ruby", "checksum": "a02115e5420b4de036839b9811e3f7967d73446a554b42aa45106af335851d76"}, {"name": "rack-test", "version": "2.1.0", "platform": "ruby", "checksum": "0c61fc61904049d691922ea4bb99e28004ed3f43aa5cfd495024cc345f125dfb"}, {"name": "rack-timeout", "version": "0.7.0", "platform": "ruby", "checksum": "757337e9793cca999bb73a61fe2a7d4280aa9eefbaf787ce3b98d860749c87d9"}, {"name": "rackup", "version": "1.0.1", "platform": "ruby", "checksum": "ba86604a28989fe1043bff20d819b360944ca08156406812dca6742b24b3c249"}, {"name": "rails", "version": "*******", "platform": "ruby", "checksum": "f38ff86c5e6905e5d30a762575f92ddad5b60346f5acfc282b0e22dbb36eca97"}, {"name": "rails-controller-testing", "version": "1.0.5", "platform": "ruby", "checksum": "741448db59366073e86fc965ba403f881c636b79a2c39a48d0486f2607182e94"}, {"name": "rails-dom-testing", "version": "2.2.0", "platform": "ruby", "checksum": "e515712e48df1f687a1d7c380fd7b07b8558faa26464474da64183a7426fa93b"}, {"name": "rails-html-sanitizer", "version": "1.6.1", "platform": "ruby", "checksum": "e3d2fb10339f03b802e39c7f6cac28c54fd404d3f65ae39c31cca9d150c5cbf0"}, {"name": "rails-i18n", "version": "7.0.10", "platform": "ruby", "checksum": "efae16e0ac28c0f42e98555c8db1327d69ab02058c8b535e0933cb106dd931ca"}, {"name": "railties", "version": "*******", "platform": "ruby", "checksum": "457506d29b7be2c5644b5bd4740edaf42bcefa52b50f9bed519a9b10ec9069e0"}, {"name": "rainbow", "version": "3.1.1", "platform": "ruby", "checksum": "039491aa3a89f42efa1d6dec2fc4e62ede96eb6acd95e52f1ad581182b79bc6a"}, {"name": "rake", "version": "13.0.6", "platform": "ruby", "checksum": "5ce4bf5037b4196c24ac62834d8db1ce175470391026bd9e557d669beeb19097"}, {"name": "rake-compiler-dock", "version": "1.9.1", "platform": "ruby", "checksum": "e73720a29aba9c114728ce39cc0d8eef69ba61d88e7978c57bac171724cd4d53"}, {"name": "rb-fsevent", "version": "0.11.2", "platform": "ruby", "checksum": "43900b972e7301d6570f64b850a5aa67833ee7d87b458ee92805d56b7318aefe"}, {"name": "rb-inotify", "version": "0.10.1", "platform": "ruby", "checksum": "050062d4f31d307cca52c3f6a7f4b946df8de25fc4bd373e1a5142e41034a7ca"}, {"name": "rb_sys", "version": "0.9.110", "platform": "ruby", "checksum": "8993fd1f49b5f6b394b49dafda77fb5c635458637f8effe508cca78ebc03427d"}, {"name": "rbs", "version": "3.6.1", "platform": "ruby", "checksum": "ed7273d018556844583d1785ac54194e67eec594d68e317d57fa90ad035532c0"}, {"name": "rbtrace", "version": "0.5.2", "platform": "ruby", "checksum": "a2d7d222ab81363aaa0e91337ddbf70df834885d401a80ea0339d86c71f31895"}, {"name": "rchardet", "version": "1.8.0", "platform": "ruby", "checksum": "693acd5253d5ade81a51940697955f6dd4bb2f0d245bda76a8e23deec70a52c7"}, {"name": "rdoc", "version": "6.13.0", "platform": "ruby", "checksum": "32c2139ae43ed91b7c43032fe5423d21d57718829cc5a11e5c9710d2aa5e0329"}, {"name": "re2", "version": "2.19.0", "platform": "aarch64-linux-gnu", "checksum": "0d2a4bc61a72e6c8a9d5ef4fa737ff81e31babd3f2eed935a4968ed2de5d85fc"}, {"name": "re2", "version": "2.19.0", "platform": "aarch64-linux-musl", "checksum": "f2545dc1901ce8bc9deb4757610a237cfcd7ba33fa41d126aba52d8dd09bd388"}, {"name": "re2", "version": "2.19.0", "platform": "arm-linux-gnu", "checksum": "e31ec03b36dc94a7ce655012cd8e62a90108a79ea9dc19c6c0e6b56bc10c4d53"}, {"name": "re2", "version": "2.19.0", "platform": "arm-linux-musl", "checksum": "84788a23bdef85cb96a7c7684ca7479e1307ac662e9497f3897711dc2dc16c0b"}, {"name": "re2", "version": "2.19.0", "platform": "arm64-darwin", "checksum": "ec04ad299846bf8e57abad31a2b632a405f753af042771b4af6b4f8899f6083c"}, {"name": "re2", "version": "2.19.0", "platform": "ruby", "checksum": "676b956a65d88abac2ff7010b48e38b3a1a8dce99a995eadf35609e7bc076ee7"}, {"name": "re2", "version": "2.19.0", "platform": "x64-mingw-ucrt", "checksum": "9e66e660657d9ec07c9af9a219c8cf04a7706dbdff09c266d5be3e964f399239"}, {"name": "re2", "version": "2.19.0", "platform": "x64-mingw32", "checksum": "55a3beea0946bbc48f804a1347bf9f7f583f364adf864cf69282e6c2d40984dc"}, {"name": "re2", "version": "2.19.0", "platform": "x86-linux-gnu", "checksum": "c50b6752223f522c56a581bfb0cbc3e65f994123c09ee1fb0c99f51e35b38fe0"}, {"name": "re2", "version": "2.19.0", "platform": "x86-linux-musl", "checksum": "9760b1cb9535caf64fabc5bd4272c11f727bef64c0f29204b8374bcefb2511ef"}, {"name": "re2", "version": "2.19.0", "platform": "x86-mingw32", "checksum": "fa80144f6a3eec1ce8dcafc945f2c4e51bac131aaa35633ea19664b5cd586679"}, {"name": "re2", "version": "2.19.0", "platform": "x86_64-da<PERSON><PERSON>", "checksum": "c6ad0ee36001f158a4c007c446a09ba955509ed2e90cfa1a4fc52b3418402ce9"}, {"name": "re2", "version": "2.19.0", "platform": "x86_64-linux-gnu", "checksum": "fa949b498e65348a9cb94eedcff8cff52804d703159f0106ebf1ea13f02b55f0"}, {"name": "re2", "version": "2.19.0", "platform": "x86_64-linux-musl", "checksum": "ca108f0d4474f9885ccdb653c38e7fd0b8ded24ba6d6416aaea07fc5939da36f"}, {"name": "recaptcha", "version": "5.12.3", "platform": "ruby", "checksum": "37d1894add9e70a54d0c6c7f0ecbeedffbfa7d075acfbd4c509818dfdebdb7ee"}, {"name": "recursive-open-struct", "version": "1.1.3", "platform": "ruby", "checksum": "a3538a72552fcebcd0ada657bdff313641a4a5fbc482c08cfb9a65acb1c9de5a"}, {"name": "redcarpet", "version": "3.6.0", "platform": "ruby", "checksum": "8ad1889c0355ff4c47174af14edd06d62f45a326da1da6e8a121d59bdcd2e9e9"}, {"name": "redis", "version": "5.4.1", "platform": "ruby", "checksum": "b5e675b57ad22b15c9bcc765d5ac26f60b675408af916d31527af9bd5a81faae"}, {"name": "redis-actionpack", "version": "5.5.0", "platform": "ruby", "checksum": "dc0570b78c14ec62b35c17b97fab778ee5986bc55e695bfb6826488088693311"}, {"name": "redis-client", "version": "0.25.2", "platform": "ruby", "checksum": "aa37e34c29da39fdb0b8663e7a649adb0923959cd4a9351befe2cd19e6f8d6f0"}, {"name": "redis-cluster-client", "version": "0.13.5", "platform": "ruby", "checksum": "18d6c9598009bcdb47b2a62bd6d00b833444aace740d2a2f00113774b298204c"}, {"name": "redis-clustering", "version": "5.4.1", "platform": "ruby", "checksum": "87444bb101fda5f1ef73b87243759224ca5952f3fe3c73842a2b8f78e45844ea"}, {"name": "redis-namespace", "version": "1.11.0", "platform": "ruby", "checksum": "e91a1aa2b2d888b6dea1d4ab8d39e1ae6fac3426161feb9d91dd5cca598a2239"}, {"name": "redis-rack", "version": "3.0.0", "platform": "ruby", "checksum": "abb50b82ae10ad4d11ca2e4901bfc2b98256cdafbbd95f80c86fc9e001478380"}, {"name": "redis-store", "version": "1.11.0", "platform": "ruby", "checksum": "edc4f3e239dcd1fdd9905584e6b1e623a84618e14436e6e8a07c70891008eda4"}, {"name": "regexp_parser", "version": "2.10.0", "platform": "ruby", "checksum": "cb6f0ddde88772cd64bff1dbbf68df66d376043fe2e66a9ef77fcb1b0c548c61"}, {"name": "regexp_property_values", "version": "1.0.0", "platform": "java", "checksum": "5e26782b01241616855c4ee7bb8a62fce9387e484f2d3eaf04f2a0633708222e"}, {"name": "regexp_property_values", "version": "1.0.0", "platform": "ruby", "checksum": "162499dc0bba1e66d334273a059f207a61981cc8cc69d2ca743594e7886d080f"}, {"name": "reline", "version": "0.6.0", "platform": "ruby", "checksum": "57620375dcbe56ec09bac7192bfb7460c716bbf0054dc94345ecaa5438e539d2"}, {"name": "representable", "version": "3.2.0", "platform": "ruby", "checksum": "cc29bf7eebc31653586849371a43ffe36c60b54b0a6365b5f7d95ec34d1ebace"}, {"name": "request_store", "version": "1.7.0", "platform": "ruby", "checksum": "e1b75d5346a315f452242a68c937ef8e48b215b9453a77a6c0acdca2934c88cb"}, {"name": "responders", "version": "3.0.1", "platform": "ruby", "checksum": "613fe28e498987f4feaa3230aa6313ca4bd5f0563a3da83511b0dd6cd8f47292"}, {"name": "rest-client", "version": "2.1.0", "platform": "ruby", "checksum": "35a6400bdb14fae28596618e312776c158f7ebbb0ccad752ff4fa142bf2747e3"}, {"name": "rest-client", "version": "2.1.0", "platform": "x64-mingw32", "checksum": "7cd156496196d90b7d8f5b8de521ef67d8a9e03f06862da80b9b5912ab05a470"}, {"name": "rest-client", "version": "2.1.0", "platform": "x86-mingw32", "checksum": "fb543caf36cb555c05c6186aeb1273c6a1b059896e4cfd394e7269b20c40ca01"}, {"name": "rest-client", "version": "2.1.0", "platform": "x86-mswin32", "checksum": "a35a3bb8d16ca39d110a946a2c805267f98ce07a0ae890e4512a45eadea47a6e"}, {"name": "retriable", "version": "3.1.2", "platform": "ruby", "checksum": "0a5a5d0ca4ba61a76fb31a17ab8f7f80281beb040c329d34dfc137a1398688e0"}, {"name": "reverse_markdown", "version": "3.0.0", "platform": "ruby", "checksum": "ab228386765a0259835873cd07054b62939c40f620c77c247eafaaa3b23faca4"}, {"name": "rexml", "version": "3.4.2", "platform": "ruby", "checksum": "1384268554a37af5da5279431ca3f2f37d46f09ffdd6c95e17cc84c83ea7c417"}, {"name": "<PERSON>u", "version": "2.0.0", "platform": "ruby", "checksum": "3e695aaf9f24baba3af45823b5c427b58a624582132f18482320e2737f9f8a85"}, {"name": "rotp", "version": "6.3.0", "platform": "ruby", "checksum": "75d40087e65ed0d8022c33055a6306c1c400d1c12261932533b5d6cbcd868854"}, {"name": "rouge", "version": "4.6.0", "platform": "ruby", "checksum": "10198622df0ef919796da5686a9cc116a49280805e1ed4b851c97ef677eddd7a"}, {"name": "rqrcode", "version": "2.2.0", "platform": "ruby", "checksum": "23eea88bb44c7ee6d6cab9354d08c287f7ebcdc6112e1fe7bcc2d010d1ffefc1"}, {"name": "rqrcode_core", "version": "1.2.0", "platform": "ruby", "checksum": "cf4989dc82d24e2877984738c4ee569308625fed2a810960f1b02d68d0308d1a"}, {"name": "rspec", "version": "3.13.0", "platform": "ruby", "checksum": "d490914ac1d5a5a64a0e1400c1d54ddd2a501324d703b8cfe83f458337bab993"}, {"name": "rspec-benchmark", "version": "0.6.0", "platform": "ruby", "checksum": "1014adb57ec2599a2455c63884229f367a2fff6a63a77fd68ce5d804c83dd6cf"}, {"name": "rspec-core", "version": "3.13.1", "platform": "ruby", "checksum": "9daa4ff29812e620193ebc8952e032f031fe167a9f6daf7ea3d29dc31d47c868"}, {"name": "rspec-expectations", "version": "3.13.3", "platform": "ruby", "checksum": "0e6b5af59b900147698ea0ff80456c4f2e69cac4394fbd392fbd1ca561f66c58"}, {"name": "rspec-mocks", "version": "3.13.2", "platform": "ruby", "checksum": "2327335def0e1665325a9b617e3af9ae20272741d80ac550336309a7c59abdef"}, {"name": "rspec-parameterized", "version": "1.0.2", "platform": "ruby", "checksum": "b456dec0091924175ac13963e173cdbaa2ab3e1581a405a948addc34e3f3f4c2"}, {"name": "rspec-parameterized-core", "version": "1.0.0", "platform": "ruby", "checksum": "287b494985e79821160af63aba4f91db8dbfa9a21cb200db34ba38f40e16ccc1"}, {"name": "rspec-parameterized-table_syntax", "version": "1.0.0", "platform": "ruby", "checksum": "d7df951eff9c5dd367ca7d5f9ae4853bb7ab7941f9d5b35bba361d112704988c"}, {"name": "rspec-rails", "version": "7.1.1", "platform": "ruby", "checksum": "e15dccabed211e2fd92f21330c819adcbeb1591c1d66c580d8f2d8288557e331"}, {"name": "rspec-retry", "version": "0.6.2", "platform": "ruby", "checksum": "6101ba23a38809811ae3484acde4ab481c54d846ac66d5037ccb40131a60d858"}, {"name": "rspec-support", "version": "3.13.1", "platform": "ruby", "checksum": "48877d4f15b772b7538f3693c22225f2eda490ba65a0515c4e7cf6f2f17de70f"}, {"name": "rspec_junit_formatter", "version": "0.6.0", "platform": "ruby", "checksum": "40dde674e6ae4e6cc0ff560da25497677e34fefd2338cc467a8972f602b62b15"}, {"name": "rspec_profiling", "version": "0.0.9", "platform": "ruby", "checksum": "6199be2daeaa14bac3d10d704dbb0a8df052cf046332c505603263aea24f7590"}, {"name": "rubocop", "version": "1.71.1", "platform": "ruby", "checksum": "d3dfd1e484a3a619dcf76c6a4fba694cd833921e4fd254d111845c26bcecfcfa"}, {"name": "rubocop-ast", "version": "1.38.0", "platform": "ruby", "checksum": "4fdf6792fe443a9a18acb12dbc8225d0d64cd1654e41fedb30e79c18edbb26ae"}, {"name": "rubocop-capybara", "version": "2.21.0", "platform": "ruby", "checksum": "5d264efdd8b6c7081a3d4889decf1451a1cfaaec204d81534e236bc825b280ab"}, {"name": "rubocop-factory_bot", "version": "2.26.1", "platform": "ruby", "checksum": "8de13cd4edcee5ca800f255188167ecef8dbfc3d1fae9f15734e9d2e755392aa"}, {"name": "rubocop-graphql", "version": "1.5.4", "platform": "ruby", "checksum": "2d888d40b08577daf1e74ca4623be1e3058c1a93543d5a7220818f561a254192"}, {"name": "rubocop-performance", "version": "1.21.1", "platform": "ruby", "checksum": "5cf20002a544275ad6aa99abca4b945d2a2ed71be925c38fe83700360ed8734e"}, {"name": "rubocop-rails", "version": "2.26.2", "platform": "ruby", "checksum": "f5561a09d6afd2f54316f3f0f6057338ca55b6c24a25ba6a938d3ed0fded84ad"}, {"name": "rubocop-rspec", "version": "3.0.5", "platform": "ruby", "checksum": "c6a8e29fb1b00d227c32df159e92f5ebb9e0ff734e52955fb13aff5c74977e0f"}, {"name": "rubocop-rspec_rails", "version": "2.30.0", "platform": "ruby", "checksum": "888112e83f9d7ef7ad2397e9d69a0b9614a4bae24f072c399804a180f80c4c46"}, {"name": "ruby-lsp", "version": "0.23.20", "platform": "ruby", "checksum": "06956135001716b89d08385714c095e01a6b3563452e4a265781899d26fb2769"}, {"name": "ruby-lsp-rails", "version": "0.3.31", "platform": "ruby", "checksum": "670aed466e54b5632e4907b8dedb91d8b144917c42513e013d656af175bf8c76"}, {"name": "ruby-lsp-rspec", "version": "0.1.24", "platform": "ruby", "checksum": "41ff78b6ddaa588d81a6c6cce7dd151ddc53246c7fe5f2fd9a94a0e30877a222"}, {"name": "ruby-magic", "version": "0.6.0", "platform": "ruby", "checksum": "7b2138877b7d23aff812c95564eba6473b74b815ef85beb0eb792e729a2b6101"}, {"name": "ruby-progressbar", "version": "1.11.0", "platform": "ruby", "checksum": "cc127db3866dc414ffccbf92928a241e585b3aa2b758a5563e74a6ee0f57d50a"}, {"name": "ruby-saml", "version": "1.18.0", "platform": "ruby", "checksum": "de342a55925fd5ce6114d0802651c324428c0fec26e7fe52bf3a7cfa54dbfa6d"}, {"name": "ruby-statistics", "version": "4.1.0", "platform": "ruby", "checksum": "7d697abd5dc4e6141d21ecb4165482807564f11bbe154cf1c60a2677b507f2a9"}, {"name": "ruby2_keywords", "version": "0.0.5", "platform": "ruby", "checksum": "ffd13740c573b7301cf7a2e61fc857b2a8e3d3aff32545d6f8300d8bae10e3ef"}, {"name": "rubyntlm", "version": "0.6.3", "platform": "ruby", "checksum": "5b321456dba3130351f7451f8669f1afa83a0d26fd63cdec285b7b88e667102d"}, {"name": "rubypants", "version": "0.2.0", "platform": "ruby", "checksum": "f07e38eac793655a0323fe91946081052341b9e69807026fcf102346589eedee"}, {"name": "rubyzip", "version": "2.4.1", "platform": "ruby", "checksum": "8577c88edc1fde8935eb91064c5cb1aef9ad5494b940cf19c775ee833e075615"}, {"name": "rugged", "version": "1.6.3", "platform": "ruby", "checksum": "362631de8dc6f1074242f21e01148ac70b7fe8cdb17f85eee91d4ea83457cb04"}, {"name": "safe_yaml", "version": "1.0.4", "platform": "ruby", "checksum": "248193992ef1730a0c9ec579999ef2256a2b3a32a9bd9d708a1e12544a489ec2"}, {"name": "safety_net_attestation", "version": "0.4.0", "platform": "ruby", "checksum": "96be2d74e7ed26453a51894913449bea0e072f44490021545ac2d1c38b0718ce"}, {"name": "sanitize", "version": "6.0.2", "platform": "ruby", "checksum": "48c4eb8e92bb1699056b6000986ac50fc9df82f458a941abf2c4d6759bccd5cf"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "aarch64-linux-android", "checksum": "ba9c75b59b34e12679c55a6a42da5b8b90311bd4e41477ff7d34a3738d2bf2e0"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "aarch64-linux-gnu", "checksum": "c84eda6b86669a15695d9a7ddbc7a6e3cb706735d17fe37c5c4e1c584d467534"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "aarch64-linux-musl", "checksum": "6f6224df2d4a22ba686a636ea8a4a7f146208d9e3dbbe35041a7daed63528c40"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "aarch64-mingw-ucrt", "checksum": "66abfce03d940ca5c231c5eaf3df658508761dac09f7474101db9ee549561080"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "arm-linux-androideabi", "checksum": "f5c55676da02a33d664d3ebef043e6b3813d64fd4fc1016420dd8ed4c5b252bf"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "arm-linux-gnueabihf", "checksum": "e0e067f06ea4bb001c7c2099d6f0980d1fad918b3ea182857563f6488f07ff65"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "arm-linux-musleabihf", "checksum": "ad2b7cf152d5a7195d57508d466ebce56cdcac5d3ef95f05fa62664eeb9a5eda"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "arm64-darwin", "checksum": "a6a524aefe8b181c55d5f11c26b2329a06e20de32180f18904fc8488b89bdb36"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "riscv64-linux-android", "checksum": "04cd2e96f12e1fc84010a01d213dd4496afbd7c92ed3f72b904a5cf55b8048c7"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "riscv64-linux-gnu", "checksum": "7527f7eb49dc788892a2db4e22d23d06bbce05ddf6663950069cd8f933005484"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "riscv64-linux-musl", "checksum": "b658e258db2eaa183143a12e95fa229b97f39fb74fa0d4463899bfbb05dba375"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "ruby", "checksum": "a2a6adc4ce695ece780f40388d207de396e5091ddd28767440c7907a4501beda"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "x64-mingw-ucrt", "checksum": "20d1464c7faaec4481f81cc173ca1374a2cda5f558fa4e98b8bb3c7c386763d3"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "x64-mingw32", "checksum": "95b4c534bc31db1f5692fcfc0e965c81a21fd4dce7e72261d682780851f0d4b2"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "x64-mswin64", "checksum": "6591f5aa8c6940f7584004ad41581048d62d5ab8c83bbf65507c801098bcdb94"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "x86-cygwin", "checksum": "834ee1a4cb4be1d354f12422550ada9db5966a836c6af05c2a2e7c3aa3790117"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "x86-linux-android", "checksum": "4b0334b50a5475bcb621257abc4b32ca802205dbb23608a78c60b1ce1745d4b3"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "x86-linux-gnu", "checksum": "de9255b988ce9866a4734acfcb44f55e73055607e11a1cfa4a0105e5fd6fd928"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "x86-linux-musl", "checksum": "8fc2a3dda88a903550f422c99a0bab9385af493d731f2256cd580fe4a4e92423"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "x86-mingw-ucrt", "checksum": "ea7544b5b30d64a4c9547d576eb2e76f66de8d0d773cfe205357cf564cfae773"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "x86-mingw32", "checksum": "58f2879d01b31bab07f59f641badc62168ccfa7931c69aa906e79ede502c1911"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "x86-mswin32", "checksum": "b699e56fde6bfbaa1b2042ad58680e84b6cc535b88dd1a0cfaabeee7c0af880f"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "x86_64-cygwin", "checksum": "11eeea03f15ae39b03a84bebf8d80f8824941d2cfde57b1c21dbb31d65de0dc3"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "x86_64-da<PERSON><PERSON>", "checksum": "228ee25c012e50bef83e433415b20dddaaaa9c01c8c40c4f99669d919f26bfc3"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "x86_64-linux-android", "checksum": "e183bf5a0b3916eb3f5b3c7596ae44fd72edb0fe233dd00df48131271ed752ea"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "x86_64-linux-gnu", "checksum": "3000a6b984ea746eac1f0c9a17400134fe76c3d41b33436262ac82b5d153d7b4"}, {"name": "sass-embedded", "version": "1.77.5", "platform": "x86_64-linux-musl", "checksum": "c5dd43155112f7b4eb9e3cafb8ceaa54a157977a2033b1022875bac71a4a4d56"}, {"name": "sawyer", "version": "0.9.2", "platform": "ruby", "checksum": "fa3a72d62a4525517b18857ddb78926aab3424de0129be6772a8e2ba240e7aca"}, {"name": "sd_notify", "version": "0.1.1", "platform": "ruby", "checksum": "cbc7ac6caa7cedd26b30a72b5eeb6f36050dc0752df263452ea24fb5a4ad3131"}, {"name": "securerandom", "version": "0.4.1", "platform": "ruby", "checksum": "cc5193d414a4341b6e225f0cb4446aceca8e50d5e1888743fac16987638ea0b1"}, {"name": "seed-fu", "version": "2.3.9", "platform": "ruby", "checksum": "6d902d12dc1b88a16d487506baacc93b3a92e3671fdd603110d1600d35fbf478"}, {"name": "selenium-webdriver", "version": "4.32.0", "platform": "ruby", "checksum": "20dbdf27b586beeac9e2791a22312b414d0a30192b6ce144779f7dfde8ae8afe"}, {"name": "semver_dialects", "version": "3.7.0", "platform": "ruby", "checksum": "6110b05266f7c8ce7794869d4d9dd3e15c3e5878eb1ffe5f0cea00060141dd1e"}, {"name": "sentry-rails", "version": "5.23.0", "platform": "ruby", "checksum": "8d2cbc3c85e343c1e882d7c8595d410e0c3afa3b005f51430225b938e128dec4"}, {"name": "sentry-ruby", "version": "5.23.0", "platform": "ruby", "checksum": "8e8bb2f9a56a267a50fcba947f2ae131b6542f45fc3bb5764c2c25ba68f385cc"}, {"name": "sentry-sidekiq", "version": "5.23.0", "platform": "ruby", "checksum": "34dc6413a25773e185acba605b0ed4dcec5edf3e8e562447b7b44ed6435d1c18"}, {"name": "shellany", "version": "0.0.1", "platform": "ruby", "checksum": "0e127a9132698766d7e752e82cdac8250b6adbd09e6c0a7fbbb6f61964fedee7"}, {"name": "shoulda-matchers", "version": "6.4.0", "platform": "ruby", "checksum": "9055bb7f4bb342125fb860809798855c630e05ef5e75837b3168b8e6ee1608b0"}, {"name": "sidekiq-cron", "version": "1.12.0", "platform": "ruby", "checksum": "6663080a454088bd88773a0da3ae91e554b8a2e8b06cfc629529a83fd1a3096c"}, {"name": "sigdump", "version": "0.2.5", "platform": "ruby", "checksum": "bb706c1cce70458b285d2c3a57121e801ccb79f68be7f7377692eb40b5437242"}, {"name": "signet", "version": "0.19.0", "platform": "ruby", "checksum": "537f3939f57f141f691e6069a97ec40f34fadafc4c7e5ba94edb06cf4350dd31"}, {"name": "simple_po_parser", "version": "1.1.6", "platform": "ruby", "checksum": "122687d44d3de516a0e69e2f383a4180f5015e8c5ed5a7f2258f2b376f64cbf3"}, {"name": "simplecov", "version": "0.22.0", "platform": "ruby", "checksum": "fe2622c7834ff23b98066bb0a854284b2729a569ac659f82621fc22ef36213a5"}, {"name": "simplecov-cobertura", "version": "2.1.0", "platform": "ruby", "checksum": "2c6532e34df2e38a379d72cef9a05c3b16c64ce90566beebc6887801c4ad3f02"}, {"name": "simplecov-html", "version": "0.12.3", "platform": "ruby", "checksum": "4b1aad33259ffba8b29c6876c12db70e5750cb9df829486e4c6e5da4fa0aa07b"}, {"name": "simplecov-lcov", "version": "0.8.0", "platform": "ruby", "checksum": "0115f31cb7ef5ec4334f5d9382c67fd43de2e5270e21b65bfc693da82dd713c1"}, {"name": "simplecov_json_formatter", "version": "0.1.4", "platform": "ruby", "checksum": "529418fbe8de1713ac2b2d612aa3daa56d316975d307244399fa4838c601b428"}, {"name": "<PERSON><PERSON>n", "version": "0.2.3", "platform": "ruby", "checksum": "08ce96f03fa1605286be22651ba0fc9c0b2d6272c9b27a260bc88be05b0d2c29"}, {"name": "singleton", "version": "0.3.0", "platform": "ruby", "checksum": "83ea1bca5f4aa34d00305ab842a7862ea5a8a11c73d362cb52379d94e9615778"}, {"name": "sixarm_ruby_unaccent", "version": "1.2.0", "platform": "ruby", "checksum": "0043a6077bdf2c4b03040152676a07f8bf77144f9b007b1960ee5c94d13a4384"}, {"name": "slack-messenger", "version": "2.3.6", "platform": "ruby", "checksum": "58581e587debcbb769336cc7ebe4eb6ae411947fccf347e967a17ac9813e66d8"}, {"name": "snaky_hash", "version": "2.0.0", "platform": "ruby", "checksum": "fe8b2e39e8ff69320f7812af73ea06401579e29ff1734a7009567391600687de"}, {"name": "snowplow-tracker", "version": "0.8.0", "platform": "ruby", "checksum": "7ba6f4f1443a829845fd28e63eda72d9d3d247f485310ddcccaebbc52b734a38"}, {"name": "solargraph", "version": "0.54.4", "platform": "ruby", "checksum": "842705cc511a085e967314de8bd2dd89b00f90238b5582e665ffa39efbd880e0"}, {"name": "solargraph-rspec", "version": "0.5.2", "platform": "ruby", "checksum": "ac3f9ec710511f946934912bccebadd49a3886d6e126099f16d34080caf9bff3"}, {"name": "sorbet-runtime", "version": "0.5.11647", "platform": "ruby", "checksum": "64b65112f2e6a5323310ca9ac0d7d9a6be63aade5a62a6225fe066042ff4fdb6"}, {"name": "spamcheck", "version": "1.3.3", "platform": "ruby", "checksum": "3a29ba9dfcd59543d88054d38c657f79e0a6cf44d763df08ad47680abed50ec7"}, {"name": "spring", "version": "4.3.0", "platform": "ruby", "checksum": "0aaaf3bcce38e8528275854881d1922660d76cbd19a9a3af4a419d95b7fe7122"}, {"name": "spring-commands-rspec", "version": "1.0.4", "platform": "ruby", "checksum": "6202e54fa4767452e3641461a83347645af478bf45dddcca9737b43af0dd1a2c"}, {"name": "sprite-factory", "version": "1.7.1", "platform": "ruby", "checksum": "5586524a1aec003241f1abc6852b61433e988aba5ee2b55f906387bf49b01ba2"}, {"name": "sprockets", "version": "3.7.5", "platform": "ruby", "checksum": "72c20f256548f8a37fe7db41d96be86c3262fddaf4ebe9d69ec8317394fed383"}, {"name": "sprockets-rails", "version": "3.5.2", "platform": "ruby", "checksum": "a9e88e6ce9f8c912d349aa5401509165ec42326baf9e942a85de4b76dbc4119e"}, {"name": "ssh_data", "version": "1.3.0", "platform": "ruby", "checksum": "ec7c1e95a3aebeee412147998f4c147b4b05da6ed0aafda6083f9449318eaac0"}, {"name": "ssrf_filter", "version": "1.0.8", "platform": "ruby", "checksum": "03f49f54837e407d43ee93ec733a8a94dc1bcf8185647ac61606e63aaedaa0db"}, {"name": "stackprof", "version": "0.2.27", "platform": "ruby", "checksum": "aff6d28656c852e74cf632cc2046f849033dc1dedffe7cb8c030d61b5745e80c"}, {"name": "state_machines", "version": "0.5.0", "platform": "ruby", "checksum": "23e6249d374a920b528dccade403518b4abbd83841a3e2c9ef13e6f1a009b102"}, {"name": "state_machines-activemodel", "version": "0.8.0", "platform": "ruby", "checksum": "e932dab190d4be044fb5f9cab01a3ea0b092c5f113d4676c6c0a0d49bf738d2c"}, {"name": "state_machines-activerecord", "version": "0.8.0", "platform": "ruby", "checksum": "072fb701b8ab03de0608297f6c55dc34ed096e556fa8f77e556f3c461c71aab6"}, {"name": "state_machines-rspec", "version": "0.6.0", "platform": "ruby", "checksum": "2ba57a45df57d0c97f79146e2e0f65f519b52e65e182805ef79cb73b1fe5c0bd"}, {"name": "stringio", "version": "3.1.7", "platform": "java", "checksum": "167bdd3d60a002ee94bc289cc3259638aaadc36a47b3086a44a694b5dc72a499"}, {"name": "stringio", "version": "3.1.7", "platform": "ruby", "checksum": "5b78b7cb242a315fb4fca61a8255d62ec438f58da2b90be66048546ade4507fa"}, {"name": "strings", "version": "0.2.1", "platform": "ruby", "checksum": "933293b3c95cf85b81eb44b3cf673e3087661ba739bbadfeadf442083158d6fb"}, {"name": "strings-ansi", "version": "0.2.0", "platform": "ruby", "checksum": "90262d760ea4a94cc2ae8d58205277a343409c288cbe7c29416b1826bd511c88"}, {"name": "swd", "version": "2.0.3", "platform": "ruby", "checksum": "4cdbe2a4246c19f093fce22e967ec3ebdd4657d37673672e621bf0c7eb770655"}, {"name": "sync", "version": "0.5.0", "platform": "ruby", "checksum": "668356cc07c59ac7ed9ecf34fec3929831f179c07adb1f3e1c3b7a1609a638fd"}, {"name": "sys-filesystem", "version": "1.4.3", "platform": "ruby", "checksum": "390919de89822ad6d3ba3daf694d720be9d83ed95cdf7adf54d4573c98b17421"}, {"name": "sysexits", "version": "1.2.0", "platform": "ruby", "checksum": "598241c4ae57baa403c125182dfdcc0d1ac4c0fb606dd47fbed57e4aaf795662"}, {"name": "table_print", "version": "1.5.7", "platform": "ruby", "checksum": "436664281f93387b882335795e16cfeeb839ad0c785ff7f9110fc0f17c68b5cb"}, {"name": "tanuki_emoji", "version": "0.13.0", "platform": "ruby", "checksum": "dee2182a5cad6f88ed91cd4e39088bd2a8f313e24f83ff5d4b5b0fecf29f6d93"}, {"name": "telesign", "version": "2.4.0", "platform": "ruby", "checksum": "009a76081cfb7fc901475bffa382353a764377b7a881d96fd441aaa9a87cb39f"}, {"name": "telesignenterprise", "version": "2.6.0", "platform": "ruby", "checksum": "172eac4563d27e57f1a2777497ec48204346fc355bbe233b55f8b05fd0d67df2"}, {"name": "temple", "version": "0.8.2", "platform": "ruby", "checksum": "c12071214346c606dbd219b4117276d04a9f2c20d65e66a66b2c4ec18efc1f18"}, {"name": "term-ansicolor", "version": "1.7.1", "platform": "ruby", "checksum": "92339ffec77c4bddc786a29385c91601dd52fc68feda23609bba0491229b05f7"}, {"name": "terminal-table", "version": "3.0.2", "platform": "ruby", "checksum": "f951b6af5f3e00203fb290a669e0a85c5dd5b051b3b023392ccfd67ba5abae91"}, {"name": "terser", "version": "1.0.2", "platform": "ruby", "checksum": "80c2e0bc7e2db4e12e8529658f9e0820e13d685ae67d745bf981f269743bb28e"}, {"name": "test-prof", "version": "1.4.4", "platform": "ruby", "checksum": "1a59513ed9d33a1f5ca17c0b89da4e70f60a91c83ec62e9a873dbb99141353ef"}, {"name": "test_file_finder", "version": "0.3.1", "platform": "ruby", "checksum": "83fb0588a06b2784b51892910b9bfd06609f8d31f2d851a98d976f644d177199"}, {"name": "text", "version": "1.3.1", "platform": "ruby", "checksum": "2fbbbc82c1ce79c4195b13018a87cbb00d762bda39241bb3cdc32792759dd3f4"}, {"name": "thor", "version": "1.3.1", "platform": "ruby", "checksum": "fa7e3471d4f6a27138e3d9c9b0d4daac9c3d7383927667ae83e9ab42ae7401ef"}, {"name": "thread_safe", "version": "0.3.6", "platform": "java", "checksum": "bb28394cd0924c068981adee71f36a81c85c92e7d74d3f62372bd51489a0e0c2"}, {"name": "thread_safe", "version": "0.3.6", "platform": "ruby", "checksum": "9ed7072821b51c57e8d6b7011a8e282e25aeea3a4065eab326e43f66f063b05a"}, {"name": "thrift", "version": "0.22.0", "platform": "ruby", "checksum": "7a44a197529af812f89ed0c26a3be60c43b378a488262efaeba966246fbb5d78"}, {"name": "tilt", "version": "2.0.11", "platform": "ruby", "checksum": "7b180fc472cbdeb186c85d31c0f2d1e61a2c0d77e1d9fd0ca28482a9d972d6a0"}, {"name": "timeout", "version": "0.4.3", "platform": "ruby", "checksum": "9509f079b2b55fe4236d79633bd75e34c1c1e7e3fb4b56cb5fda61f80a0fe30e"}, {"name": "timfel-krb5-auth", "version": "0.8.3", "platform": "ruby", "checksum": "ab388c9d747fa3cd95baf2cc1c03253e372d8c680adcc543670f4f099854bb80"}, {"name": "tins", "version": "1.31.1", "platform": "ruby", "checksum": "51c4a347c25c630d310cbc2c040ffb84e266c8227f2ade881f1130ee4f9fbecf"}, {"name": "toml-rb", "version": "2.2.0", "platform": "ruby", "checksum": "a1e2c54ac3cc9d49861004f75f0648b3622ac03a76abe105358c31553227d9a6"}, {"name": "tomlrb", "version": "1.3.0", "platform": "ruby", "checksum": "68666bf53fa70ba686a48a7435ce7e086f5227c58c4c993bd9792f4760f2a503"}, {"name": "tpm-key_attestation", "version": "0.12.0", "platform": "ruby", "checksum": "e133d80cf24fef0e7a7dfad00fd6aeff01fc79875fbfc66cd8537bbd622b1e6d"}, {"name": "traces", "version": "0.15.2", "platform": "ruby", "checksum": "d2547834b7248bb8c8f4f6532c6b9ba80ef8e2d6068ce16e7873575d7b802d81"}, {"name": "trailblazer-option", "version": "0.1.2", "platform": "ruby", "checksum": "20e4f12ea4e1f718c8007e7944ca21a329eee4eed9e0fa5dde6e8ad8ac4344a3"}, {"name": "train-core", "version": "3.10.8", "platform": "ruby", "checksum": "8493da02015fbe9b11840d22ba879ef18a0aa2633cb0c04eac3f07dd9b87223b"}, {"name": "truncato", "version": "0.7.13", "platform": "ruby", "checksum": "34621943c067eb892389d356d1312822b81b574e8d7dec2b61955fef0e91e380"}, {"name": "ttfunk", "version": "1.8.0", "platform": "ruby", "checksum": "a7cbc7e489cc46e979dde04d34b5b9e4f5c8f1ee5fc6b1a7be39b829919d20ca"}, {"name": "tty-color", "version": "0.6.0", "platform": "ruby", "checksum": "6f9c37ca3a4e2367fb2e6d09722762647d6f455c111f05b59f35730eeb24332a"}, {"name": "tty-command", "version": "0.10.1", "platform": "ruby", "checksum": "0c6c471fcb932d55518734eb4e2e07e9efdd2918713cc39bb7393ba862471192"}, {"name": "tty-cursor", "version": "0.7.1", "platform": "ruby", "checksum": "79534185e6a777888d88628b14b6a1fdf5154a603f285f80b1753e1908e0bf48"}, {"name": "tty-markdown", "version": "0.7.2", "platform": "ruby", "checksum": "1ed81db97028d006ba81e2cfd9fe0a04b0eb28650ad0d4086ed6e5627f4ac511"}, {"name": "tty-prompt", "version": "0.23.1", "platform": "ruby", "checksum": "fcdbce905238993f27eecfdf67597a636bc839d92192f6a0eef22b8166449ec8"}, {"name": "tty-reader", "version": "0.9.0", "platform": "ruby", "checksum": "c62972c985c0b1566f0e56743b6a7882f979d3dc32ff491ed490a076f899c2b1"}, {"name": "tty-screen", "version": "0.8.1", "platform": "ruby", "checksum": "6508657c38f32bdca64880abe201ce237d80c94146e1f9b911cba3c7823659a2"}, {"name": "typhoeus", "version": "1.4.1", "platform": "ruby", "checksum": "1c17db8364bd45ab302dc61e460173c3e69835896be88a3df07c206d5c55ef7c"}, {"name": "tzinfo", "version": "2.0.6", "platform": "ruby", "checksum": "8daf828cc77bcf7d63b0e3bdb6caa47e2272dcfaf4fbfe46f8c3a9df087a829b"}, {"name": "uber", "version": "0.1.0", "platform": "ruby", "checksum": "5beeb407ff807b5db994f82fa9ee07cfceaa561dad8af20be880bc67eba935dc"}, {"name": "undercover", "version": "0.7.4", "platform": "ruby", "checksum": "56ea18d64da97c9a5c40c3ecea23ae26d04b716e66540b2eb9e56fc3d6356071"}, {"name": "unf", "version": "0.1.4", "platform": "java", "checksum": "49a5972ec0b3d091d3b0b2e00113f2f342b9b212f0db855eb30a629637f6d302"}, {"name": "unf", "version": "0.1.4", "platform": "ruby", "checksum": "4999517a531f2a955750f8831941891f6158498ec9b6cb1c81ce89388e63022e"}, {"name": "unf_ext", "version": "0.0.8.2", "platform": "ruby", "checksum": "90b9623ee359cc4878461c5d2eab7d3d3ce5801a680a9e7ac83b8040c5b742fa"}, {"name": "unf_ext", "version": "0.0.8.2", "platform": "x64-mingw-ucrt", "checksum": "580e3c1ca389f10ca17d13ddeb5dc3fbdc80ce2c2b8cdb15c08af3a3b45a60fc"}, {"name": "unf_ext", "version": "0.0.8.2", "platform": "x64-mingw32", "checksum": "f7e4c01774c91eb22e30d53dfc40ffbbb5a175f785c8f6f1be17ad96a0b29ed0"}, {"name": "unf_ext", "version": "0.0.8.2", "platform": "x86-mingw32", "checksum": "6d44c13c98924bebd15ebdd4ed196ead403a0770ac03304570873349fda2a208"}, {"name": "unicode-display_width", "version": "2.4.2", "platform": "ruby", "checksum": "6a10205d1a19ca790c4e53064ba93f09d9eb234bf6bd135d9deb6001c21428be"}, {"name": "unicode-emoji", "version": "4.0.4", "platform": "ruby", "checksum": "2c2c4ef7f353e5809497126285a50b23056cc6e61b64433764a35eff6c36532a"}, {"name": "unicode_utils", "version": "1.4.0", "platform": "ruby", "checksum": "b922d0cf2313b6b7136ada6645ce7154ffc86418ca07d53b058efe9eb72f2a40"}, {"name": "uniform_notifier", "version": "1.16.0", "platform": "ruby", "checksum": "99b39ee4a0864e3b49f375b5e5803eb26d35ed6eb1719c96407573a87bc4dbb5"}, {"name": "unleash", "version": "3.2.2", "platform": "ruby", "checksum": "0f6e56498de920de66a01bceffb93933693ade646bb853fc70eb16bd1026b93b"}, {"name": "unparser", "version": "0.6.7", "platform": "ruby", "checksum": "ae42e73edfa273766e66c166368fb75ca5972cd8ec50c536253e0f6299a9dec8"}, {"name": "uri", "version": "0.13.2", "platform": "ruby", "checksum": "a557196e652011bcff0b36d29f9e427fefcf60cc35c0ab8cce08768a6287e457"}, {"name": "useragent", "version": "0.16.11", "platform": "ruby", "checksum": "700e6413ad4bb954bb63547fa098dddf7b0ebe75b40cc6f93b8d54255b173844"}, {"name": "valid_email", "version": "0.1.3", "platform": "ruby", "checksum": "b81452b51b64c4beb67913f68db52c20ecb4d73d45512f5b282ab4a3f4416570"}, {"name": "validate_url", "version": "1.0.15", "platform": "ruby", "checksum": "72fe164c0713d63a9970bd6700bea948babbfbdcec392f2342b6704042f57451"}, {"name": "validates_hostname", "version": "1.0.13", "platform": "ruby", "checksum": "eac40178cc0b4f727df9cc6a5cb5bc2550718ad8d9bb3728df9aba6354bdda19"}, {"name": "version_gem", "version": "1.1.8", "platform": "ruby", "checksum": "a964767ecbe36551b9ff2e59099548c27569f2f7f94bdb09f609d76393a8e008"}, {"name": "version_sorter", "version": "2.3.0", "platform": "ruby", "checksum": "2147f2a1a3804fbb8f60d268b7d7c1ec717e6dd727ffe2c165b4e05e82efe1da"}, {"name": "view_component", "version": "3.23.2", "platform": "ruby", "checksum": "3c2fed4b9e38bf074fa3d42ca55eedbb2cc070e0f3c31d7c13a50b0db530892b"}, {"name": "virtus", "version": "2.0.0", "platform": "ruby", "checksum": "8841dae4eb7fcc097320ba5ea516bf1839e5d056c61ee27138aa4bddd6e3d1c2"}, {"name": "vite_rails", "version": "3.0.19", "platform": "ruby", "checksum": "195c44677bc05c1f94e7a69f1264e49d4bad2729ab06538ee858c2962f5bb500"}, {"name": "vite_ruby", "version": "3.9.2", "platform": "ruby", "checksum": "e10a7c851b590cccab57904bc96c2eb078ed6e22560a778db1dcefa3818a4972"}, {"name": "vmstat", "version": "2.3.1", "platform": "ruby", "checksum": "5587cb430a54dbfc4a5c29dd01bd6a4031b2ff4c1d387504d74ff246f3b39104"}, {"name": "warden", "version": "1.2.9", "platform": "ruby", "checksum": "46684f885d35a69dbb883deabf85a222c8e427a957804719e143005df7a1efd0"}, {"name": "warning", "version": "1.5.0", "platform": "ruby", "checksum": "0f12c49fea0c06757778eefdcc7771e4fd99308901e3d55c504d87afdd718c53"}, {"name": "webauthn", "version": "3.0.0", "platform": "ruby", "checksum": "3f77d422c2a8a4b31e56cf42f83414bd066e0506e9896936e1730262dc4a20e6"}, {"name": "webfinger", "version": "2.1.3", "platform": "ruby", "checksum": "567a52bde77fb38ca6b67e55db755f988766ec4651c1d24916a65aa70540695c"}, {"name": "webmock", "version": "3.25.1", "platform": "ruby", "checksum": "ab9d5d9353bcbe6322c83e1c60a7103988efc7b67cd72ffb9012629c3d396323"}, {"name": "webrick", "version": "1.9.1", "platform": "ruby", "checksum": "b42d3c94f166f3fb73d87e9b359def9b5836c426fc8beacf38f2184a21b2a989"}, {"name": "websocket", "version": "1.2.10", "platform": "ruby", "checksum": "2cc1a4a79b6e63637b326b4273e46adcddf7871caa5dc5711f2ca4061a629fa8"}, {"name": "websocket-driver", "version": "0.7.6", "platform": "java", "checksum": "bc894b9e9d5aee55ac04b61003e1957c4ef411a5a048199587d0499785b505c3"}, {"name": "websocket-driver", "version": "0.7.6", "platform": "ruby", "checksum": "f69400be7bc197879726ad8e6f5869a61823147372fd8928836a53c2c741d0db"}, {"name": "websocket-extensions", "version": "0.1.5", "platform": "ruby", "checksum": "1c6ba63092cda343eb53fc657110c71c754c56484aad42578495227d717a8241"}, {"name": "wikicloth", "version": "0.8.1", "platform": "ruby", "checksum": "7ac8a9ca0a948cf472851e521afc6c2a6b04a8f91ef1d824ba6a61ffbd60e6ca"}, {"name": "wisper", "version": "2.0.1", "platform": "ruby", "checksum": "ce17bc5c3a166f241a2e6613848b025c8146fce2defba505920c1d1f3f88fae6"}, {"name": "with_env", "version": "1.1.0", "platform": "ruby", "checksum": "50b3e4f0a6cda8f90d8a6bd87a6261f6c381429abafb161c4c69ad4a0cd0b6e4"}, {"name": "wmi-lite", "version": "1.0.7", "platform": "ruby", "checksum": "116ef5bb470dbe60f58c2db9047af3064c16245d6562c646bc0d90877e27ddda"}, {"name": "xml-simple", "version": "1.1.9", "platform": "ruby", "checksum": "d21131e519c86f1a5bc2b6d2d57d46e6998e47f18ed249b25cad86433dbd695d"}, {"name": "xpath", "version": "3.2.0", "platform": "ruby", "checksum": "6dfda79d91bb3b949b947ecc5919f042ef2f399b904013eb3ef6d20dd3a4082e"}, {"name": "yajl-ruby", "version": "1.4.3", "platform": "ruby", "checksum": "8c974d9c11ae07b0a3b6d26efea8407269b02e4138118fbe3ef0d2ec9724d1d2"}, {"name": "yard", "version": "0.9.37", "platform": "ruby", "checksum": "a6e910399e78e613f80ba9add9ba7c394b1a935f083cccbef82903a3d2a26992"}, {"name": "yard-solargraph", "version": "0.1.0", "platform": "ruby", "checksum": "a19a4619c942181a618fb9458970a9d2534cf7fda69fc43949629a7948a5930e"}, {"name": "zeitwerk", "version": "2.6.7", "platform": "ruby", "checksum": "61767a6158480df290d0d2a3fd860d8ba3a28ba113837668ee94b657716a1409"}]