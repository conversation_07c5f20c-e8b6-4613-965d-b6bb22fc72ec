<% active_custom_abilities = MemberRole.all_customizable_permissions.filter { |p| !::Feature::Definition.get("custom_ability_#{p}") } %>
<% custom_abilities_by_feature_category = active_custom_abilities.group_by { |_k, definition| definition[:feature_category] } %>
<% def humanize(feature_category) %>
<%   case feature_category %>
<%   when 'mlops' %>
<%     "MLOps" %>
<%   when 'not_owned' %>
<%     "Not categorized" %>
<%   else %>
<%     "#{feature_category.humanize}" %>
<%   end %>
<% end %>
<% def enabled_link(ability) %>
<%   return unless ability[:feature_flag_enabled_milestone] || ability[:feature_flag_enabled_mr] %>
<%   return "GitLab #{ability[:feature_flag_enabled_milestone]}" unless ability[:feature_flag_enabled_mr] %>
<%   "GitLab [#{ability[:feature_flag_enabled_milestone]}](#{ability[:feature_flag_enabled_mr]})" %>
<% end %>
<% def feature_flag(ability) %>
<%   return unless ability[:feature_flag] %>
<%   "`#{ability[:feature_flag]}`" %>
<% end %>
<% def scope(ability) %>
<%   scopes = [] %>
<%   scopes << 'Instance' unless ability[:group_ability] || ability[:project_ability]%>
<%   scopes << 'Group' if ability[:group_ability]%>
<%   scopes << 'Project' if ability[:project_ability]%>
<%   return scopes.join(',<br> ') %>
<% end %>
---
stage: Software Supply Chain Security
group: Authorization
info: "To determine the technical writer assigned to the Stage/Group associated with this page, see https://handbook.gitlab.com/handbook/product/ux/technical-writing/#assignments"
title: Custom permissions
---

<!--
  This documentation is auto generated by a Rake task.

  Please do not edit this file directly. To update this file, run:
  `bundle exec rake gitlab:custom_roles:compile_docs`

  To make changes to the output of the Rake task,
  edit `tooling/custom_roles/docs/templates/custom_abilities.md.erb`.
-->
{{< history >}}

- Custom admin roles [introduced](https://gitlab.com/gitlab-org/gitlab/-/merge_requests/181346) in GitLab 17.9 [with a flag](../../administration/feature_flags/_index.md) named `custom_admin_roles`. Disabled by default.
- Custom admin roles [generally available](https://gitlab.com/groups/gitlab-org/-/epics/15957) in GitLab 18.3. Feature flag `custom_admin_roles` enabled by default.

{{< /history >}}

You can create a [custom role](_index.md) by adding one or more custom permissions to a base role.

{{< alert type="note" >}}

Some permissions depend on other permissions.
For example, the `admin_vulnerability` permission requires you to also include the `read_vulnerability` permission.
Any dependencies are noted in the `Description` column for each permission.

{{< /alert >}}
<% custom_abilities_by_feature_category.sort.each do |category, abilities| %>

## <%= "#{humanize(category)}" %>

| Permission | Description | API Attribute | Scope | Introduced |
|:-----------|:------------|:--------------|:------|:-----------|
<% abilities.each do |name, ability| %>
| <%= ability[:title] %> | <%= ability[:description] %> | <%= "[`#{name}`](#{ability[:introduced_by_mr]})" %> | <%= scope(ability) %> | GitLab <%= "[#{ability[:milestone]}](#{ability[:introduced_by_issue]})" %> |
<% end %>
<% end %>
