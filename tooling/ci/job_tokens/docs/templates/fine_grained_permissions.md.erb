---
stage: Software Supply Chain Security
group: Authorization
info: To determine the technical writer assigned to the Stage/Group associated with this page, see https://handbook.gitlab.com/handbook/product/ux/technical-writing/#assignments
title: Fine-grained permissions for CI/CD job tokens
---

<!--
  This documentation is auto-generated by a Rake task.

  Please do not edit this file directly. To update this file, run:
  `bundle exec rake ci:job_tokens:compile_docs`.

  To make changes to the output of the Rake task,
  edit `tooling/ci/job_tokens/docs/templates/fine_grained_permissions.md.erb`.
-->

{{< details >}}

- Tier: Free, Premium, Ultimate
- Offering: GitLab.com, GitLab Self-Managed, GitLab Dedicated

{{< /details >}}

{{< history >}}

- [Introduced](https://gitlab.com/groups/gitlab-org/-/epics/15234) as an [experiment](../../policy/development_stages_support.md#experiment) in GitLab 17.10.
- [Changed](https://gitlab.com/groups/gitlab-org/-/epics/16199) from experiment to beta in GitLab 18.0.
- [Generally available](https://gitlab.com/groups/gitlab-org/-/epics/15258) in GitLab 18.3.

{{< /history >}}

You can use fine-grained permissions to explicitly allow access to a limited set of API endpoints.
These permissions are applied to the CI/CD job tokens in a specified project.

## Add fine-grained permissions to the job token allowlist

Prerequisites:

- You must have at least the Maintainer role for the project.
- You must enable the use of fine-grained permissions for a project.

You can add fine-grained permissions to groups and projects on your job token allowlist. This allows
them to use job tokens to access specific project resources and more accurately control which
resources are available to these groups and projects.

To add fine-grained permissions to groups or projects on the job token allowlist:

1. On the left sidebar, select **Search or go to** and find your project.
1. Select **Settings > CI/CD**.
1. Expand **Job token permissions**.
1. In the **CI/CD job token allowlist** section, select **Add**.
1. From the dropdown list, select **Group or project**.
1. Enter the path to an existing group or project.
1. Select **Fine-grained permissions**.
1. Grant permissions to the [available API endpoints](#available-api-endpoints).
1. Select **Add**.

GitLab adds the group or project to the job token allowlist with the specified permissions. The group or project can now
access any allowed resources in the current project.

## Available API endpoints

The following endpoints are available for CI/CD job tokens.

`None` means fine-grained permissions cannot control access to this endpoint.

<%= allowed_endpoints %>
