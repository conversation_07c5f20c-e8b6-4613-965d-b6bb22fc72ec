ARG MISE_DATA_DIR=/home/<USER>/.local/share/mise
ARG GDK_DIR=/home/<USER>/gitlab-development-kit

ARG GDK_SHA=5eaaeced17b6aeaebd487212b408b145904f084b
# Use tag prefix when running on 'stable' branch to make sure 'protected' image is used which is not deleted by registry cleanup
ARG GDK_BASE_TAG_PREFIX

FROM registry.gitlab.com/gitlab-org/gitlab-development-kit/mise-bootstrapped-verify/main:${GDK_BASE_TAG_PREFIX}${GDK_SHA} AS base

ARG MISE_DATA_DIR
ARG GDK_DIR
ENV GITLAB_LICENSE_MODE=test \
    ********************* \
    MISE_DATA_DIR=${MISE_DATA_DIR} \
    MISE_TRUSTED_CONFIG_PATHS=${GDK_DIR} \
    GITLAB_ROOT_PASSWORD=5iveL!fe

# Disable idiomatic version files like .ruby-version by default and only rely on .tool-versions
RUN mise settings add idiomatic_version_file_enable_tools "[]"

# Clone GDK at specific sha
#
ARG GDK_SHA
RUN set -eux; \
    git clone --depth 1 https://gitlab.com/gitlab-org/gitlab-development-kit.git && cd gitlab-development-kit; \
    git fetch --depth 1 origin ${GDK_SHA} && git -c advice.detachedHead=false checkout ${GDK_SHA}; \
    mkdir gitlab

WORKDIR ${GDK_DIR}

COPY --chown=gdk:gdk qa/gdk/gdk.yml ./

# Bootstrap gdk
RUN set -eux; \
    make bootstrap \
    && sudo apt-get autoclean \
    && rm -rf ${MISE_DATA_DIR}/downloads \
    && rm -rf ${MISE_DATA_DIR}/installs/ruby/*/lib/ruby/gems/*/cache

# Build gitlab-shell
#
FROM base AS gitlab-shell

COPY --chown=gdk:gdk GITLAB_SHELL_VERSION ./gitlab/
RUN make gitlab-shell-setup \
    && cd gitlab-shell \
    && go clean -cache -modcache -r

# Build gitlab-workhorse
#
FROM base AS workhorse

COPY --chown=gdk:gdk VERSION GITLAB_WORKHORSE_VERSION ./gitlab/
COPY --chown=gdk:gdk workhorse ./gitlab/workhorse
RUN make ********************** \
    && cd gitlab/workhorse \
    && go clean -cache -modcache -r

# Build gitaly
#
FROM base AS gitaly
COPY --chown=gdk:gdk GITALY_SERVER_VERSION ./gitlab/
RUN set -eux; \
    make gitaly-setup; \
    cd gitaly \
    && go clean -cache -modcache -r \
    && rm -rf _build/cache \
    _build/deps \
    _build/intermediate

# Install gitlab gem dependencies
#
FROM base AS gitlab-gems

COPY --chown=gdk:gdk Gemfile Gemfile.lock .tool-versions ./gitlab/
COPY --chown=gdk:gdk vendor/gems/ ./gitlab/vendor/gems/
COPY --chown=gdk:gdk gems/ ./gitlab/gems/
RUN set -eux; \
    make gitlab-asdf-install \
    && make .gitlab-bundle \
    && rm -rf ${MISE_DATA_DIR}/installs/ruby/*/lib/ruby/gems/*/cache

# Install gitlab npm dependencies
#
FROM base AS gitlab-node-modules

COPY --chown=gdk:gdk package.json yarn.lock .tool-versions ./gitlab/
COPY --chown=gdk:gdk scripts/frontend/postinstall.js ./gitlab/scripts/frontend/postinstall.js
COPY --chown=gdk:gdk scripts/frontend/preinstall.mjs ./gitlab/scripts/frontend/preinstall.mjs
RUN make .gitlab-yarn && yarn cache clean

# Build gitlab-topology-service
#
FROM base AS gitlab-topology-service
RUN make gitlab-**********************

# Build gitlab-http-router
#
FROM base AS gitlab-http-router
RUN make gitlab-http-router-setup

# Build final image
#
FROM base AS gdk

ARG GDK_DIR

# Set global defaults so we can initialize empty git repo
RUN git config --global init.defaultBranch master \
    && git config --global user.email "<EMAIL>" \
    && git config --global user.name "gdk"

# Copy all components from separate docker stages
COPY --from=gitlab-shell --chown=gdk:gdk ${GDK_DIR}/gitlab-shell ./gitlab-shell/
COPY --from=gitlab-http-router --chown=gdk:gdk ${GDK_DIR}/gitlab-http-router ./gitlab-http-router
COPY --from=gitaly --chown=gdk:gdk ${GDK_DIR}/gitaly ./gitaly/
COPY --from=workhorse --chown=gdk:gdk ${GDK_DIR}/gitlab/workhorse ./gitlab/workhorse/
COPY --from=gitlab-node-modules --chown=gdk:gdk ${GDK_DIR}/gitlab/node_modules ./gitlab/node_modules/
# TODO: Check if skipping setup can be added same way as gitaly and other services, otherwise 'make all' rebuilds topology-service on all rails code changes
# https://gitlab.com/gitlab-org/gitlab-development-kit/-/issues/2802
COPY --from=gitlab-topology-service --chown=gdk:gdk ${GDK_DIR}/gitlab-topology-service ./gitlab-topology-service
# Copy all mise tools and gems for main rails app
COPY --from=gitlab-gems --chown=gdk:gdk ${MISE_DATA_DIR}/installs/ ${MISE_DATA_DIR}/installs/
COPY --from=gitlab-gems --chown=gdk:gdk ${MISE_DATA_DIR}/shims/ ${MISE_DATA_DIR}/shims/
COPY --from=gitlab-gems --chown=gdk:gdk ${MISE_DATA_DIR}/plugins/ ${MISE_DATA_DIR}/plugins/

# Copy code
COPY --chown=gdk:gdk ./ ./gitlab/
COPY --chown=gdk:gdk qa/gdk/entrypoint ../

# Create custom hook for E2E tests
RUN mkdir -p ${GDK_DIR}/gitaly-custom-hooks/pre-receive.d
COPY --chown=gdk:gdk --chmod=700 qa/gdk/pre-receive ${GDK_DIR}/gitaly-custom-hooks/pre-receive.d

# Set up GDK
RUN set -eux; \
    # We need to init git repository within docker build because external .git folder
    # will always invalidate cache on 'COPY --chown=gdk:gdk ./ ./gitlab/' step and some gdk setup steps require gitlab
    # to be an actual git repository
    (cd gitlab && git init . && git add --all && git commit --quiet -m "Init repository") &> /dev/null; \
    gdk config set gitaly.skip_setup true \
    && gdk config set workhorse.skip_setup true \
    && gdk config set gitlab_shell.skip_setup true \
    && make redis/redis.conf all \
    && gdk kill \
    && rm -rf ./gitlab/.git

ENTRYPOINT [ "/home/<USER>/entrypoint" ]
CMD [ "gdk", "tail" ]

HEALTHCHECK --interval=10s --timeout=1s --start-period=5s --retries=17 \
    CMD curl --fail http://gdk.test:3000/users/sign_in || exit 1

EXPOSE 3000
