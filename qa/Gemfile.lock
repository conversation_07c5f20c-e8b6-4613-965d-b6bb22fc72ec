PATH
  remote: ../gems/gitlab-utils
  specs:
    gitlab-utils (0.1.0)
      actionview (>= *******)
      activesupport (>= *******)
      addressable (~> 2.8)
      rake (~> 13.0)

PATH
  remote: gems/gitlab-orchestrator
  specs:
    gitlab-orchestrator (0.0.1)
      activesupport (>= *******)
      rainbow (~> 3.1)
      require_all (~> 3.0)
      thor (~> 1.3)
      tty-prompt (~> 0.23.1)
      tty-spinner (~> 0.9.3)
      tty-which (~> 0.5.0)

GEM
  remote: https://rubygems.org/
  specs:
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      mutex_m
      securerandom (>= 0.3)
      tzinfo (~> 2.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    allure-rspec (2.27.0)
      allure-ruby-commons (= 2.27.0)
      rspec-core (>= 3.8, < 4)
    allure-ruby-commons (2.27.0)
      mime-types (>= 3.3, < 4)
      require_all (>= 2, < 4)
      rspec-expectations (~> 3.12)
    amatch (0.4.1)
      mize
      tins (~> 1.0)
    ast (2.4.2)
    base64 (0.2.0)
    benchmark (0.4.0)
    bigdecimal (3.1.9)
    binding_of_caller (1.0.0)
      debug_inspector (>= 0.0.1)
    builder (3.3.0)
    byebug (12.0.0)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    capybara-screenshot (1.0.26)
      capybara (>= 1.0, < 4)
      launchy
    coderay (1.1.2)
    concurrent-ruby (1.2.3)
    connection_pool (2.5.0)
    crass (1.0.6)
    csv (3.3.0)
    debug_inspector (1.1.0)
    declarative (0.0.20)
    deprecation_toolkit (2.2.3)
      activesupport (>= 6.1)
    diff-lcs (1.3)
    domain_name (0.6.20240107)
    drb (2.2.1)
    erubi (1.12.0)
    excon (1.2.7)
      logger
    factory_bot (6.5.1)
      activesupport (>= 6.1.0)
    faker (3.5.2)
      i18n (>= 1.8.11, < 2)
    faraday (2.9.2)
      faraday-net_http (>= 2.0, < 3.2)
    faraday-net_http (3.1.0)
      net-http
    faraday-retry (2.3.2)
      faraday (~> 2.0)
    ffi (1.17.0)
    ffi-compiler (1.0.1)
      ffi (>= 1.0.0)
      rake
    fog-core (2.6.0)
      builder
      excon (~> 1.0)
      formatador (>= 0.2, < 2.0)
      mime-types
    fog-google (1.25.0)
      addressable (>= 2.7.0)
      fog-core (~> 2.5)
      fog-json (~> 1.2)
      fog-xml (~> 0.1.0)
      google-apis-compute_v1 (~> 0.53)
      google-apis-dns_v1 (~> 0.28)
      google-apis-iamcredentials_v1 (~> 0.15)
      google-apis-monitoring_v3 (~> 0.37)
      google-apis-pubsub_v1 (~> 0.30)
      google-apis-sqladmin_v1beta4 (~> 0.38)
      google-apis-storage_v1 (>= 0.19, < 1)
      google-cloud-env (>= 1.2, < 3.0)
    fog-json (1.2.0)
      fog-core
      multi_json (~> 1.10)
    fog-xml (0.1.4)
      fog-core
      nokogiri (>= 1.5.11, < 2.0.0)
    formatador (0.3.0)
    gitlab (4.19.0)
      httparty (~> 0.20)
      terminal-table (>= 1.5.1)
    gitlab-qa (15.5.0)
      activesupport (>= 6.1, < 7.2)
      ffi (~> 1.17)
      gitlab (~> 4.19)
      http (~> 5.0)
      nokogiri (~> 1.10)
      parallel (>= 1, < 2)
      rainbow (>= 3, < 4)
      table_print (= 1.5.7)
      zeitwerk (>= 2, < 3)
    gitlab_quality-test_tooling (2.19.1)
      activesupport (>= 7.0, < 7.3)
      amatch (~> 0.4.1)
      fog-google (~> 1.24, >= 1.24.1)
      gitlab (>= 4.19, < 7.0)
      http (~> 5.0)
      influxdb-client (~> 3.1)
      nokogiri (~> 1.10)
      parallel (>= 1, < 2)
      rainbow (>= 3, < 4)
      rspec-parameterized (>= 1.0, < 3.0)
      table_print (= 1.5.7)
      zeitwerk (>= 2, < 3)
    google-apis-compute_v1 (0.101.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-core (0.15.0)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (~> 1.9)
      httpclient (>= 2.8.1, < 3.a)
      mini_mime (~> 1.0)
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
      rexml
    google-apis-dns_v1 (0.43.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-iamcredentials_v1 (0.21.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-monitoring_v3 (0.64.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-pubsub_v1 (0.53.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-sqladmin_v1beta4 (0.73.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-storage_v1 (0.40.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-cloud-env (2.3.1)
      base64 (~> 0.2)
      faraday (>= 1.0, < 3.a)
    google-logging-utils (0.2.0)
    googleauth (1.14.0)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.2)
      google-logging-utils (~> 0.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    http (5.1.1)
      addressable (~> 2.8)
      http-cookie (~> 1.0)
      http-form_data (~> 2.2)
      llhttp-ffi (~> 0.4.0)
    http-accept (1.7.0)
    http-cookie (1.0.5)
      domain_name (~> 0.5)
    http-form_data (2.3.0)
    httparty (0.21.0)
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    httpclient (2.8.3)
    i18n (1.14.1)
      concurrent-ruby (~> 1.0)
    influxdb-client (3.2.0)
      csv
    junit_merge (0.1.2)
      nokogiri (>= 1.5, < 2.0)
    jwt (2.8.2)
      base64
    knapsack (4.0.0)
      rake
    launchy (2.5.0)
      addressable (~> 2.7)
    llhttp-ffi (0.4.0)
      ffi-compiler (~> 1.0)
      rake (~> 13.0)
    logger (1.6.0)
    loofah (2.21.3)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    matrix (0.4.2)
    method_source (1.0.0)
    mime-types (3.5.2)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2024.0604)
    mini_mime (1.1.5)
    mini_portile2 (2.8.7)
    minitest (5.25.4)
    mize (0.6.1)
    multi_json (1.15.0)
    multi_xml (0.6.0)
    mutex_m (0.3.0)
    net-http (0.4.1)
      uri
    netrc (0.11.0)
    nokogiri (1.18.9)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    octokit (9.2.0)
      faraday (>= 1, < 3)
      sawyer (~> 0.9)
    os (1.1.4)
    parallel (1.27.0)
    parallel_tests (5.4.0)
      parallel
    parser (*******)
      ast (~> 2.4.1)
      racc
    pastel (0.8.0)
      tty-color (~> 0.5)
    prism (1.4.0)
    proc_to_ast (0.2.0)
      parser
      rouge
      unparser
    pry (0.14.1)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-byebug (3.11.0)
      byebug (~> 12.0)
      pry (>= 0.13, < 0.16)
    public_suffix (6.0.0)
    racc (1.8.1)
    rack (2.2.17)
    rack-test (1.1.0)
      rack (>= 1.0, < 3)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.1)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rainbow (3.1.1)
    rake (13.3.0)
    regexp_parser (2.1.1)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    require_all (3.0.0)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    retriable (3.1.2)
    rexml (3.3.9)
    rotp (6.3.0)
    rouge (4.5.2)
    rspec (3.13.1)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.0)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-parameterized (2.0.0)
      rspec-parameterized-core (>= 2, < 3)
      rspec-parameterized-table_syntax (>= 2, < 3)
    rspec-parameterized-core (2.0.0)
      parser
      prism
      proc_to_ast (>= 0.2.0)
      rspec (>= 2.13, < 4)
      unparser
    rspec-parameterized-table_syntax (2.0.0)
      binding_of_caller
      rspec-parameterized-core (>= 2, < 3)
    rspec-support (3.13.0)
    rspec_junit_formatter (0.6.0)
      rspec-core (>= 2, < 4, != 2.12.0)
    ruby-debug-ide (0.7.5)
      rake (>= 0.8.1)
    rubyzip (2.3.2)
    sawyer (0.9.2)
      addressable (>= 2.3.5)
      faraday (>= 0.17.3, < 3)
    securerandom (0.4.1)
    selenium-webdriver (4.35.0)
      base64 (~> 0.2)
      logger (~> 1.4)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 4.0)
      websocket (~> 1.0)
    signet (0.19.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    slack-notifier (2.4.0)
    sync (0.5.0)
    table_print (1.5.7)
    terminal-table (4.0.0)
      unicode-display_width (>= 1.1.1, < 4)
    thor (1.3.1)
    tins (1.38.0)
      bigdecimal
      sync
    trailblazer-option (0.1.2)
    tty-color (0.6.0)
    tty-cursor (0.7.1)
    tty-prompt (0.23.1)
      pastel (~> 0.8)
      tty-reader (~> 0.8)
    tty-reader (0.9.0)
      tty-cursor (~> 0.7)
      tty-screen (~> 0.8)
      wisper (~> 2.0)
    tty-screen (0.8.2)
    tty-spinner (0.9.3)
      tty-cursor (~> 0.7)
    tty-which (0.5.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    unicode-display_width (2.5.0)
    unparser (0.6.5)
      diff-lcs (~> 1.3)
      parser (>= 3.1.0)
    uri (0.13.0)
    warning (1.5.0)
    websocket (1.2.10)
    wisper (2.0.1)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.7.3)

PLATFORMS
  ruby

DEPENDENCIES
  activesupport (~> *******)
  allure-rspec (~> 2.27.0)
  capybara (~> 3.40.0)
  capybara-screenshot (~> 1.0.26)
  deprecation_toolkit (~> 2.2.3)
  factory_bot (~> 6.5.1)
  faker (~> 3.5, >= 3.5.2)
  faraday-retry (~> 2.3, >= 2.3.2)
  fog-google (~> 1.25)
  gitlab-orchestrator!
  gitlab-qa (~> 15, >= 15.5.0)
  gitlab-utils!
  gitlab_quality-test_tooling (~> 2.19.1)
  influxdb-client (~> 3.2)
  junit_merge (~> 0.1.2)
  knapsack (~> 4.0)
  nokogiri (~> 1.18, >= 1.18.9)
  octokit (~> 9.2.0)
  parallel (~> 1.27)
  parallel_tests (~> 5.4)
  pry-byebug (~> 3.11.0)
  rainbow (~> 3.1.1)
  rake (~> 13, >= 13.3.0)
  rest-client (~> 2.1.0)
  rotp (~> 6.3.0)
  rspec (~> 3.13, >= 3.13.1)
  rspec-parameterized (~> 2.0.0)
  rspec_junit_formatter (~> 0.6.0)
  ruby-debug-ide (~> 0.7.5)
  selenium-webdriver (= 4.35.0)
  slack-notifier (~> 2.4)
  terminal-table (~> 4.0.0)
  warning (~> 1.5)
  zeitwerk (~> 2.7, >= 2.7.3)

BUNDLED WITH
   2.7.1
