<body>
  <meta charset="utf-8" />
  <meta content="IE=edge" http-equiv="X-UA-Compatible" />
  <meta content="width=device-width, initial-scale=1" name="viewport" />
  <title>Merge requests · <PERSON> / Merge-requests-project Name · GitLab</title>

  <meta content="object" property="og:type" />
  <meta content="GitLab" property="og:site_name" />
  <meta
    content="Merge requests · <PERSON> / Merge-requests-project Name · GitLab"
    property="og:title"
  />
  <meta content="GitLab Enterprise Edition" property="og:description" />
  <meta
    content="http://test.host/assets/twitter_card-570ddb06edf56a2312253c5872489847a0f385112ddbcd71ccfa1570febab5d2.jpg"
    property="og:image"
  />
  <meta content="64" property="og:image:width" />
  <meta content="64" property="og:image:height" />
  <meta
    content="http://test.host/frontend-fixtures/merge-requests-project/-/merge_requests"
    property="og:url"
  />
  <meta content="summary" property="twitter:card" />
  <meta
    content="Merge requests · <PERSON>42 / Merge-requests-project Name · GitLab"
    property="twitter:title"
  />
  <meta content="GitLab Enterprise Edition" property="twitter:description" />
  <meta
    content="http://test.host/assets/twitter_card-570ddb06edf56a2312253c5872489847a0f385112ddbcd71ccfa1570febab5d2.jpg"
    property="twitter:image"
  />

  <meta name="csp-nonce" content="YvN7ALTNcueZIIqujkPQQw==" />
  <meta name="action-cable-url" content="http://test.host/-/cable" />

  <meta content="GitLab Enterprise Edition" name="description" />
  <meta content="#ececef" name="theme-color" />

  <div class="layout-page page-with-super-sidebar">
    <aside
      class="js-super-sidebar super-sidebar super-sidebar-loading"
      data-command-palette='{"project_files_url":"/frontend-fixtures/merge-requests-project/-/files/master?format=json","project_blob_url":"/frontend-fixtures/merge-requests-project/-/blob/master"}'
      data-force-desktop-expanded-sidebar=""
      data-is-saas="false"
      data-root-path="http://test.host/"
      data-sidebar='{"whats_new_most_recent_release_items_count":3,"whats_new_version_digest":"0dc755729105d759eb626954bd82029a9f94aed1c747983d4f27a0d7ade59e57","is_logged_in":true,"context_switcher_links":[{"title":"Your work","link":"/","icon":"work"},{"title":"Explore","link":"/explore","icon":"compass"},{"title":"Profile","link":"/-/user_settings/profile","icon":"profile"},{"title":"Preferences","link":"/-/profile/preferences","icon":"preferences"}],"current_menu_items":[{"id":"project_overview","title":"Merge-requests-project Name","entity_id":18,"link":"/frontend-fixtures/merge-requests-project","link_classes":"shortcuts-project","is_active":false},{"id":"manage_menu","title":"Manage","icon":"users","avatar_shape":"rect","link":"/frontend-fixtures/merge-requests-project/activity","is_active":false,"items":[{"id":"activity","title":"Activity","link":"/frontend-fixtures/merge-requests-project/activity","link_classes":"shortcuts-project-activity","is_active":false},{"id":"members","title":"Members","link":"/frontend-fixtures/merge-requests-project/-/project_members","is_active":false},{"id":"labels","title":"Labels","link":"/frontend-fixtures/merge-requests-project/-/labels","is_active":false}],"separated":false},{"id":"plan_menu","title":"Plan","icon":"planning","avatar_shape":"rect","link":"/frontend-fixtures/merge-requests-project/-/issues","is_active":false,"items":[{"id":"project_issue_list","title":"Issues","link":"/frontend-fixtures/merge-requests-project/-/issues","pill_count_field":"openIssuesCount","link_classes":"shortcuts-issues has-sub-items","is_active":false},{"id":"boards","title":"Issue boards","link":"/frontend-fixtures/merge-requests-project/-/boards","link_classes":"shortcuts-issue-boards","is_active":false},{"id":"milestones","title":"Milestones","link":"/frontend-fixtures/merge-requests-project/-/milestones","is_active":false},{"id":"project_wiki","title":"Wiki","link":"/frontend-fixtures/merge-requests-project/-/wikis/home","link_classes":"shortcuts-wiki","is_active":false}],"separated":false},{"id":"code_menu","title":"Code","icon":"code","avatar_shape":"rect","link":"/frontend-fixtures/merge-requests-project/-/merge_requests","is_active":true,"items":[{"id":"project_merge_request_list","title":"Merge requests","link":"/frontend-fixtures/merge-requests-project/-/merge_requests","pill_count_field":"openMergeRequestsCount","link_classes":"shortcuts-merge_requests","is_active":true},{"id":"files","title":"Repository","link":"/frontend-fixtures/merge-requests-project/-/tree/master","link_classes":"shortcuts-tree","is_active":false},{"id":"branches","title":"Branches","link":"/frontend-fixtures/merge-requests-project/-/branches","is_active":false},{"id":"commits","title":"Commits","link":"/frontend-fixtures/merge-requests-project/-/commits/master?ref_type=heads","link_classes":"shortcuts-commits","is_active":false},{"id":"tags","title":"Tags","link":"/frontend-fixtures/merge-requests-project/-/tags","is_active":false},{"id":"graphs","title":"Repository graph","link":"/frontend-fixtures/merge-requests-project/-/network/master?ref_type=heads","link_classes":"shortcuts-network","is_active":false},{"id":"compare","title":"Compare revisions","link":"/frontend-fixtures/merge-requests-project/-/compare?from=master\u0026to=master","is_active":false},{"id":"project_snippets","title":"Snippets","link":"/frontend-fixtures/merge-requests-project/-/snippets","link_classes":"shortcuts-snippets","is_active":false}],"separated":false},{"id":"build_menu","title":"Build","icon":"rocket","avatar_shape":"rect","link":"/frontend-fixtures/merge-requests-project/-/pipelines","is_active":false,"items":[{"id":"pipelines","title":"Pipelines","link":"/frontend-fixtures/merge-requests-project/-/pipelines","link_classes":"shortcuts-pipelines","is_active":false},{"id":"jobs","title":"Jobs","link":"/frontend-fixtures/merge-requests-project/-/jobs","link_classes":"shortcuts-builds","is_active":false},{"id":"pipelines_editor","title":"Pipeline editor","link":"/frontend-fixtures/merge-requests-project/-/ci/editor?branch_name=master","is_active":false},{"id":"pipeline_schedules","title":"Pipeline schedules","link":"/frontend-fixtures/merge-requests-project/-/pipeline_schedules","link_classes":"shortcuts-builds","is_active":false},{"id":"artifacts","title":"Artifacts","link":"/frontend-fixtures/merge-requests-project/-/artifacts","link_classes":"shortcuts-builds","is_active":false}],"separated":false},{"id":"secure_menu","title":"Secure","icon":"shield","avatar_shape":"rect","link":"/frontend-fixtures/merge-requests-project/-/audit_events","is_active":false,"items":[{"id":"audit_events","title":"Audit events","link":"/frontend-fixtures/merge-requests-project/-/audit_events","is_active":false},{"id":"configuration","title":"Security configuration","link":"/frontend-fixtures/merge-requests-project/-/security/configuration","is_active":false}],"separated":false},{"id":"deploy_menu","title":"Deploy","icon":"deployments","avatar_shape":"rect","link":"/frontend-fixtures/merge-requests-project/-/releases","is_active":false,"items":[{"id":"releases","title":"Releases","link":"/frontend-fixtures/merge-requests-project/-/releases","link_classes":"shortcuts-deployments-releases","is_active":false},{"id":"feature_flags","title":"Feature flags","link":"/frontend-fixtures/merge-requests-project/-/feature_flags","link_classes":"shortcuts-feature-flags","is_active":false},{"id":"packages_registry","title":"Package Registry","link":"/frontend-fixtures/merge-requests-project/-/packages","link_classes":"shortcuts-container-registry","is_active":false},{"id":"model_registry","title":"Model registry","link":"/frontend-fixtures/merge-requests-project/-/ml/models","is_active":false}],"separated":false},{"id":"operations_menu","title":"Operate","icon":"cloud-pod","avatar_shape":"rect","link":"/frontend-fixtures/merge-requests-project/-/environments","is_active":false,"items":[{"id":"environments","title":"Environments","link":"/frontend-fixtures/merge-requests-project/-/environments","link_classes":"shortcuts-environments","is_active":false},{"id":"kubernetes","title":"Kubernetes clusters","link":"/frontend-fixtures/merge-requests-project/-/clusters","link_classes":"shortcuts-kubernetes","is_active":false},{"id":"terraform_states","title":"Terraform states","link":"/frontend-fixtures/merge-requests-project/-/terraform","is_active":false},{"id":"infrastructure_registry","title":"Terraform modules","link":"/frontend-fixtures/merge-requests-project/-/terraform_module_registry","is_active":false},{"id":"google_cloud","title":"Google Cloud","link":"/frontend-fixtures/merge-requests-project/-/google_cloud/configuration","is_active":false},{"id":"aws","title":"AWS","link":"/frontend-fixtures/merge-requests-project/-/aws","is_active":false}],"separated":false},{"id":"monitor_menu","title":"Monitor","icon":"monitor","avatar_shape":"rect","link":"/frontend-fixtures/merge-requests-project/-/error_tracking","is_active":false,"items":[{"id":"error_tracking","title":"Error Tracking","link":"/frontend-fixtures/merge-requests-project/-/error_tracking","is_active":false},{"id":"alert_management","title":"Alerts","link":"/frontend-fixtures/merge-requests-project/-/alert_management","is_active":false},{"id":"incidents","title":"Incidents","link":"/frontend-fixtures/merge-requests-project/-/incidents","is_active":false}],"separated":false},{"id":"analyze_menu","title":"Analyze","icon":"chart","avatar_shape":"rect","link":"/frontend-fixtures/merge-requests-project/-/value_stream_analytics","is_active":false,"items":[{"id":"cycle_analytics","title":"Value stream analytics","link":"/frontend-fixtures/merge-requests-project/-/value_stream_analytics","link_classes":"shortcuts-project-cycle-analytics","is_active":false},{"id":"contributors","title":"Contributor analytics","link":"/frontend-fixtures/merge-requests-project/-/graphs/master?ref_type=heads","is_active":false},{"id":"ci_cd_analytics","title":"CI/CD analytics","link":"/frontend-fixtures/merge-requests-project/-/pipelines/charts","is_active":false},{"id":"repository_analytics","title":"Repository analytics","link":"/frontend-fixtures/merge-requests-project/-/graphs/master/charts","link_classes":"shortcuts-repository-charts","is_active":false},{"id":"model_experiments","title":"Model experiments","link":"/frontend-fixtures/merge-requests-project/-/ml/experiments","is_active":false}],"separated":false},{"id":"settings_menu","title":"Settings","icon":"settings","avatar_shape":"rect","link":"/frontend-fixtures/merge-requests-project/edit","is_active":false,"items":[{"id":"general","title":"General","link":"/frontend-fixtures/merge-requests-project/edit","is_active":false},{"id":"integrations","title":"Integrations","link":"/frontend-fixtures/merge-requests-project/-/settings/integrations","is_active":false},{"id":"webhooks","title":"Webhooks","link":"/frontend-fixtures/merge-requests-project/-/hooks","is_active":false},{"id":"access_tokens","title":"Access tokens","link":"/frontend-fixtures/merge-requests-project/-/settings/access_tokens","is_active":false},{"id":"repository","title":"Repository","link":"/frontend-fixtures/merge-requests-project/-/settings/repository","is_active":false},{"id":"merge_request_settings","title":"Merge requests","link":"/frontend-fixtures/merge-requests-project/-/settings/merge_requests","is_active":false},{"id":"ci_cd","title":"CI/CD","link":"/frontend-fixtures/merge-requests-project/-/settings/ci_cd","is_active":false},{"id":"packages_and_registries","title":"Packages and registries","link":"/frontend-fixtures/merge-requests-project/-/settings/packages_and_registries","is_active":false},{"id":"monitor","title":"Monitor","link":"/frontend-fixtures/merge-requests-project/-/settings/operations","is_active":false},{"id":"usage_quotas","title":"Usage quotas","link":"/frontend-fixtures/merge-requests-project/-/usage_quotas","is_active":false}],"separated":true}],"current_context_header":"Project","support_path":"https://about.gitlab.com/get-help/","docs_path":"/help/docs","display_whats_new":true,"show_version_check":false,"search":{"search_path":"/search","issues_path":"/dashboard/issues","mr_path":"/dashboard/merge_requests","autocomplete_path":"/search/autocomplete","settings_path":"/search/settings","search_context":{"project":{"id":18,"name":"Merge-requests-project Name"},"project_metadata":{"mr_path":"/frontend-fixtures/merge-requests-project/-/merge_requests","issues_path":"/frontend-fixtures/merge-requests-project/-/issues"},"code_search":false,"scope":"merge_requests","for_snippets":null}},"panel_type":"project","shortcut_links":[{"title":"Milestones","href":"/dashboard/milestones","css_class":"dashboard-shortcuts-milestones"},{"title":"Snippets","href":"/dashboard/snippets","css_class":"dashboard-shortcuts-snippets"},{"title":"Activity","href":"/dashboard/activity","css_class":"dashboard-shortcuts-activity"},{"title":"Groups","href":"/dashboard/groups","css_class":"dashboard-shortcuts-groups"},{"title":"Projects","href":"/dashboard/projects","css_class":"dashboard-shortcuts-projects"},{"title":"Create a new issue","href":"/frontend-fixtures/merge-requests-project/-/issues/new","css_class":"shortcuts-new-issue"}],"terms":null,"is_admin":false,"name":"Sidney Jones42","username":"frontend-fixtures","admin_url":"http://test.host/admin","admin_mode":{"admin_mode_feature_enabled":true,"admin_mode_active":false,"enter_admin_mode_url":"/admin/session/new","leave_admin_mode_url":"/admin/session/destroy","user_is_admin":false},"avatar_url":"https://www.gravatar.com/avatar/d3141e10fa3afaf0a6ecde38bcacf66bc03908a2114f4f4259dc5e600f3b37d0?s=80\u0026d=identicon","has_link_to_profile":true,"link_to_profile":"/frontend-fixtures","logo_url":null,"status":{"can_update":true,"busy":null,"customized":null,"availability":"","emoji":null,"message_html":null,"message":null,"clear_after":null},"settings":{"has_settings":true,"profile_path":"/-/user_settings/profile","profile_preferences_path":"/-/profile/preferences"},"user_counts":{"assigned_issues":0,"assigned_merge_requests":0,"review_requested_merge_requests":0,"todos":0,"last_update":1435917600000},"can_sign_out":true,"sign_out_link":"/users/sign_out","issues_dashboard_path":"/dashboard/issues?assignee_username=frontend-fixtures","merge_request_dashboard_path":null,"todos_dashboard_path":"/dashboard/todos","create_new_menu_groups":[{"name":"In this project","items":[{"text":"New issue","href":"/frontend-fixtures/merge-requests-project/-/issues/new","component":null,"extraAttrs":{"data-track-label":"new_issue","data-track-action":"click_link","data-track-property":"nav_create_menu","data-testid":"create_menu_item","data-qa-create-menu-item":"new_issue"}},{"text":"New merge request","href":"/frontend-fixtures/merge-requests-project/-/merge_requests/new","component":null,"extraAttrs":{"data-track-label":"new_mr","data-track-action":"click_link","data-track-property":"nav_create_menu","data-testid":"create_menu_item","data-qa-create-menu-item":"new_mr"}},{"text":"New snippet","href":"/frontend-fixtures/merge-requests-project/-/snippets/new","component":null,"extraAttrs":{"data-track-label":"new_snippet","data-track-action":"click_link","data-track-property":"nav_create_menu","data-testid":"create_menu_item","data-qa-create-menu-item":"new_snippet"}},{"text":"Invite members","href":null,"component":"invite_members","extraAttrs":{"data-track-label":"invite","data-track-action":"click_link","data-track-property":"nav_create_menu","data-testid":"create_menu_item","data-qa-create-menu-item":"invite"}}]},{"name":"In GitLab","items":[{"text":"New project/repository","href":"/projects/new","component":null,"extraAttrs":{"data-track-label":"general_new_project","data-track-action":"click_link","data-track-property":"nav_create_menu","data-testid":"create_menu_item","data-qa-create-menu-item":"general_new_project"}},{"text":"New group","href":"/groups/new","component":null,"extraAttrs":{"data-track-label":"general_new_group","data-track-action":"click_link","data-track-property":"nav_create_menu","data-testid":"create_menu_item","data-qa-create-menu-item":"general_new_group"}},{"text":"New organization","href":"/-/organizations/new","component":null,"extraAttrs":{"data-track-label":"general_new_organization","data-track-action":"click_link","data-track-property":"nav_create_menu","data-testid":"create_menu_item","data-qa-create-menu-item":"general_new_organization"}},{"text":"New snippet","href":"/-/snippets/new","component":null,"extraAttrs":{"data-track-label":"general_new_snippet","data-track-action":"click_link","data-track-property":"nav_create_menu","data-testid":"create_menu_item","data-qa-create-menu-item":"general_new_snippet"}}]}],"merge_request_menu":[{"name":"Merge requests","items":[{"text":"Assigned","href":"/dashboard/merge_requests?assignee_username=frontend-fixtures","count":0,"userCount":"assigned_merge_requests","extraAttrs":{"data-track-action":"click_link","data-track-label":"merge_requests_assigned","data-track-property":"nav_core_menu","class":"dashboard-shortcuts-merge_requests"}},{"text":"Review requests","href":"/dashboard/merge_requests?reviewer_username=frontend-fixtures","count":0,"userCount":"review_requested_merge_requests","extraAttrs":{"data-track-action":"click_link","data-track-label":"merge_requests_to_review","data-track-property":"nav_core_menu","class":"dashboard-shortcuts-review_requests"}}]}],"projects_path":"/dashboard/projects","groups_path":"/dashboard/groups","gitlab_com_but_not_canary":false,"gitlab_com_and_canary":false,"canary_toggle_com_url":"https://next.gitlab.com","current_context":{"namespace":"projects","item":{"id":18,"name":"Merge-requests-project Name","namespace":"Sidney Jones42 / Merge-requests-project Name","fullPath":"frontend-fixtures/merge-requests-project","webUrl":"/frontend-fixtures/merge-requests-project","avatarUrl":null}},"pinned_items":["project_issue_list","project_merge_request_list"],"update_pins_url":"/-/users/pins","is_impersonating":false,"stop_impersonation_path":"/admin/impersonation","track_visits_path":"/-/track_namespace_visits","work_items":null,"show_tanuki_bot":false,"trial":{"has_start_trial":false,"url":"/-/trials/new?glm_content=top-right-dropdown\u0026glm_source=gitlab.com"}}'
    ></aside>

    <div class="content-wrapper">
      <div class="broadcast-wrapper"></div>
      <div
        class="alert-wrapper alert-wrapper-top-space container-fluid container-limited gl-flex gl-flex-col gl-gap-3"
      ></div>
      <div class="top-bar-fixed container-fluid" data-testid="top-bar">
        <div class="top-bar-container gl-flex gl-items-center gl-gap-2">
          <div class="gl-flex gl-grow gl-basis-0 gl-items-center gl-justify-start gl-gap-3">
            <button
              class="gl-button btn btn-icon btn-md btn-default btn-default-tertiary js-super-sidebar-toggle-expand super-sidebar-toggle -gl-ml-3"
              aria-controls="super-sidebar"
              aria-expanded="false"
              aria-label="Primary navigation sidebar"
              type="button"
            >
              <svg class="s16 gl-icon gl-button-icon" data-testid="sidebar-icon">
                <use
                  href="http://test.host/assets/icons-aa2c8ddf99d22b77153ca2bb092a23889c12c597fc8b8de94b0f730eb53513f6.svg#sidebar"
                ></use>
              </svg>
            </button>

            <div data-testid="breadcrumb-links" id="js-vue-page-breadcrumbs-wrapper">
              <div
                data-breadcrumbs-json='[{"text":"Sidney Jones42","href":"/frontend-fixtures","avatarPath":null},{"text":"Merge-requests-project Name","href":"/frontend-fixtures/merge-requests-project","avatarPath":null},{"text":"Merge requests","href":"/frontend-fixtures/merge-requests-project/-/merge_requests","avatarPath":null}]'
                id="js-vue-page-breadcrumbs"
              ></div>
              <div id="js-injected-page-breadcrumbs"></div>
            </div>
          </div>
          <div class="gl-flex gl-flex-none gl-items-center gl-justify-center">
            <div id="js-advanced-search-modal"></div>
          </div>
        </div>
      </div>

      <div class="container-fluid container-limited project-highlight-puc">
        <main
          class="content"
          id="content-body"
          itemscope
          itemtype="http://schema.org/SoftwareSourceCode"
        >
          <div class="flash-container flash-container-page sticky" data-testid="flash-container">
            <div id="js-global-alerts"></div>
          </div>

          <div
            class="js-invite-members-modal"
            data-access-levels='{"Guest":10,"Planner":15,"Reporter":20,"Developer":30,"Maintainer":40,"Owner":50}'
            data-default-access-level="10"
            data-full-path="frontend-fixtures/merge-requests-project"
            data-has-gitlab-subscription="false"
            data-help-link="http://test.host/help/user/permissions.md"
            data-id="18"
            data-is-project="true"
            data-name="Merge-requests-project Name"
            data-overage-members-modal-available="false"
            data-reload-page-on-submit="false"
            data-root-id="32"
          ></div>

          <div
            class="js-hand-raise-lead-modal"
            data-submit-path="http://test.host/-/gitlab_subscriptions/hand_raise_leads"
            data-user='{"namespace_id":32,"user_name":"frontend-fixtures","first_name":"Sidney","last_name":"Jones42","company_name":""}'
          ></div>

          <div class="top-area">
            <ul class="issues-state-filters nav gl-tabs-nav gl-grow gl-border-b-0" role="tablist">
              <li role="presentation" class="nav-item">
                <a
                  id="state-opened"
                  title="Filter by merge requests that are currently open."
                  data-state="opened"
                  role="tab"
                  class="nav-link gl-tab-nav-item active gl-tab-nav-item-active"
                  href="http://test.host/frontend-fixtures/merge-requests-project/-/merge_requests?state=opened"
                  ><span>Open</span>
                  <span
                    class="gl-badge badge badge-pill badge-neutral gl-tab-counter-badge gl-hidden sm:gl-inline-flex"
                    ><span class="gl-badge-content">1</span></span
                  >
                </a>
              </li>
              <li role="presentation" class="nav-item">
                <a
                  id="state-merged"
                  title="Filter by merge requests that are currently merged."
                  data-state="merged"
                  role="tab"
                  class="nav-link gl-tab-nav-item"
                  href="http://test.host/frontend-fixtures/merge-requests-project/-/merge_requests?state=merged"
                  ><span>Merged</span>
                  <span
                    class="gl-badge badge badge-pill badge-neutral gl-tab-counter-badge gl-hidden sm:gl-inline-flex"
                    ><span class="gl-badge-content">0</span></span
                  >
                </a>
              </li>
              <li role="presentation" class="nav-item">
                <a
                  id="state-closed"
                  title="Filter by merge requests that are currently closed and unmerged."
                  data-state="closed"
                  role="tab"
                  class="nav-link gl-tab-nav-item"
                  href="http://test.host/frontend-fixtures/merge-requests-project/-/merge_requests?state=closed"
                  ><span>Closed</span>
                  <span
                    class="gl-badge badge badge-pill badge-neutral gl-tab-counter-badge gl-hidden sm:gl-inline-flex"
                    ><span class="gl-badge-content">0</span></span
                  >
                </a>
              </li>
              <li role="presentation" class="nav-item">
                <a
                  id="state-all"
                  title="Show all merge requests."
                  data-state="all"
                  role="tab"
                  class="nav-link gl-tab-nav-item"
                  href="http://test.host/frontend-fixtures/merge-requests-project/-/merge_requests?state=all"
                  ><span>All</span>
                  <span
                    class="gl-badge badge badge-pill badge-neutral gl-tab-counter-badge gl-hidden sm:gl-inline-flex"
                    ><span class="gl-badge-content">1</span></span
                  >
                </a>
              </li>
            </ul>
            <div class="nav-controls">
              <button
                class="gl-button btn btn-md btn-default js-bulk-update-toggle gl-mr-3"
                type="submit"
              >
                <span class="gl-button-text"> Bulk edit </span></button
              ><a
                data-event-tracking="click_new_merge_request_list"
                class="gl-button btn btn-md btn-confirm"
                href="http://test.host/frontend-fixtures/merge-requests-project/-/merge_requests/new"
                ><span class="gl-button-text"> New merge request </span>
              </a>
            </div>
          </div>
          <div class="issues-filters">
            <div
              class="issues-details-filters filtered-search-block row-content-block second-block gl-flex gl-flex-col gl-gap-3 lg:gl-flex-row"
            >
              <div class="gl-flex gl-w-full gl-grow gl-flex-col md:gl-flex-row">
                <form
                  class="filter-form js-filter-form gl-w-full"
                  action="http://test.host/frontend-fixtures/merge-requests-project/-/merge_requests?"
                  accept-charset="UTF-8"
                  method="get"
                >
                  <div
                    class="check-all-holder hidden gl-float-left gl-mr-3 gl-hidden gl-leading-36 sm:gl-block"
                  >
                    <div class="gl-form-checkbox custom-control custom-checkbox">
                      <input
                        type="checkbox"
                        name="check-all-issues"
                        id="check-all-issues"
                        class="custom-control-input"
                      />
                      <label class="custom-control-label" for="check-all-issues"
                        ><span><span class="gl-sr-only"> Select all </span> </span></label
                      >
                    </div>
                  </div>
                  <div
                    class="issues-other-filters filtered-search-wrapper gl-flex gl-flex-col md:gl-flex-row"
                  >
                    <div class="filtered-search-box">
                      <div class="dropdown filtered-search-history-dropdown-wrapper">
                        <button
                          class="dropdown-menu-toggle gl-button btn btn-default filtered-search-history-dropdown-toggle-button"
                          type="button"
                          data-toggle="dropdown"
                        >
                          <span class="dropdown-toggle-text"
                            ><span
                              ><svg class="s16" data-testid="history-icon">
                                <use
                                  href="http://test.host/assets/icons-aa2c8ddf99d22b77153ca2bb092a23889c12c597fc8b8de94b0f730eb53513f6.svg#history"
                                ></use></svg></span
                            ><span class="gl-sr-only">Recent searches</span></span
                          ><svg
                            class="s16 dropdown-menu-toggle-icon"
                            data-testid="chevron-down-icon"
                          >
                            <use
                              href="http://test.host/assets/icons-aa2c8ddf99d22b77153ca2bb092a23889c12c597fc8b8de94b0f730eb53513f6.svg#chevron-down"
                            ></use>
                          </svg>
                        </button>
                        <div class="dropdown-menu dropdown-select filtered-search-history-dropdown">
                          <div
                            data-testid="dropdown-list-content"
                            class="dropdown-content filtered-search-history-dropdown-content"
                          >
                            <div
                              class="js-filtered-search-history-dropdown"
                              data-full-path="frontend-fixtures/merge-requests-project"
                            ></div>
                          </div>
                          <div class="dropdown-loading">
                            <div class="gl-spinner-container gl-mt-7" role="status">
                              <span
                                aria-hidden
                                class="gl-spinner gl-spinner-md gl-spinner-dark !gl-align-text-bottom"
                              ></span
                              ><span class="gl-sr-only !gl-absolute">Loading</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="filtered-search-box-input-container droplab-dropdown">
                        <div class="scroll-container">
                          <ul class="tokens-container list-unstyled">
                            <li class="input-token">
                              <input
                                aria-label="Add search filter"
                                autocomplete="off"
                                class="form-control filtered-search"
                                data-environments-endpoint="http://test.host/frontend-fixtures/merge-requests-project/unfoldered_environment_names"
                                data-labels-endpoint="http://test.host/frontend-fixtures/merge-requests-project/-/labels"
                                data-milestones-endpoint="http://test.host/frontend-fixtures/merge-requests-project/-/milestones"
                                data-project-id="18"
                                data-releases-endpoint="http://test.host/frontend-fixtures/merge-requests-project/-/releases"
                                id="filtered-search-merge_requests"
                                placeholder="Search or filter results…"
                              />
                            </li>
                          </ul>
                        </div>
                        <div
                          class="gl-filtered-search-suggestion-list dropdown-menu"
                          id="js-dropdown-hint"
                        >
                          <ul class="filter-dropdown" data-dropdown data-dynamic>
                            <li
                              class="filter-dropdown-item"
                              data-action="{{hint === 'search' ? 'submit' : '' }}"
                              data-hint="{{hint}}"
                              data-tag="{{tag}}"
                            >
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text gl-inline-flex">
                                  <svg>
                                    <use xlink:href="{{icon}}"></use>
                                  </svg>
                                  {{ formattedKey }}
                                </span>
                              </button>
                            </li>
                          </ul>
                        </div>
                        <div
                          class="gl-filtered-search-suggestion-list dropdown-menu"
                          id="js-dropdown-operator"
                        >
                          <ul class="filter-dropdown" data-dropdown data-dynamic>
                            <li class="filter-dropdown-item" data-value="{{ title }}">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span
                                  class="gl-button-text gl-inline-flex gl-w-full gl-justify-between"
                                >
                                  {{ title }}
                                  <span class="gl-text-subtle"> {{ help }} </span>
                                </span>
                              </button>
                            </li>
                          </ul>
                        </div>
                        <div
                          class="gl-filtered-search-suggestion-list dropdown-menu"
                          id="js-dropdown-author"
                        >
                          <ul data-dropdown>
                            <li class="filter-dropdown-item js-current-user">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text gl-flex gl-items-center">
                                  <div class="gl-shrink-0">
                                    <img
                                      alt="Sidney Jones42's avatar"
                                      src="https://www.gravatar.com/avatar/d3141e10fa3afaf0a6ecde38bcacf66bc03908a2114f4f4259dc5e600f3b37d0?s=128&amp;d=identicon"
                                      class="avatar s32"
                                      title="Sidney Jones42"
                                    />
                                  </div>
                                  <div class="gl-flex gl-flex-col">
                                    <span class="gl-whitespace-normal gl-break-words gl-font-bold">
                                      Sidney Jones42
                                    </span>
                                    <span
                                      class="js-dropdown-light-content gl-whitespace-normal gl-break-all gl-text-subtle"
                                    >
                                      @frontend-fixtures
                                    </span>
                                  </div>
                                </span>
                              </button>
                            </li>
                          </ul>
                          <ul class="filter-dropdown" data-dropdown data-dynamic>
                            <li class="filter-dropdown-item">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text gl-flex gl-items-center">
                                  <div class="gl-shrink-0">
                                    <img
                                      alt="{{name}}'s avatar"
                                      src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
                                      data-src="{{avatar_url}}"
                                      class="avatar s32 lazy"
                                      title="{{name}}"
                                    />
                                  </div>
                                  <div class="gl-flex gl-flex-col">
                                    <span class="gl-whitespace-normal gl-break-words gl-font-bold">
                                      {{name}}
                                    </span>
                                    <span
                                      class="js-dropdown-light-content gl-whitespace-normal gl-break-all gl-text-subtle"
                                    >
                                      @{{username}}
                                    </span>
                                  </div>
                                </span>
                              </button>
                            </li>
                          </ul>
                        </div>
                        <div
                          class="gl-filtered-search-suggestion-list dropdown-menu"
                          id="js-dropdown-assignee"
                        >
                          <ul data-dropdown>
                            <li class="filter-dropdown-item" data-value="None">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> None </span>
                              </button>
                            </li>
                            <li class="filter-dropdown-item" data-value="Any">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> Any </span>
                              </button>
                            </li>
                            <li class="divider droplab-item-ignore"></li>
                            <li class="filter-dropdown-item js-current-user">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text gl-flex gl-items-center">
                                  <div class="gl-shrink-0">
                                    <img
                                      alt="Sidney Jones42's avatar"
                                      src="https://www.gravatar.com/avatar/d3141e10fa3afaf0a6ecde38bcacf66bc03908a2114f4f4259dc5e600f3b37d0?s=128&amp;d=identicon"
                                      class="avatar s32"
                                      title="Sidney Jones42"
                                    />
                                  </div>
                                  <div class="gl-flex gl-flex-col">
                                    <span class="gl-whitespace-normal gl-break-words gl-font-bold">
                                      Sidney Jones42
                                    </span>
                                    <span
                                      class="js-dropdown-light-content gl-whitespace-normal gl-break-all gl-text-subtle"
                                    >
                                      @frontend-fixtures
                                    </span>
                                  </div>
                                </span>
                              </button>
                            </li>
                          </ul>
                          <ul class="filter-dropdown" data-dropdown data-dynamic>
                            <li class="filter-dropdown-item">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text gl-flex gl-items-center">
                                  <div class="gl-shrink-0">
                                    <img
                                      alt="{{name}}'s avatar"
                                      src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
                                      data-src="{{avatar_url}}"
                                      class="avatar s32 lazy"
                                      title="{{name}}"
                                    />
                                  </div>
                                  <div class="gl-flex gl-flex-col">
                                    <span class="gl-whitespace-normal gl-break-words gl-font-bold">
                                      {{name}}
                                    </span>
                                    <span
                                      class="js-dropdown-light-content gl-whitespace-normal gl-break-all gl-text-subtle"
                                    >
                                      @{{username}}
                                    </span>
                                  </div>
                                </span>
                              </button>
                            </li>
                          </ul>
                        </div>
                        <div
                          class="gl-filtered-search-suggestion-list dropdown-menu"
                          id="js-dropdown-reviewer"
                        >
                          <ul data-dropdown>
                            <li class="filter-dropdown-item" data-value="None">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> None </span>
                              </button>
                            </li>
                            <li class="filter-dropdown-item" data-value="Any">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> Any </span>
                              </button>
                            </li>
                            <li class="divider droplab-item-ignore"></li>
                            <li class="filter-dropdown-item js-current-user">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text gl-flex gl-items-center">
                                  <div class="gl-shrink-0">
                                    <img
                                      alt="Sidney Jones42's avatar"
                                      src="https://www.gravatar.com/avatar/d3141e10fa3afaf0a6ecde38bcacf66bc03908a2114f4f4259dc5e600f3b37d0?s=128&amp;d=identicon"
                                      class="avatar s32"
                                      title="Sidney Jones42"
                                    />
                                  </div>
                                  <div class="gl-flex gl-flex-col">
                                    <span class="gl-whitespace-normal gl-break-words gl-font-bold">
                                      Sidney Jones42
                                    </span>
                                    <span
                                      class="js-dropdown-light-content gl-whitespace-normal gl-break-all gl-text-subtle"
                                    >
                                      @frontend-fixtures
                                    </span>
                                  </div>
                                </span>
                              </button>
                            </li>
                          </ul>
                          <ul class="filter-dropdown" data-dropdown data-dynamic>
                            <li class="filter-dropdown-item">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text gl-flex gl-items-center">
                                  <div class="gl-shrink-0">
                                    <img
                                      alt="{{name}}'s avatar"
                                      src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
                                      data-src="{{avatar_url}}"
                                      class="avatar s32 lazy"
                                      title="{{name}}"
                                    />
                                  </div>
                                  <div class="gl-flex gl-flex-col">
                                    <span class="gl-whitespace-normal gl-break-words gl-font-bold">
                                      {{name}}
                                    </span>
                                    <span
                                      class="js-dropdown-light-content gl-whitespace-normal gl-break-all gl-text-subtle"
                                    >
                                      @{{username}}
                                    </span>
                                  </div>
                                </span>
                              </button>
                            </li>
                          </ul>
                        </div>
                        <div
                          class="filtered-search-input-dropdown-menu dropdown-menu"
                          id="js-dropdown-approver"
                        >
                          <ul data-dropdown>
                            <li class="filter-dropdown-item" data-value="None">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> None </span>
                              </button>
                            </li>
                            <li class="filter-dropdown-item" data-value="Any">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> Any </span>
                              </button>
                            </li>
                            <li class="divider droplab-item-ignore"></li>
                            <li class="filter-dropdown-item js-current-user">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text gl-flex gl-items-center">
                                  <div class="gl-shrink-0">
                                    <img
                                      alt="Sidney Jones42's avatar"
                                      src="https://www.gravatar.com/avatar/d3141e10fa3afaf0a6ecde38bcacf66bc03908a2114f4f4259dc5e600f3b37d0?s=128&amp;d=identicon"
                                      class="avatar s32"
                                      title="Sidney Jones42"
                                    />
                                  </div>
                                  <div class="gl-flex gl-flex-col">
                                    <span class="gl-whitespace-normal gl-break-words gl-font-bold">
                                      Sidney Jones42
                                    </span>
                                    <span
                                      class="js-dropdown-light-content gl-whitespace-normal gl-break-all gl-text-subtle"
                                    >
                                      @frontend-fixtures
                                    </span>
                                  </div>
                                </span>
                              </button>
                            </li>
                          </ul>
                          <ul class="filter-dropdown" data-dropdown data-dynamic>
                            <li class="filter-dropdown-item">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text gl-flex gl-items-center">
                                  <div class="gl-shrink-0">
                                    <img
                                      alt="{{name}}'s avatar"
                                      src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
                                      data-src="{{avatar_url}}"
                                      class="avatar s32 lazy"
                                      title="{{name}}"
                                    />
                                  </div>
                                  <div class="gl-flex gl-flex-col">
                                    <span class="gl-whitespace-normal gl-break-words gl-font-bold">
                                      {{name}}
                                    </span>
                                    <span
                                      class="js-dropdown-light-content gl-whitespace-normal gl-break-all gl-text-subtle"
                                    >
                                      @{{username}}
                                    </span>
                                  </div>
                                </span>
                              </button>
                            </li>
                          </ul>
                        </div>

                        <div
                          class="filtered-search-input-dropdown-menu dropdown-menu"
                          id="js-dropdown-approved-by"
                        >
                          <ul data-dropdown>
                            <li class="filter-dropdown-item" data-value="None">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> None </span>
                              </button>
                            </li>
                            <li class="filter-dropdown-item" data-value="Any">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> Any </span>
                              </button>
                            </li>
                            <li class="divider droplab-item-ignore"></li>
                            <li class="filter-dropdown-item js-current-user">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text gl-flex gl-items-center">
                                  <div class="gl-shrink-0">
                                    <img
                                      alt="Sidney Jones42's avatar"
                                      src="https://www.gravatar.com/avatar/d3141e10fa3afaf0a6ecde38bcacf66bc03908a2114f4f4259dc5e600f3b37d0?s=128&amp;d=identicon"
                                      class="avatar s32"
                                      title="Sidney Jones42"
                                    />
                                  </div>
                                  <div class="gl-flex gl-flex-col">
                                    <span class="gl-whitespace-normal gl-break-words gl-font-bold">
                                      Sidney Jones42
                                    </span>
                                    <span
                                      class="js-dropdown-light-content gl-whitespace-normal gl-break-all gl-text-subtle"
                                    >
                                      @frontend-fixtures
                                    </span>
                                  </div>
                                </span>
                              </button>
                            </li>
                          </ul>
                          <ul class="filter-dropdown" data-dropdown data-dynamic>
                            <li class="filter-dropdown-item">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text gl-flex gl-items-center">
                                  <div class="gl-shrink-0">
                                    <img
                                      alt="{{name}}'s avatar"
                                      src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
                                      data-src="{{avatar_url}}"
                                      class="avatar s32 lazy"
                                      title="{{name}}"
                                    />
                                  </div>
                                  <div class="gl-flex gl-flex-col">
                                    <span class="gl-whitespace-normal gl-break-words gl-font-bold">
                                      {{name}}
                                    </span>
                                    <span
                                      class="js-dropdown-light-content gl-whitespace-normal gl-break-all gl-text-subtle"
                                    >
                                      @{{username}}
                                    </span>
                                  </div>
                                </span>
                              </button>
                            </li>
                          </ul>
                        </div>

                        <div
                          class="filtered-search-input-dropdown-menu dropdown-menu"
                          id="js-dropdown-merge-user"
                        >
                          <ul data-dropdown>
                            <li class="filter-dropdown-item js-current-user">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text gl-flex gl-items-center">
                                  <div class="gl-shrink-0">
                                    <img
                                      alt="Sidney Jones42's avatar"
                                      src="https://www.gravatar.com/avatar/d3141e10fa3afaf0a6ecde38bcacf66bc03908a2114f4f4259dc5e600f3b37d0?s=128&amp;d=identicon"
                                      class="avatar s32"
                                      title="Sidney Jones42"
                                    />
                                  </div>
                                  <div class="gl-flex gl-flex-col">
                                    <span class="gl-whitespace-normal gl-break-words gl-font-bold">
                                      Sidney Jones42
                                    </span>
                                    <span
                                      class="js-dropdown-light-content gl-whitespace-normal gl-break-all gl-text-subtle"
                                    >
                                      @frontend-fixtures
                                    </span>
                                  </div>
                                </span>
                              </button>
                            </li>
                          </ul>
                          <ul class="filter-dropdown" data-dropdown data-dynamic>
                            <li class="filter-dropdown-item">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text gl-flex gl-items-center">
                                  <div class="gl-shrink-0">
                                    <img
                                      alt="{{name}}'s avatar"
                                      src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
                                      data-src="{{avatar_url}}"
                                      class="avatar s32 lazy"
                                      title="{{name}}"
                                    />
                                  </div>
                                  <div class="gl-flex gl-flex-col">
                                    <span class="gl-whitespace-normal gl-break-words gl-font-bold">
                                      {{name}}
                                    </span>
                                    <span
                                      class="js-dropdown-light-content gl-whitespace-normal gl-break-all gl-text-subtle"
                                    >
                                      @{{username}}
                                    </span>
                                  </div>
                                </span>
                              </button>
                            </li>
                          </ul>
                        </div>

                        <div
                          class="gl-filtered-search-suggestion-list dropdown-menu"
                          id="js-dropdown-milestone"
                        >
                          <ul data-dropdown>
                            <li class="filter-dropdown-item" data-value="None">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> None </span>
                              </button>
                            </li>
                            <li class="filter-dropdown-item" data-value="Any">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> Any </span>
                              </button>
                            </li>
                            <li class="filter-dropdown-item" data-value="Upcoming">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> Upcoming </span>
                              </button>
                            </li>
                            <li class="filter-dropdown-item" data-value="Started">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> Started </span>
                              </button>
                            </li>
                            <li class="divider droplab-item-ignore"></li>
                          </ul>
                          <ul class="filter-dropdown" data-dropdown data-dynamic>
                            <li class="filter-dropdown-item">
                              <button
                                class="gl-button btn btn-md btn-link js-data-value"
                                type="button"
                              >
                                <span class="gl-button-text"> {{title}} </span>
                              </button>
                            </li>
                          </ul>
                        </div>

                        <div
                          class="gl-filtered-search-suggestion-list dropdown-menu"
                          id="js-dropdown-release"
                        >
                          <ul data-dropdown>
                            <li class="filter-dropdown-item" data-value="None">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> None </span>
                              </button>
                            </li>
                            <li class="filter-dropdown-item" data-value="Any">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> Any </span>
                              </button>
                            </li>
                            <li class="divider droplab-item-ignore"></li>
                          </ul>
                          <ul class="filter-dropdown" data-dropdown data-dynamic>
                            <li class="filter-dropdown-item">
                              <button
                                class="gl-button btn btn-md btn-link js-data-value"
                                type="button"
                              >
                                <span class="gl-button-text"> {{title}} </span>
                              </button>
                            </li>
                          </ul>
                        </div>
                        <div
                          class="gl-filtered-search-suggestion-list dropdown-menu"
                          id="js-dropdown-label"
                        >
                          <ul data-dropdown>
                            <li class="filter-dropdown-item" data-value="None">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> None </span>
                              </button>
                            </li>
                            <li class="filter-dropdown-item" data-value="Any">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> Any </span>
                              </button>
                            </li>
                            <li class="divider droplab-item-ignore"></li>
                          </ul>
                          <ul class="filter-dropdown" data-dropdown data-dynamic>
                            <li class="filter-dropdown-item">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text gl-inline-flex gl-gap-3">
                                  <span
                                    class="dropdown-label-box gl-m-0"
                                    style="background: {{color}}"
                                  ></span>
                                  <span class="label-title js-data-value"> {{title}} </span>
                                </span>
                              </button>
                            </li>
                          </ul>
                        </div>
                        <div
                          class="gl-filtered-search-suggestion-list dropdown-menu"
                          id="js-dropdown-my-reaction"
                        >
                          <ul data-dropdown>
                            <li class="filter-dropdown-item" data-value="None">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> None </span>
                              </button>
                            </li>
                            <li class="filter-dropdown-item" data-value="Any">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> Any </span>
                              </button>
                            </li>
                            <li class="divider droplab-item-ignore"></li>
                          </ul>
                          <ul class="filter-dropdown" data-dropdown data-dynamic>
                            <li class="filter-dropdown-item">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text gl-inline-flex gl-gap-1">
                                  <gl-emoji></gl-emoji>
                                  <span class="js-data-value"> {{name}} </span>
                                </span>
                              </button>
                            </li>
                          </ul>
                        </div>
                        <div
                          class="gl-filtered-search-suggestion-list dropdown-menu"
                          id="js-dropdown-wip"
                        >
                          <ul class="filter-dropdown" data-dropdown>
                            <li class="filter-dropdown-item" data-capitalize data-value="yes">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> Yes </span>
                              </button>
                            </li>
                            <li class="filter-dropdown-item" data-capitalize data-value="no">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> No </span>
                              </button>
                            </li>
                          </ul>
                        </div>
                        <div
                          class="gl-filtered-search-suggestion-list dropdown-menu"
                          id="js-dropdown-approved"
                        >
                          <ul class="filter-dropdown" data-dropdown>
                            <li class="filter-dropdown-item" data-capitalize data-value="yes">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> Yes </span>
                              </button>
                            </li>
                            <li class="filter-dropdown-item" data-capitalize data-value="no">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> No </span>
                              </button>
                            </li>
                          </ul>
                        </div>
                        <div
                          class="gl-filtered-search-suggestion-list dropdown-menu"
                          id="js-dropdown-confidential"
                        >
                          <ul class="filter-dropdown" data-dropdown>
                            <li class="filter-dropdown-item" data-capitalize data-value="yes">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> Yes </span>
                              </button>
                            </li>
                            <li class="filter-dropdown-item" data-capitalize data-value="no">
                              <button type="button" class="gl-button btn btn-md btn-link">
                                <span class="gl-button-text"> No </span>
                              </button>
                            </li>
                          </ul>
                        </div>
                        <div
                          class="gl-filtered-search-suggestion-list dropdown-menu"
                          id="js-dropdown-target-branch"
                        >
                          <ul class="filter-dropdown" data-dropdown data-dynamic>
                            <li class="filter-dropdown-item">
                              <button
                                class="gl-button btn btn-md btn-link js-data-value gl-font-monospace"
                                type="button"
                              >
                                <span class="gl-button-text"> {{title}} </span>
                              </button>
                            </li>
                          </ul>
                        </div>
                        <div
                          class="gl-filtered-search-suggestion-list dropdown-menu"
                          id="js-dropdown-source-branch"
                        >
                          <ul class="filter-dropdown" data-dropdown data-dynamic>
                            <li class="filter-dropdown-item">
                              <button
                                class="gl-button btn btn-md btn-link js-data-value gl-font-monospace"
                                type="button"
                              >
                                <span class="gl-button-text"> {{title}} </span>
                              </button>
                            </li>
                          </ul>
                        </div>
                        <div
                          class="gl-filtered-search-suggestion-list dropdown-menu"
                          id="js-dropdown-environment"
                        >
                          <ul class="filter-dropdown" data-dropdown data-dynamic>
                            <li class="filter-dropdown-item">
                              <button
                                class="gl-button btn btn-md btn-link js-data-value"
                                type="button"
                              >
                                <span class="gl-button-text"> {{title}} </span>
                              </button>
                            </li>
                          </ul>
                        </div>
                      </div>
                      <button
                        class="gl-button btn btn-icon btn-sm btn-default btn-default-tertiary clear-search hidden has-tooltip gl-mr-1 gl-self-center"
                        title="Clear"
                        type="button"
                      >
                        <svg
                          class="s16 gl-icon gl-button-icon clear-search-icon"
                          data-testid="clear-icon"
                        >
                          <use
                            href="http://test.host/assets/icons-aa2c8ddf99d22b77153ca2bb092a23889c12c597fc8b8de94b0f730eb53513f6.svg#clear"
                          ></use>
                        </svg>
                      </button>
                    </div>
                  </div>
                </form>
              </div>
              <div
                class="filter-dropdown-container gl-flex gl-flex-col gl-items-start md:gl-flex-row"
              >
                <div>
                  <div class="btn-group" role="group">
                    <div
                      class="gl-new-dropdown js-redirect-listbox btn-group"
                      data-placement="right"
                      data-items='[{"value":"priority","text":"Priority","href":"/frontend-fixtures/merge-requests-project/-/merge_requests?sort=priority"},{"value":"created_date","text":"Created date","href":"/frontend-fixtures/merge-requests-project/-/merge_requests?sort=created_date"},{"value":"closed_at","text":"Closed date","href":"/frontend-fixtures/merge-requests-project/-/merge_requests?sort=closed_at"},{"value":"updated_desc","text":"Updated date","href":"/frontend-fixtures/merge-requests-project/-/merge_requests?sort=updated_desc"},{"value":"milestone","text":"Milestone due date","href":"/frontend-fixtures/merge-requests-project/-/merge_requests?sort=milestone"},{"value":"popularity","text":"Popularity","href":"/frontend-fixtures/merge-requests-project/-/merge_requests?sort=popularity"},{"value":"label_priority","text":"Label priority","href":"/frontend-fixtures/merge-requests-project/-/merge_requests?sort=label_priority"},{"value":"title_asc","text":"Title","href":"/frontend-fixtures/merge-requests-project/-/merge_requests?sort=title_asc"}]'
                      data-selected="created_date"
                    >
                      <button
                        class="gl-button btn btn-md btn-default gl-new-dropdown-toggle"
                        type="button"
                      >
                        <span class="gl-button-text">
                          <span class="gl-new-dropdown-button-text">Created date</span
                          ><svg
                            class="s16 gl-button-icon gl-new-dropdown-chevron gl-icon"
                            data-testid="chevron-down-icon"
                          >
                            <use
                              href="http://test.host/assets/icons-aa2c8ddf99d22b77153ca2bb092a23889c12c597fc8b8de94b0f730eb53513f6.svg#chevron-down"
                            ></use>
                          </svg>
                        </span>
                      </button>
                    </div>
                    <a
                      class="gl-button btn btn-icon btn-md btn-default has-tooltip reverse-sort-btn rspec-reverse-sort"
                      title="Sort direction"
                      href="http://test.host/frontend-fixtures/merge-requests-project/-/merge_requests?sort=created_asc"
                      ><svg class="s16 gl-icon gl-button-icon" data-testid="sort-highest-icon">
                        <use
                          href="http://test.host/assets/icons-aa2c8ddf99d22b77153ca2bb092a23889c12c597fc8b8de94b0f730eb53513f6.svg#sort-highest"
                        ></use>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <aside
            aria-label="Bulk update"
            aria-live="polite"
            class="issues-bulk-update js-right-sidebar right-sidebar"
            data-signed-in
          >
            <div class="issuable-sidebar hidden">
              <form
                class="bulk-update"
                action="http://test.host/frontend-fixtures/merge-requests-project/-/merge_requests/bulk_update"
                accept-charset="UTF-8"
                method="post"
              >
                <div class="block issuable-sidebar-header">
                  <div class="filter-item update-issues-btn float-left gl-inline-block">
                    <button
                      type="submit"
                      disabled
                      class="gl-button btn btn-md btn-confirm js-update-selected-issues"
                    >
                      <span class="gl-button-text"> Update selected </span>
                    </button>
                  </div>
                  <button
                    class="gl-button btn btn-md btn-default js-bulk-update-menu-hide gl-float-right"
                    type="button"
                  >
                    <span class="gl-button-text"> Cancel </span>
                  </button>
                </div>
                <div class="block js-status-dropdown-container">
                  <div class="title">Status</div>
                  <div class="js-status-dropdown"></div>
                </div>
                <div class="block">
                  <div class="title">Assignee</div>
                  <div class="filter-item">
                    <input
                      class="js-assignee-ids-input"
                      name="update[assignee_ids][]"
                      type="hidden"
                    />
                    <div
                      class="js-assignee-dropdown"
                      data-full-path="frontend-fixtures/merge-requests-project"
                    ></div>
                  </div>
                </div>
                <div class="block">
                  <div class="title">Labels</div>
                  <div class="filter-item labels-filter">
                    <div class="dropdown">
                      <button
                        class="dropdown-menu-toggle js-label-select js-multiselect js-filter-bulk-update"
                        data-default-label="Labels"
                        data-field-name="update[label_ids][]"
                        data-labels="http://test.host/frontend-fixtures/merge-requests-project/-/labels.json?include_ancestor_groups=true"
                        data-namespace-path="frontend-fixtures"
                        data-persist-when-hide="true"
                        data-project-id="18"
                        data-project-path="merge-requests-project"
                        data-scoped-labels="false"
                        data-testid="issuable-label-dropdown"
                        data-toggle="dropdown"
                        data-use-id
                        type="button"
                      >
                        <span class="dropdown-toggle-text"> Select labels </span>
                        <svg class="s16 dropdown-menu-toggle-icon" data-testid="chevron-down-icon">
                          <use
                            href="http://test.host/assets/icons-aa2c8ddf99d22b77153ca2bb092a23889c12c597fc8b8de94b0f730eb53513f6.svg#chevron-down"
                          ></use>
                        </svg>
                      </button>
                      <div
                        class="dropdown-menu dropdown-select dropdown-menu-paging dropdown-menu-labels dropdown-menu-selectable dropdown-extended-height"
                      >
                        <div class="dropdown-page-one">
                          <div class="dropdown-title gl-flex">
                            <span class="gl-ml-auto">Apply a label</span
                            ><button
                              class="dropdown-title-button dropdown-menu-close gl-ml-auto"
                              aria-label="Close"
                              type="button"
                            >
                              <svg class="s16 dropdown-menu-close-icon" data-testid="close-icon">
                                <use
                                  href="http://test.host/assets/icons-aa2c8ddf99d22b77153ca2bb092a23889c12c597fc8b8de94b0f730eb53513f6.svg#close"
                                ></use>
                              </svg>
                            </button>
                          </div>
                          <div class="dropdown-input">
                            <input
                              type="search"
                              data-testid="dropdown-input-field"
                              class="dropdown-input-field"
                              placeholder="Search"
                              autocomplete="off"
                            /><svg class="s16 dropdown-input-search" data-testid="search-icon">
                              <use
                                href="http://test.host/assets/icons-aa2c8ddf99d22b77153ca2bb092a23889c12c597fc8b8de94b0f730eb53513f6.svg#search"
                              ></use></svg
                            ><svg
                              class="s16 dropdown-input-clear js-dropdown-input-clear"
                              data-testid="close-icon"
                            >
                              <use
                                href="http://test.host/assets/icons-aa2c8ddf99d22b77153ca2bb092a23889c12c597fc8b8de94b0f730eb53513f6.svg#close"
                              ></use>
                            </svg>
                          </div>
                          <div class="dropdown-content"></div>
                          <div class="dropdown-loading">
                            <div class="gl-spinner-container gl-mt-7" role="status">
                              <span
                                aria-hidden
                                class="gl-spinner gl-spinner-md gl-spinner-dark !gl-align-text-bottom"
                              ></span
                              ><span class="gl-sr-only !gl-absolute">Loading</span>
                            </div>
                          </div>
                        </div>

                        <div class="dropdown-loading">
                          <div class="gl-spinner-container gl-mt-7" role="status">
                            <span
                              aria-hidden
                              class="gl-spinner gl-spinner-md gl-spinner-dark !gl-align-text-bottom"
                            ></span
                            ><span class="gl-sr-only !gl-absolute">Loading</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="block">
                  <div class="title">Milestone</div>
                  <div class="filter-item">
                    <div
                      class="js-milestone-dropdown-root"
                      data-full-path="frontend-fixtures/merge-requests-project"
                      data-workspace-type="project"
                    ></div>
                  </div>
                </div>
                <div class="block">
                  <div class="title">Subscriptions</div>
                  <div class="js-subscriptions-dropdown"></div>
                </div>
                <input
                  type="hidden"
                  name="update[issuable_ids]"
                  id="update_issuable_ids"
                  value=""
                  autocomplete="off"
                />
                <input type="hidden" name="state_event" id="state_event" autocomplete="off" />
              </form>
            </div>
          </aside>

          <div class="merge-requests-holder">
            <ul class="content-list mr-list issuable-list">
              <li class="merge-request" data-id="19" data-labels="[]" id="merge_request_19">
                <div class="issue-check hidden gl-mr-3">
                  <div class="gl-form-checkbox custom-control custom-checkbox">
                    <input
                      type="checkbox"
                      name="selected_merge_request_19"
                      id="selected_merge_request_19"
                      data-id="19"
                      class="custom-control-input"
                    />
                    <label class="custom-control-label" for="selected_merge_request_19"
                      ><span><span class="gl-sr-only"> My title 36 </span> </span></label
                    >
                  </div>
                </div>
                <div class="issuable-info-container gl-flex-col gl-gap-3 md:gl-flex-row">
                  <div class="issuable-main-info !gl-mr-0">
                    <div class="merge-request-title title">
                      <span class="merge-request-title-text js-onboarding-mr-item">
                        <a
                          class="js-prefetch-document"
                          href="http://test.host/frontend-fixtures/merge-requests-project/-/merge_requests/1"
                          >My title 36</a
                        >
                      </span>
                    </div>
                    <div class="issuable-info">
                      <span class="issuable-reference gl-inline-block"> !1 </span>
                      <span class="gl-hidden sm:gl-inline">
                        <span class="issuable-authored gl-inline-block !gl-text-subtle">
                          · created
                          <time
                            class="js-timeago"
                            title="Jul 3, 2015 10:00am"
                            datetime="2015-07-03T10:00:00Z"
                            tabindex="0"
                            aria-label="Jul 3, 2015 10:00am"
                            data-toggle="tooltip"
                            data-placement="bottom"
                            data-container="body"
                            >Jul 03, 2015</time
                          >
                          by
                          <a
                            class="author-link js-user-link !gl-text-subtle"
                            data-user-id="30"
                            data-username="frontend-fixtures"
                            data-name="Sidney Jones42"
                            data-testid="author-link"
                            href="http://test.host/frontend-fixtures"
                            ><span class="author">Sidney Jones42</span></a
                          >
                        </span>
                        <span
                          class="project-ref-path has-tooltip gl-inline-block gl-max-w-26 gl-truncate gl-align-bottom"
                          title="Target branch: feature"
                        >
                           
                          <a
                            class="ref-name !gl-text-subtle"
                            href="http://test.host/frontend-fixtures/merge-requests-project/-/commits/feature"
                            ><svg class="s12 fork-sprite" data-testid="branch-icon">
                              <use
                                href="http://test.host/assets/icons-aa2c8ddf99d22b77153ca2bb092a23889c12c597fc8b8de94b0f730eb53513f6.svg#branch"
                              ></use>
                            </svg>
                            feature
                          </a></span
                        >
                      </span>
                    </div>
                  </div>
                  <div
                    class="gl-flex gl-w-full gl-shrink-0 gl-flex-row gl-justify-between gl-gap-1 gl-self-start gl-text-sm md:gl-w-auto md:!gl-flex-col"
                  >
                    <ul class="controls gl-gap-3 gl-self-end gl-pl-0"></ul>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </main>
      </div>
    </div>
  </div>
</body>
