# The following specs are excluded from running in random order.
# They are run in defined order.
#
# See https://docs.gitlab.com/ee/development/testing_guide/best_practices.html#test-order.
#
---
- './ee/spec/controllers/admin/projects_controller_spec.rb'
- './ee/spec/controllers/ee/groups_controller_spec.rb'
- './ee/spec/controllers/ee/projects/jobs_controller_spec.rb'
- './ee/spec/controllers/groups/contribution_analytics_controller_spec.rb'
- './ee/spec/controllers/projects_controller_spec.rb'
- './ee/spec/controllers/projects/integrations/jira/issues_controller_spec.rb'
- './ee/spec/controllers/projects/mirrors_controller_spec.rb'
- './ee/spec/features/admin/admin_groups_spec.rb'
- './ee/spec/features/admin/admin_sends_notification_spec.rb'
- './ee/spec/features/admin/geo/admin_geo_replication_nav_spec.rb'
- './ee/spec/features/admin/geo/admin_geo_sidebar_spec.rb'
- './ee/spec/features/admin/licenses/admin_adds_license_spec.rb'
- './ee/spec/features/admin/users/users_spec.rb'
- './ee/spec/features/boards/boards_spec.rb'
- './ee/spec/features/boards/group_boards/board_deletion_spec.rb'
- './ee/spec/features/boards/group_boards/multiple_boards_spec.rb'
- './ee/spec/features/boards/new_issue_spec.rb'
- './ee/spec/features/boards/scoped_issue_board_spec.rb'
- './ee/spec/features/boards/sidebar_spec.rb'
- './ee/spec/features/boards/swimlanes/epics_swimlanes_drag_drop_spec.rb'
- './ee/spec/features/boards/swimlanes/epics_swimlanes_filtering_spec.rb'
- './ee/spec/features/boards/swimlanes/epics_swimlanes_sidebar_labels_spec.rb'
- './ee/spec/features/boards/swimlanes/epics_swimlanes_sidebar_spec.rb'
- './ee/spec/features/boards/swimlanes/epics_swimlanes_spec.rb'
- './ee/spec/features/boards/user_adds_lists_to_board_spec.rb'
- './ee/spec/features/boards/user_visits_board_spec.rb'
- './ee/spec/features/burndown_charts_spec.rb'
- './ee/spec/features/burnup_charts_spec.rb'
- './ee/spec/features/ci_shared_runner_settings_spec.rb'
- './ee/spec/features/clusters/cluster_detail_page_spec.rb'
- './ee/spec/features/dashboards/activity_spec.rb'
- './ee/spec/features/dashboards/groups_spec.rb'
- './ee/spec/features/dashboards/issues_spec.rb'
- './ee/spec/features/dashboards/merge_requests_spec.rb'
- './ee/spec/features/dashboards/operations_spec.rb'
- './ee/spec/features/dashboards/projects_spec.rb'
- './ee/spec/features/dashboards/todos_spec.rb'
- './ee/spec/features/discussion_comments/epic_quick_actions_spec.rb'
- './ee/spec/features/epic_boards/epic_boards_sidebar_spec.rb'
- './ee/spec/features/epic_boards/epic_boards_spec.rb'
- './ee/spec/features/epic_boards/multiple_epic_boards_spec.rb'
- './ee/spec/features/epic_boards/new_epic_spec.rb'
- './ee/spec/features/geo_node_spec.rb'
- './ee/spec/features/gitlab_subscriptions/seat_count_alert_spec.rb'
- './ee/spec/features/google_analytics_datalayer_spec.rb'
- './ee/spec/features/groups/active_tabs_spec.rb'
- './ee/spec/features/groups/analytics/ci_cd_analytics_spec.rb'
- './ee/spec/features/groups/analytics/cycle_analytics/charts_spec.rb'
- './ee/spec/features/groups/analytics/cycle_analytics/filters_and_data_spec.rb'
- './ee/spec/features/groups/analytics/cycle_analytics/multiple_value_streams_spec.rb'
- './ee/spec/features/groups/analytics/productivity_analytics_spec.rb'
- './ee/spec/features/groups/audit_events_spec.rb'
- './ee/spec/features/groups/billing_spec.rb'
- './ee/spec/features/groups/contribution_analytics_spec.rb'
- './ee/spec/features/groups/group_overview_spec.rb'
- './ee/spec/features/groups/group_page_with_external_authorization_service_spec.rb'
- './ee/spec/features/groups/group_roadmap_spec.rb'
- './ee/spec/features/groups/group_settings_spec.rb'
- './ee/spec/features/groups/groups_security_credentials_spec.rb'
- './ee/spec/features/groups/hooks/user_adds_hook_spec.rb'
- './ee/spec/features/groups/hooks/user_edits_hooks_spec.rb'
- './ee/spec/features/groups/hooks/user_tests_hooks_spec.rb'
- './ee/spec/features/groups/hooks/user_views_hooks_spec.rb'
- './ee/spec/features/groups/insights_spec.rb'
- './ee/spec/features/groups/issues_spec.rb'
- './ee/spec/features/groups/iterations/user_creates_iteration_in_cadence_spec.rb'
- './ee/spec/features/groups/iterations/user_edits_iteration_cadence_spec.rb'
- './ee/spec/features/groups/iterations/user_edits_iteration_spec.rb'
- './ee/spec/features/groups/iterations/user_views_iteration_cadence_spec.rb'
- './ee/spec/features/groups/iterations/user_views_iteration_spec.rb'
- './ee/spec/features/groups/ldap_group_links_spec.rb'
- './ee/spec/features/groups/members/list_members_spec.rb'
- './ee/spec/features/groups/members/manage_groups_spec.rb'
- './ee/spec/features/groups/members/manage_members_spec.rb'
- './ee/spec/features/groups/members/override_ldap_memberships_spec.rb'
- './ee/spec/features/groups/navbar_spec.rb'
- './ee/spec/features/groups/new_spec.rb'
- './ee/spec/features/groups/push_rules_spec.rb'
- './ee/spec/features/groups/saml_enforcement_spec.rb'
- './ee/spec/features/groups/saml_group_links_spec.rb'
- './ee/spec/features/groups/saml_providers_spec.rb'
- './ee/spec/features/groups/scim_token_spec.rb'
- './ee/spec/features/groups/security/compliance_dashboards_spec.rb'
- './ee/spec/features/groups/settings/ci_cd_spec.rb'
- './ee/spec/features/groups/settings/protected_environments_spec.rb'
- './ee/spec/features/groups/settings/reporting_spec.rb'
- './ee/spec/features/groups/settings/user_configures_insights_spec.rb'
- './ee/spec/features/groups/settings/user_searches_in_settings_spec.rb'
- './ee/spec/features/groups_spec.rb'
- './ee/spec/features/groups/wikis_spec.rb'
- './ee/spec/features/groups/wiki/user_views_wiki_empty_spec.rb'
- './ee/spec/features/incidents/user_uses_quick_actions_spec.rb'
- './ee/spec/features/integrations/jira/jira_issues_list_spec.rb'
- './ee/spec/features/issues/epic_in_issue_sidebar_spec.rb'
- './ee/spec/features/issues/gfm_autocomplete_ee_spec.rb'
- './ee/spec/features/issues/issue_actions_spec.rb'
- './ee/spec/features/issues/list/blocking_issues_spec.rb'
- './ee/spec/features/issues/list/filtered_search/filter_issues_by_iteration_spec.rb'
- './ee/spec/features/issues/list/filtered_search/filter_issues_by_multiple_assignees_spec.rb'
- './ee/spec/features/issues/list/filtered_search/filter_issues_epic_spec.rb'
- './ee/spec/features/issues/list/filtered_search/filter_issues_weight_spec.rb'
- './ee/spec/features/issues/list/sub_nav_ee_spec.rb'
- './ee/spec/features/issues/list/user_bulk_edits_issues_spec.rb'
- './ee/spec/features/issues/list/user_sees_empty_state_spec.rb'
- './ee/spec/features/issues/list/user_views_issues_spec.rb'
- './ee/spec/features/issues/move_issue_resource_weight_events_spec.rb'
- './ee/spec/features/issues/new/form_spec.rb'
- './ee/spec/features/issues/new/user_creates_issue_spec.rb'
- './ee/spec/features/issues/related_issues_spec.rb'
- './ee/spec/features/issues/resource_weight_events_spec.rb'
- './ee/spec/features/issues/viewing_relocated_issues_spec.rb'
- './ee/spec/features/labels_hierarchy_spec.rb'
- './ee/spec/features/markdown/markdown_spec.rb'
- './ee/spec/features/merge_request/merge_request_widget_blocking_mrs_spec.rb'
- './ee/spec/features/merge_request/sidebar_spec.rb'
- './ee/spec/features/merge_requests/user_filters_by_approvers_spec.rb'
- './ee/spec/features/merge_requests/user_resets_approvers_spec.rb'
- './ee/spec/features/merge_requests/user_views_all_merge_requests_spec.rb'
- './ee/spec/features/merge_request/user_approves_spec.rb'
- './ee/spec/features/merge_request/user_approves_with_password_spec.rb'
- './ee/spec/features/merge_request/user_comments_on_merge_request_spec.rb'
- './ee/spec/features/merge_request/user_creates_merge_request_spec.rb'
- './ee/spec/features/merge_request/user_creates_merge_request_with_blocking_mrs_spec.rb'
- './ee/spec/features/merge_request/user_creates_multiple_assignees_mr_spec.rb'
- './ee/spec/features/merge_request/user_creates_multiple_reviewers_mr_spec.rb'
- './ee/spec/features/merge_request/user_edits_approval_rules_mr_spec.rb'
- './ee/spec/features/merge_request/user_edits_merge_request_blocking_mrs_spec.rb'
- './ee/spec/features/merge_request/user_edits_multiple_assignees_mr_spec.rb'
- './ee/spec/features/merge_request/user_edits_multiple_reviewers_mr_spec.rb'
- './ee/spec/features/merge_request/user_merges_immediately_spec.rb'
- './ee/spec/features/merge_request/user_merges_with_namespace_storage_limits_spec.rb'
- './ee/spec/features/merge_request/user_merges_with_push_rules_spec.rb'
- './ee/spec/features/merge_request/user_sees_approval_widget_spec.rb'
- './ee/spec/features/merge_request/user_sees_closing_issues_message_spec.rb'
- './ee/spec/features/merge_request/user_sees_merge_widget_spec.rb'
- './ee/spec/features/merge_request/user_sees_mr_approvals_promo_spec.rb'
- './ee/spec/features/merge_request/user_sees_status_checks_widget_spec.rb'
- './ee/spec/features/merge_request/user_selects_branches_for_new_mr_spec.rb'
- './ee/spec/features/merge_request/user_sets_approval_rules_spec.rb'
- './ee/spec/features/merge_request/user_sets_approvers_spec.rb'
- './ee/spec/features/merge_request/user_uses_slash_commands_spec.rb'
- './ee/spec/features/merge_request/user_views_blocked_merge_request_spec.rb'
- './ee/spec/features/merge_trains/two_merge_requests_on_train_spec.rb'
- './ee/spec/features/merge_trains/user_adds_merge_request_to_merge_train_spec.rb'
- './ee/spec/features/milestones/user_views_milestone_spec.rb'
- './ee/spec/features/namespace_user_cap_reached_alert_spec.rb'
- './ee/spec/features/oncall_schedules/user_creates_schedule_spec.rb'
- './ee/spec/features/operations_nav_link_spec.rb'
- './ee/spec/features/password_reset_spec.rb'
- './ee/spec/features/pending_group_memberships_spec.rb'
- './ee/spec/features/pending_project_memberships_spec.rb'
- './ee/spec/features/profiles/account_spec.rb'
- './ee/spec/features/profiles/billing_spec.rb'
- './ee/spec/features/profiles/user_visits_public_profile_spec.rb'
- './ee/spec/features/projects/active_tabs_spec.rb'
- './ee/spec/features/projects/audit_events_spec.rb'
- './ee/spec/features/projects/custom_projects_template_spec.rb'
- './ee/spec/features/projects/environments/environments_spec.rb'
- './ee/spec/features/projects/feature_flags/feature_flag_issues_spec.rb'
- './ee/spec/features/projects/feature_flags/user_creates_feature_flag_spec.rb'
- './ee/spec/features/projects/feature_flags/user_deletes_feature_flag_spec.rb'
- './ee/spec/features/projects/feature_flags/user_sees_feature_flag_list_spec.rb'
- './ee/spec/features/projects/feature_flags/user_updates_feature_flag_spec.rb'
- './ee/spec/features/projects/insights_spec.rb'
- './ee/spec/features/projects/integrations/jira_issues_list_spec.rb'
- './ee/spec/features/projects/integrations/project_integrations_spec.rb'
- './ee/spec/features/projects/integrations/user_activates_github_spec.rb'
- './ee/spec/features/projects/integrations/user_activates_jira_spec.rb'
- './ee/spec/features/projects/iterations/iteration_cadences_list_spec.rb'
- './ee/spec/features/projects/iterations/user_views_iteration_spec.rb'
- './ee/spec/features/projects/jobs/blocked_deployment_job_page_spec.rb'
- './ee/spec/features/projects/jobs_spec.rb'
- './ee/spec/features/projects/kerberos_clone_instructions_spec.rb'
- './ee/spec/features/projects/members/invite_group_and_members_spec.rb'
- './ee/spec/features/projects/members/manage_groups_spec.rb'
- './ee/spec/features/projects/members/member_is_removed_from_project_spec.rb'
- './ee/spec/features/projects/members/member_leaves_project_spec.rb'
- './ee/spec/features/projects/merge_requests/user_approves_merge_request_spec.rb'
- './ee/spec/features/projects/merge_requests/user_edits_merge_request_spec.rb'
- './ee/spec/features/projects/milestones/milestone_spec.rb'
- './ee/spec/features/projects/mirror_spec.rb'
- './ee/spec/features/projects/new_project_from_template_spec.rb'
- './ee/spec/features/projects/new_project_spec.rb'
- './ee/spec/features/projects/path_locks_spec.rb'
- './ee/spec/features/projects/pipelines/pipeline_csp_spec.rb'
- './ee/spec/features/projects/pipelines/pipeline_spec.rb'
- './ee/spec/features/projects/pipelines/pipelines_spec.rb'
- './ee/spec/features/projects/push_rules_spec.rb'
- './ee/spec/features/projects/quality/test_case_create_spec.rb'
- './ee/spec/features/projects/quality/test_case_list_spec.rb'
- './ee/spec/features/projects/quality/test_case_show_spec.rb'
- './ee/spec/features/projects/releases/user_views_release_spec.rb'
- './ee/spec/features/projects/requirements_management/requirements_list_spec.rb'
- './ee/spec/features/projects/security/dast_scanner_profiles_spec.rb'
- './ee/spec/features/projects/security/dast_site_profiles_spec.rb'
- './ee/spec/features/projects/security/user_creates_on_demand_scan_spec.rb'
- './ee/spec/features/projects/security/user_edits_on_demand_scan_spec.rb'
- './ee/spec/features/projects/security/user_views_security_configuration_spec.rb'
- './ee/spec/features/projects/settings/auto_rollback_spec.rb'
- './ee/spec/features/projects/settings/disable_merge_trains_setting_spec.rb'
- './ee/spec/features/projects/settings/ee/repository_mirrors_settings_spec.rb'
- './ee/spec/features/projects/settings/ee/service_desk_setting_spec.rb'
- './ee/spec/features/projects/settings/issues_settings_spec.rb'
- './ee/spec/features/projects/settings/merge_request_approvals_settings_spec.rb'
- './ee/spec/features/projects/settings/merge_requests_settings_spec.rb'
- './ee/spec/features/projects/settings/pipeline_subscriptions_spec.rb'
- './ee/spec/features/projects/settings/protected_environments_spec.rb'
- './ee/spec/features/projects/settings/push_rules_settings_spec.rb'
- './ee/spec/features/projects/settings/user_manages_approval_settings_spec.rb'
- './ee/spec/features/projects/settings/user_manages_issues_template_spec.rb'
- './ee/spec/features/projects/settings/user_manages_members_spec.rb'
- './ee/spec/features/projects/settings/user_manages_merge_requests_template_spec.rb'
- './ee/spec/features/projects/settings/user_manages_merge_trains_spec.rb'
- './ee/spec/features/projects/show/developer_views_empty_project_instructions_spec.rb'
- './ee/spec/features/projects/show_project_spec.rb'
- './ee/spec/features/projects_spec.rb'
- './ee/spec/features/projects/user_applies_custom_file_template_spec.rb'
- './ee/spec/features/projects/view_blob_with_code_owners_spec.rb'
- './ee/spec/features/projects/wiki/user_views_wiki_empty_spec.rb'
- './ee/spec/features/promotion_spec.rb'
- './ee/spec/features/protected_branches_spec.rb'
- './ee/spec/features/protected_tags_spec.rb'
- './ee/spec/features/read_only_spec.rb'
- './ee/spec/features/registrations/combined_registration_spec.rb'
- './ee/spec/features/registrations/one_trust_spec.rb'
- './ee/spec/features/search/elastic/global_search_spec.rb'
- './ee/spec/features/search/elastic/snippet_search_spec.rb'
- './ee/spec/features/search/user_searches_for_epics_spec.rb'
- './ee/spec/features/security/admin_access_spec.rb'
- './ee/spec/features/security/dashboard_access_spec.rb'
- './ee/spec/features/security/group/internal_access_spec.rb'
- './ee/spec/features/security/group/private_access_spec.rb'
- './ee/spec/features/security/group/public_access_spec.rb'
- './ee/spec/features/security/profile_access_spec.rb'
- './ee/spec/features/security/project/discover_spec.rb'
- './ee/spec/features/security/project/internal_access_spec.rb'
- './ee/spec/features/security/project/private_access_spec.rb'
- './ee/spec/features/security/project/public_access_spec.rb'
- './ee/spec/features/security/project/snippet/internal_access_spec.rb'
- './ee/spec/features/security/project/snippet/private_access_spec.rb'
- './ee/spec/features/security/project/snippet/public_access_spec.rb'
- './ee/spec/features/signup_spec.rb'
- './ee/spec/features/subscriptions/expiring_subscription_message_spec.rb'
- './ee/spec/features/trial_registrations/company_information_spec.rb'
- './ee/spec/features/trial_registrations/signin_spec.rb'
- './ee/spec/features/trial_registrations/signup_spec.rb'
- './ee/spec/features/users/login_spec.rb'
- './ee/spec/features/users/signup_spec.rb'
- './ee/spec/features/user_unsubscribes_from_admin_notifications_spec.rb'
- './ee/spec/features/work_items/epics/delete_epic_spec.rb'
- './ee/spec/features/work_items/epics/epics_list_spec.rb'
- './ee/spec/features/work_items/epics/gfm_autocomplete_spec.rb'
- './ee/spec/features/work_items/epics/issue_promotion_spec.rb'
- './ee/spec/features/work_items/epics/referencing_epics_spec.rb'
- './ee/spec/features/work_items/epics/user_uses_quick_actions_spec.rb'
- './ee/spec/finders/analytics/cycle_analytics/stage_finder_spec.rb'
- './ee/spec/finders/analytics/devops_adoption/enabled_namespaces_finder_spec.rb'
- './ee/spec/finders/analytics/devops_adoption/snapshots_finder_spec.rb'
- './ee/spec/finders/approval_rules/group_finder_spec.rb'
- './ee/spec/finders/app_sec/fuzzing/coverage/corpuses_finder_spec.rb'
- './ee/spec/finders/audit_event_finder_spec.rb'
- './ee/spec/finders/auth/group_saml_identity_finder_spec.rb'
- './ee/spec/finders/auth/provisioned_users_finder_spec.rb'
- './ee/spec/finders/autocomplete/group_subgroups_finder_spec.rb'
- './ee/spec/finders/autocomplete/project_invited_groups_finder_spec.rb'
- './ee/spec/finders/billed_users_finder_spec.rb'
- './ee/spec/finders/boards/boards_finder_spec.rb'
- './ee/spec/finders/boards/epic_boards_finder_spec.rb'
- './ee/spec/finders/boards/milestones_finder_spec.rb'
- './ee/spec/finders/clusters/environments_finder_spec.rb'
- './ee/spec/finders/compliance_management/merge_requests/compliance_violations_finder_spec.rb'
- './ee/spec/finders/custom_project_templates_finder_spec.rb'
- './ee/spec/finders/dast/profiles_finder_spec.rb'
- './ee/spec/finders/dast_scanner_profiles_finder_spec.rb'
- './ee/spec/finders/dast_site_profiles_finder_spec.rb'
- './ee/spec/finders/dast_site_validations_finder_spec.rb'
- './ee/spec/finders/ee/alert_management/http_integrations_finder_spec.rb'
- './ee/spec/finders/ee/ci/daily_build_group_report_results_finder_spec.rb'
- './ee/spec/finders/ee/clusters/agents_finder_spec.rb'
- './ee/spec/finders/ee/fork_targets_finder_spec.rb'
- './ee/spec/finders/ee/group_members_finder_spec.rb'
- './ee/spec/finders/ee/namespaces/projects_finder_spec.rb'
- './ee/spec/finders/ee/projects_finder_spec.rb'
- './ee/spec/finders/ee/user_recent_events_finder_spec.rb'
- './ee/spec/finders/epics_finder_spec.rb'
- './ee/spec/finders/geo/ci_secure_file_registry_finder_spec.rb'
- './ee/spec/finders/geo/container_repository_registry_finder_spec.rb'
- './ee/spec/finders/geo/group_wiki_repository_registry_finder_spec.rb'
- './ee/spec/finders/geo/lfs_object_registry_finder_spec.rb'
- './ee/spec/finders/geo/merge_request_diff_registry_finder_spec.rb'
- './ee/spec/finders/geo_node_finder_spec.rb'
- './ee/spec/finders/geo/package_file_registry_finder_spec.rb'
- './ee/spec/finders/geo/pages_deployment_registry_finder_spec.rb'
- './ee/spec/finders/geo/pipeline_artifact_registry_finder_spec.rb'
- './ee/spec/finders/geo/snippet_repository_registry_finder_spec.rb'
- './ee/spec/finders/geo/terraform_state_version_registry_finder_spec.rb'
- './ee/spec/finders/geo/upload_registry_finder_spec.rb'
- './ee/spec/finders/gpg_keys_finder_spec.rb'
- './ee/spec/finders/group_projects_finder_spec.rb'
- './ee/spec/finders/group_saml_identity_finder_spec.rb'
- './ee/spec/finders/groups_with_templates_finder_spec.rb'
- './ee/spec/finders/issues_finder_spec.rb'
- './ee/spec/finders/iterations/cadences_finder_spec.rb'
- './ee/spec/finders/iterations_finder_spec.rb'
- './ee/spec/finders/licenses_finder_spec.rb'
- './ee/spec/finders/license_template_finder_spec.rb'
- './ee/spec/finders/merge_requests/by_approvers_finder_spec.rb'
- './ee/spec/finders/merge_requests_finder_spec.rb'
- './ee/spec/finders/notes_finder_spec.rb'
- './ee/spec/finders/productivity_analytics_finder_spec.rb'
- './ee/spec/finders/projects/integrations/jira/by_ids_finder_spec.rb'
- './ee/spec/finders/projects/integrations/jira/issues_finder_spec.rb'
- './ee/spec/finders/scim_finder_spec.rb'
- './ee/spec/finders/security/findings_finder_spec.rb'
- './ee/spec/finders/security/pipeline_vulnerabilities_finder_spec.rb'
- './ee/spec/finders/security/scan_execution_policies_finder_spec.rb'
- './ee/spec/finders/security/vulnerability_feedbacks_finder_spec.rb'
- './ee/spec/finders/snippets_finder_spec.rb'
- './ee/spec/finders/template_finder_spec.rb'
- './ee/spec/finders/users_finder_spec.rb'
- './ee/spec/frontend/fixtures/analytics/charts.rb'
- './ee/spec/frontend/fixtures/analytics/devops_reports/devops_adoption/enabled_namespaces.rb'
- './ee/spec/frontend/fixtures/analytics/value_streams_code_stage.rb'
- './ee/spec/frontend/fixtures/analytics/value_streams_issue_stage.rb'
- './ee/spec/frontend/fixtures/analytics/value_streams_plan_stage.rb'
- './ee/spec/frontend/fixtures/analytics/value_streams.rb'
- './ee/spec/frontend/fixtures/analytics/value_streams_review_stage.rb'
- './ee/spec/frontend/fixtures/analytics/value_streams_staging_stage.rb'
- './ee/spec/frontend/fixtures/analytics/value_streams_test_stage.rb'
- './ee/spec/frontend/fixtures/codequality_report.rb'
- './ee/spec/frontend/fixtures/dast_profiles.rb'
- './ee/spec/frontend/fixtures/dora/metrics.rb'
- './ee/spec/frontend/fixtures/epic.rb'
- './ee/spec/frontend/fixtures/issues.rb'
- './ee/spec/frontend/fixtures/merge_requests.rb'
- './ee/spec/frontend/fixtures/on_demand_dast_scans.rb'
- './ee/spec/frontend/fixtures/project_quality_summary.rb'
- './ee/spec/frontend/fixtures/runner.rb'
- './ee/spec/frontend/fixtures/saml_providers.rb'
- './ee/spec/frontend/fixtures/search.rb'
- './ee/spec/graphql/api/vulnerabilities_spec.rb'
- './ee/spec/graphql/ee/mutations/boards/issues/issue_move_list_spec.rb'
- './ee/spec/graphql/ee/mutations/boards/lists/create_spec.rb'
- './ee/spec/graphql/ee/mutations/concerns/mutations/resolves_issuable_spec.rb'
- './ee/spec/graphql/ee/resolvers/board_list_issues_resolver_spec.rb'
- './ee/spec/graphql/ee/resolvers/board_lists_resolver_spec.rb'
- './ee/spec/graphql/ee/resolvers/namespace_projects_resolver_spec.rb'
- './ee/spec/graphql/ee/types/alert_management/http_integration_type_spec.rb'
- './ee/spec/graphql/ee/types/board_list_type_spec.rb'
- './ee/spec/graphql/ee/types/boards/board_issue_input_type_spec.rb'
- './ee/spec/graphql/ee/types/board_type_spec.rb'
- './ee/spec/graphql/ee/types/ci/pipeline_merge_request_type_enum_spec.rb'
- './ee/spec/graphql/ee/types/compliance_management/compliance_framework_type_spec.rb'
- './ee/spec/graphql/ee/types/group_type_spec.rb'
- './ee/spec/graphql/ee/types/issuable_type_spec.rb'
- './ee/spec/graphql/ee/types/issue_sort_enum_spec.rb'
- './ee/spec/graphql/ee/types/milestone_type_spec.rb'
- './ee/spec/graphql/ee/types/mutation_type_spec.rb'
- './ee/spec/graphql/ee/types/notes/noteable_interface_spec.rb'
- './ee/spec/graphql/ee/types/repository/blob_type_spec.rb'
- './ee/spec/graphql/ee/types/todoable_interface_spec.rb'
- './ee/spec/graphql/ee/types/user_merge_request_interaction_type_spec.rb'
- './ee/spec/graphql/mutations/app_sec/fuzzing/coverage/corpus/create_spec.rb'
- './ee/spec/graphql/mutations/audit_events/streaming/headers/create_spec.rb'
- './ee/spec/graphql/mutations/audit_events/streaming/headers/destroy_spec.rb'
- './ee/spec/graphql/mutations/boards/epic_boards/create_spec.rb'
- './ee/spec/graphql/mutations/boards/epic_boards/destroy_spec.rb'
- './ee/spec/graphql/mutations/boards/epic_boards/epic_move_list_spec.rb'
- './ee/spec/graphql/mutations/boards/epic_boards/update_spec.rb'
- './ee/spec/graphql/mutations/boards/epic_lists/create_spec.rb'
- './ee/spec/graphql/mutations/boards/epic_lists/update_spec.rb'
- './ee/spec/graphql/mutations/boards/epics/create_spec.rb'
- './ee/spec/graphql/mutations/boards/lists/update_limit_metrics_spec.rb'
- './ee/spec/graphql/mutations/boards/update_epic_user_preferences_spec.rb'
- './ee/spec/graphql/mutations/boards/update_spec.rb'
- './ee/spec/graphql/mutations/compliance_management/frameworks/create_spec.rb'
- './ee/spec/graphql/mutations/compliance_management/frameworks/destroy_spec.rb'
- './ee/spec/graphql/mutations/compliance_management/frameworks/update_spec.rb'
- './ee/spec/graphql/mutations/dast_on_demand_scans/create_spec.rb'
- './ee/spec/graphql/mutations/dast/profiles/create_spec.rb'
- './ee/spec/graphql/mutations/dast/profiles/delete_spec.rb'
- './ee/spec/graphql/mutations/dast/profiles/run_spec.rb'
- './ee/spec/graphql/mutations/dast/profiles/update_spec.rb'
- './ee/spec/graphql/mutations/dast_scanner_profiles/create_spec.rb'
- './ee/spec/graphql/mutations/dast_scanner_profiles/delete_spec.rb'
- './ee/spec/graphql/mutations/dast_scanner_profiles/update_spec.rb'
- './ee/spec/graphql/mutations/dast_site_profiles/create_spec.rb'
- './ee/spec/graphql/mutations/dast_site_profiles/delete_spec.rb'
- './ee/spec/graphql/mutations/dast_site_profiles/update_spec.rb'
- './ee/spec/graphql/mutations/dast_site_tokens/create_spec.rb'
- './ee/spec/graphql/mutations/dast_site_validations/create_spec.rb'
- './ee/spec/graphql/mutations/dast_site_validations/revoke_spec.rb'
- './ee/spec/graphql/mutations/epics/add_issue_spec.rb'
- './ee/spec/graphql/mutations/epics/create_spec.rb'
- './ee/spec/graphql/mutations/epics/update_spec.rb'
- './ee/spec/graphql/mutations/gitlab_subscriptions/activate_spec.rb'
- './ee/spec/graphql/mutations/instance_security_dashboard/add_project_spec.rb'
- './ee/spec/graphql/mutations/instance_security_dashboard/remove_project_spec.rb'
- './ee/spec/graphql/mutations/issues/create_spec.rb'
- './ee/spec/graphql/mutations/issues/promote_to_epic_spec.rb'
- './ee/spec/graphql/mutations/issues/set_assignees_spec.rb'
- './ee/spec/graphql/mutations/issues/set_epic_spec.rb'
- './ee/spec/graphql/mutations/issues/set_escalation_policy_spec.rb'
- './ee/spec/graphql/mutations/issues/set_iteration_spec.rb'
- './ee/spec/graphql/mutations/issues/set_weight_spec.rb'
- './ee/spec/graphql/mutations/issues/update_spec.rb'
- './ee/spec/graphql/mutations/merge_requests/accept_spec.rb'
- './ee/spec/graphql/mutations/merge_requests/set_assignees_spec.rb'
- './ee/spec/graphql/mutations/merge_requests/set_reviewers_spec.rb'
- './ee/spec/graphql/mutations/projects/set_compliance_framework_spec.rb'
- './ee/spec/graphql/mutations/projects/set_locked_spec.rb'
- './ee/spec/graphql/mutations/releases/update_spec.rb'
- './ee/spec/graphql/mutations/requirements_management/create_requirement_spec.rb'
- './ee/spec/graphql/mutations/requirements_management/export_requirements_spec.rb'
- './ee/spec/graphql/mutations/requirements_management/update_requirement_spec.rb'
- './ee/spec/graphql/mutations/security/ci_configuration/configure_container_scanning_spec.rb'
- './ee/spec/graphql/mutations/security/ci_configuration/configure_dependency_scanning_spec.rb'
- './ee/spec/graphql/mutations/security_policy/assign_security_policy_project_spec.rb'
- './ee/spec/graphql/mutations/security_policy/commit_scan_execution_policy_spec.rb'
- './ee/spec/graphql/mutations/security_policy/create_security_policy_project_spec.rb'
- './ee/spec/graphql/mutations/security_policy/unassign_security_policy_project_spec.rb'
- './ee/spec/graphql/mutations/todos/create_spec.rb'
- './ee/spec/graphql/mutations/vulnerabilities/confirm_spec.rb'
- './ee/spec/graphql/mutations/vulnerabilities/create_external_issue_link_spec.rb'
- './ee/spec/graphql/mutations/vulnerabilities/create_spec.rb'
- './ee/spec/graphql/mutations/vulnerabilities/destroy_external_issue_link_spec.rb'
- './ee/spec/graphql/mutations/vulnerabilities/dismiss_spec.rb'
- './ee/spec/graphql/mutations/vulnerabilities/resolve_spec.rb'
- './ee/spec/graphql/mutations/vulnerabilities/revert_to_detected_spec.rb'
- './ee/spec/graphql/representation/vulnerability_scanner_entry_spec.rb'
- './ee/spec/graphql/resolvers/admin/cloud_licenses/current_license_resolver_spec.rb'
- './ee/spec/graphql/resolvers/admin/cloud_licenses/license_history_entries_resolver_spec.rb'
- './ee/spec/graphql/resolvers/admin/cloud_licenses/subscription_future_entries_resolver_spec.rb'
- './ee/spec/graphql/resolvers/analytics/devops_adoption/enabled_namespaces_resolver_spec.rb'
- './ee/spec/graphql/resolvers/app_sec/dast/profile_resolver_spec.rb'
- './ee/spec/graphql/resolvers/app_sec/fuzzing/coverage/corpuses_resolver_spec.rb'
- './ee/spec/graphql/resolvers/board_groupings/epics_resolvers_spec.rb'
- './ee/spec/graphql/resolvers/boards/board_list_epics_resolver_spec.rb'
- './ee/spec/graphql/resolvers/boards/epic_boards_resolvers_spec.rb'
- './ee/spec/graphql/resolvers/boards/epic_list_resolver_spec.rb'
- './ee/spec/graphql/resolvers/boards/epic_lists_resolvers_spec.rb'
- './ee/spec/graphql/resolvers/ci/code_coverage_activities_resolver_spec.rb'
- './ee/spec/graphql/resolvers/ci/code_coverage_summary_resolver_spec.rb'
- './ee/spec/graphql/resolvers/clusters/agents_resolver_spec.rb'
- './ee/spec/graphql/resolvers/dast_site_profile_resolver_spec.rb'
- './ee/spec/graphql/resolvers/dast_site_validation_resolver_spec.rb'
- './ee/spec/graphql/resolvers/dora_metrics_resolver_spec.rb'
- './ee/spec/graphql/resolvers/epic_ancestors_resolver_spec.rb'
- './ee/spec/graphql/resolvers/epic_issues_resolver_spec.rb'
- './ee/spec/graphql/resolvers/epics_resolver_spec.rb'
- './ee/spec/graphql/resolvers/external_issue_resolver_spec.rb'
- './ee/spec/graphql/resolvers/geo/ci_secure_file_registries_resolver_spec.rb'
- './ee/spec/graphql/resolvers/geo/geo_node_resolver_spec.rb'
- './ee/spec/graphql/resolvers/geo/group_wiki_repository_registries_resolver_spec.rb'
- './ee/spec/graphql/resolvers/geo/job_artifact_registries_resolver_spec.rb'
- './ee/spec/graphql/resolvers/geo/lfs_object_registries_resolver_spec.rb'
- './ee/spec/graphql/resolvers/geo/merge_request_diff_registries_resolver_spec.rb'
- './ee/spec/graphql/resolvers/geo/package_file_registries_resolver_spec.rb'
- './ee/spec/graphql/resolvers/geo/pages_deployment_registries_resolver_spec.rb'
- './ee/spec/graphql/resolvers/geo/pipeline_artifact_registries_resolver_spec.rb'
- './ee/spec/graphql/resolvers/geo/snippet_repository_registries_resolver_spec.rb'
- './ee/spec/graphql/resolvers/geo/terraform_state_version_registries_resolver_spec.rb'
- './ee/spec/graphql/resolvers/geo/upload_registries_resolver_spec.rb'
- './ee/spec/graphql/resolvers/instance_security_dashboard/projects_resolver_spec.rb'
- './ee/spec/graphql/resolvers/instance_security_dashboard_resolver_spec.rb'
- './ee/spec/graphql/resolvers/iterations/cadences_resolver_spec.rb'
- './ee/spec/graphql/resolvers/iterations_resolver_spec.rb'
- './ee/spec/graphql/resolvers/path_locks_resolver_spec.rb'
- './ee/spec/graphql/resolvers/pipeline_security_report_findings_resolver_spec.rb'
- './ee/spec/graphql/resolvers/requirements_management/requirements_resolver_spec.rb'
- './ee/spec/graphql/resolvers/requirements_management/test_reports_resolver_spec.rb'
- './ee/spec/graphql/resolvers/security_orchestration/scan_execution_policy_resolver_spec.rb'
- './ee/spec/graphql/resolvers/security_orchestration/scan_result_policy_resolver_spec.rb'
- './ee/spec/graphql/resolvers/security_report_summary_resolver_spec.rb'
- './ee/spec/graphql/resolvers/timebox_report_resolver_spec.rb'
- './ee/spec/graphql/resolvers/user_discussions_count_resolver_spec.rb'
- './ee/spec/graphql/resolvers/user_notes_count_resolver_spec.rb'
- './ee/spec/graphql/resolvers/vulnerabilities/container_images_resolver_spec.rb'
- './ee/spec/graphql/resolvers/vulnerabilities_count_per_day_resolver_spec.rb'
- './ee/spec/graphql/resolvers/vulnerabilities/details_resolver_spec.rb'
- './ee/spec/graphql/resolvers/vulnerabilities_grade_resolver_spec.rb'
- './ee/spec/graphql/resolvers/vulnerabilities/issue_links_resolver_spec.rb'
- './ee/spec/graphql/resolvers/vulnerabilities_resolver_spec.rb'
- './ee/spec/graphql/resolvers/vulnerabilities/scanners_resolver_spec.rb'
- './ee/spec/graphql/resolvers/vulnerability_severities_count_resolver_spec.rb'
- './ee/spec/graphql/types/admin/cloud_licenses/current_license_type_spec.rb'
- './ee/spec/graphql/types/admin/cloud_licenses/license_history_entry_type_spec.rb'
- './ee/spec/graphql/types/admin/cloud_licenses/subscription_future_entry_type_spec.rb'
- './ee/spec/graphql/types/alert_management/payload_alert_field_name_enum_spec.rb'
- './ee/spec/graphql/types/alert_management/payload_alert_field_path_segment_type_spec.rb'
- './ee/spec/graphql/types/alert_management/payload_alert_field_type_enum_spec.rb'
- './ee/spec/graphql/types/approval_rule_type_enum_spec.rb'
- './ee/spec/graphql/types/approval_rule_type_spec.rb'
- './ee/spec/graphql/types/app_sec/fuzzing/api/ci_configuration_type_spec.rb'
- './ee/spec/graphql/types/app_sec/fuzzing/api/scan_mode_enum_spec.rb'
- './ee/spec/graphql/types/app_sec/fuzzing/api/scan_profile_type_spec.rb'
- './ee/spec/graphql/types/app_sec/fuzzing/coverage/corpus_type_spec.rb'
- './ee/spec/graphql/types/asset_type_spec.rb'
- './ee/spec/graphql/types/audit_events/exterrnal_audit_event_destination_type_spec.rb'
- './ee/spec/graphql/types/audit_events/streaming/header_type_spec.rb'
- './ee/spec/graphql/types/boards/board_epic_type_spec.rb'
- './ee/spec/graphql/types/boards/epic_board_type_spec.rb'
- './ee/spec/graphql/types/boards/epic_list_metadata_type_spec.rb'
- './ee/spec/graphql/types/boards/epic_list_type_spec.rb'
- './ee/spec/graphql/types/boards/epic_user_preferences_type_spec.rb'
- './ee/spec/graphql/types/burnup_chart_daily_totals_type_spec.rb'
- './ee/spec/graphql/types/ci/code_coverage_activity_type_spec.rb'
- './ee/spec/graphql/types/ci/code_coverage_summary_spec.rb'
- './ee/spec/graphql/types/ci/code_quality_degradation_severity_enum_spec.rb'
- './ee/spec/graphql/types/ci/code_quality_degradation_type_spec.rb'
- './ee/spec/graphql/types/ci/minutes/namespace_monthly_usage_type_spec.rb'
- './ee/spec/graphql/types/ci/minutes/project_monthly_usage_type_spec.rb'
- './ee/spec/graphql/types/ci/pipeline_type_spec.rb'
- './ee/spec/graphql/types/ci/runner_type_spec.rb'
- './ee/spec/graphql/types/compliance_management/merge_requests/compliance_violation_input_type_spec.rb'
- './ee/spec/graphql/types/compliance_management/merge_requests/compliance_violation_reason_enum_spec.rb'
- './ee/spec/graphql/types/compliance_management/merge_requests/compliance_violation_severity_enum_spec.rb'
- './ee/spec/graphql/types/compliance_management/merge_requests/compliance_violation_sort_enum_spec.rb'
- './ee/spec/graphql/types/compliance_management/merge_requests/compliance_violation_type_spec.rb'
- './ee/spec/graphql/types/dast/profile_branch_type_spec.rb'
- './ee/spec/graphql/types/dast/profile_cadence_enum_spec.rb'
- './ee/spec/graphql/types/dast/profile_cadence_input_type_spec.rb'
- './ee/spec/graphql/types/dast/profile_cadence_type_spec.rb'
- './ee/spec/graphql/types/dast/profile_schedule_input_type_spec.rb'
- './ee/spec/graphql/types/dast/profile_schedule_type_spec.rb'
- './ee/spec/graphql/types/dast/profile_type_spec.rb'
- './ee/spec/graphql/types/dast/scan_method_type_enum_spec.rb'
- './ee/spec/graphql/types/dast_scanner_profile_type_spec.rb'
- './ee/spec/graphql/types/dast/site_profile_auth_input_type_spec.rb'
- './ee/spec/graphql/types/dast/site_profile_auth_type_spec.rb'
- './ee/spec/graphql/types/dast_site_profile_type_spec.rb'
- './ee/spec/graphql/types/dast_site_validation_type_spec.rb'
- './ee/spec/graphql/types/epic_descendant_count_type_spec.rb'
- './ee/spec/graphql/types/epic_descendant_weight_sum_type_spec.rb'
- './ee/spec/graphql/types/epic_issue_type_spec.rb'
- './ee/spec/graphql/types/epic_sort_enum_spec.rb'
- './ee/spec/graphql/types/epic_state_enum_spec.rb'
- './ee/spec/graphql/types/epic_type_spec.rb'
- './ee/spec/graphql/types/external_issue_type_spec.rb'
- './ee/spec/graphql/types/geo/ci_secure_file_registry_type_spec.rb'
- './ee/spec/graphql/types/geo/geo_node_type_spec.rb'
- './ee/spec/graphql/types/geo/job_artifact_registry_type_spec.rb'
- './ee/spec/graphql/types/geo/lfs_object_registry_type_spec.rb'
- './ee/spec/graphql/types/geo/merge_request_diff_registry_type_spec.rb'
- './ee/spec/graphql/types/geo/package_file_registry_type_spec.rb'
- './ee/spec/graphql/types/geo/pages_deployment_registry_type_spec.rb'
- './ee/spec/graphql/types/geo/pipeline_artifact_registry_type_spec.rb'
- './ee/spec/graphql/types/geo/registry_state_enum_spec.rb'
- './ee/spec/graphql/types/geo/terraform_state_version_registry_type_spec.rb'
- './ee/spec/graphql/types/geo/upload_registry_type_spec.rb'
- './ee/spec/graphql/types/global_id_type_spec.rb'
- './ee/spec/graphql/types/group_release_stats_type_spec.rb'
- './ee/spec/graphql/types/group_stats_type_spec.rb'
- './ee/spec/graphql/types/health_status_enum_spec.rb'
- './ee/spec/graphql/types/instance_security_dashboard_type_spec.rb'
- './ee/spec/graphql/types/issue_connection_type_spec.rb'
- './ee/spec/graphql/types/issue_type_spec.rb'
- './ee/spec/graphql/types/iterations/cadence_type_spec.rb'
- './ee/spec/graphql/types/iteration_type_spec.rb'
- './ee/spec/graphql/types/json_string_type_spec.rb'
- './ee/spec/graphql/types/merge_requests/approval_state_type_spec.rb'
- './ee/spec/graphql/types/metric_image_type_spec.rb'
- './ee/spec/graphql/types/move_type_enum_spec.rb'
- './ee/spec/graphql/types/path_lock_type_spec.rb'
- './ee/spec/graphql/types/permission_types/epic_spec.rb'
- './ee/spec/graphql/types/permission_types/project_spec.rb'
- './ee/spec/graphql/types/permission_types/vulnerability_spec.rb'
- './ee/spec/graphql/types/pipeline_security_report_finding_type_spec.rb'
- './ee/spec/graphql/types/projects/services_enum_spec.rb'
- './ee/spec/graphql/types/project_type_spec.rb'
- './ee/spec/graphql/types/push_rules_type_spec.rb'
- './ee/spec/graphql/types/query_type_spec.rb'
- './ee/spec/graphql/types/requirements_management/requirement_state_enum_spec.rb'
- './ee/spec/graphql/types/requirements_management/requirement_states_count_type_spec.rb'
- './ee/spec/graphql/types/requirements_management/requirement_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_details/table_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_details/text_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_details/url_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_detail_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_evidence_source_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_evidence_supporting_message_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_evidence_type_spec.rb'
- './ee/spec/graphql/types/vulnerability/external_issue_link_external_tracker_enum_spec.rb'
- './ee/spec/graphql/types/vulnerability/external_issue_link_type_enum_spec.rb'
- './ee/spec/graphql/types/vulnerability/external_issue_link_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_grade_enum_spec.rb'
- './ee/spec/graphql/types/vulnerability_identifier_input_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_identifier_type_spec.rb'
- './ee/spec/graphql/types/vulnerability/issue_link_type_enum_spec.rb'
- './ee/spec/graphql/types/vulnerability/issue_link_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_location/cluster_image_scanning_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_location/container_scanning_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_location/coverage_fuzzing_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_location/dast_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_location/dependency_scanning_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_location/generic_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_location/sast_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_location/secret_detection_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_location_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_report_type_enum_spec.rb'
- './ee/spec/graphql/types/vulnerability_request_response_header_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_request_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_response_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_scanner_input_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_scanner_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_scanner_vendor_input_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_severities_count_type_spec.rb'
- './ee/spec/graphql/types/vulnerability_severity_enum_spec.rb'
- './ee/spec/graphql/types/vulnerability_sort_enum_spec.rb'
- './ee/spec/graphql/types/vulnerability_state_enum_spec.rb'
- './ee/spec/graphql/types/vulnerable_dependency_type_spec.rb'
- './ee/spec/graphql/types/vulnerable_kubernetes_resource_type_spec.rb'
- './ee/spec/graphql/types/vulnerable_package_type_spec.rb'
- './ee/spec/graphql/types/vulnerable_projects_by_grade_type_spec.rb'
- './ee/spec/graphql/types/work_items/type_spec.rb'
- './ee/spec/graphql/types/work_items/widget_interface_spec.rb'
- './ee/spec/helpers/admin/emails_helper_spec.rb'
- './ee/spec/helpers/analytics/code_review_helper_spec.rb'
- './ee/spec/helpers/application_helper_spec.rb'
- './ee/spec/helpers/audit_events_helper_spec.rb'
- './ee/spec/helpers/billing_plans_helper_spec.rb'
- './ee/spec/helpers/boards_helper_spec.rb'
- './ee/spec/helpers/compliance_management/compliance_framework/group_settings_helper_spec.rb'
- './ee/spec/helpers/credentials_inventory_helper_spec.rb'
- './ee/spec/helpers/container_registry/container_registry_helper_spec.rb'
- './ee/spec/helpers/ee/access_tokens_helper_spec.rb'
- './ee/spec/helpers/ee/admin/identities_helper_spec.rb'
- './ee/spec/helpers/ee/auth_helper_spec.rb'
- './ee/spec/helpers/ee/blob_helper_spec.rb'
- './ee/spec/helpers/ee/branches_helper_spec.rb'
- './ee/spec/helpers/ee/ci/pipeline_editor_helper_spec.rb'
- './ee/spec/helpers/ee/ci/pipelines_helper_spec.rb'
- './ee/spec/helpers/ee/ci/runners_helper_spec.rb'
- './ee/spec/helpers/ee/dashboard_helper_spec.rb'
- './ee/spec/helpers/ee/emails_helper_spec.rb'
- './ee/spec/helpers/ee/events_helper_spec.rb'
- './ee/spec/helpers/ee/export_helper_spec.rb'
- './ee/spec/helpers/ee/feature_flags_helper_spec.rb'
- './ee/spec/helpers/ee/geo_helper_spec.rb'
- './ee/spec/helpers/ee/gitlab_routing_helper_spec.rb'
- './ee/spec/helpers/ee/graph_helper_spec.rb'
- './ee/spec/helpers/ee/groups/group_members_helper_spec.rb'
- './ee/spec/helpers/ee/groups_helper_spec.rb'
- './ee/spec/helpers/ee/groups/settings_helper_spec.rb'
- './ee/spec/helpers/ee/hooks_helper_spec.rb'
- './ee/spec/helpers/ee/integrations_helper_spec.rb'
- './ee/spec/helpers/ee/issuables_helper_spec.rb'
- './ee/spec/helpers/ee/issues_helper_spec.rb'
- './ee/spec/helpers/ee/labels_helper_spec.rb'
- './ee/spec/helpers/ee/namespaces_helper_spec.rb'
- './ee/spec/helpers/ee/operations_helper_spec.rb'
- './ee/spec/helpers/ee/personal_access_tokens_helper_spec.rb'
- './ee/spec/helpers/ee/profiles_helper_spec.rb'
- './ee/spec/helpers/ee/projects/pipeline_helper_spec.rb'
- './ee/spec/helpers/ee/projects/security/api_fuzzing_configuration_helper_spec.rb'
- './ee/spec/helpers/ee/projects/security/configuration_helper_spec.rb'
- './ee/spec/helpers/ee/projects/security/dast_configuration_helper_spec.rb'
- './ee/spec/helpers/ee/projects/security/sast_configuration_helper_spec.rb'
- './ee/spec/helpers/ee/registrations_helper_spec.rb'
- './ee/spec/helpers/ee/releases_helper_spec.rb'
- './ee/spec/helpers/ee/security_orchestration_helper_spec.rb'
- './ee/spec/helpers/ee/sorting_helper_spec.rb'
- './ee/spec/helpers/ee/subscribable_banner_helper_spec.rb'
- './ee/spec/helpers/ee/system_note_helper_spec.rb'
- './ee/spec/helpers/ee/users/callouts_helper_spec.rb'
- './ee/spec/helpers/ee/wiki_helper_spec.rb'
- './ee/spec/helpers/gitlab_subscriptions/upcoming_reconciliation_helper_spec.rb'
- './ee/spec/helpers/groups/ldap_sync_helper_spec.rb'
- './ee/spec/helpers/groups/security_features_helper_spec.rb'
- './ee/spec/helpers/kerberos_helper_spec.rb'
- './ee/spec/helpers/license_helper_spec.rb'
- './ee/spec/helpers/manual_quarterly_co_term_banner_helper_spec.rb'
- './ee/spec/helpers/markup_helper_spec.rb'
- './ee/spec/helpers/notes_helper_spec.rb'
- './ee/spec/helpers/path_locks_helper_spec.rb'
- './ee/spec/helpers/preferences_helper_spec.rb'
- './ee/spec/helpers/prevent_forking_helper_spec.rb'
- './ee/spec/helpers/projects/on_demand_scans_helper_spec.rb'
- './ee/spec/helpers/projects/project_members_helper_spec.rb'
- './ee/spec/helpers/projects/security/dast_profiles_helper_spec.rb'
- './ee/spec/helpers/projects/security/discover_helper_spec.rb'
- './ee/spec/helpers/push_rules_helper_spec.rb'
- './ee/spec/helpers/roadmaps_helper_spec.rb'
- './ee/spec/helpers/routing/pseudonymization_helper_spec.rb'
- './ee/spec/helpers/seat_count_alert_helper_spec.rb'
- './ee/spec/helpers/security_helper_spec.rb'
- './ee/spec/helpers/subscriptions_helper_spec.rb'
- './ee/spec/helpers/timeboxes_helper_spec.rb'
- './ee/spec/helpers/users_helper_spec.rb'
- './ee/spec/helpers/vulnerabilities_helper_spec.rb'
- './ee/spec/initializers/1_settings_spec.rb'
- './ee/spec/initializers/database_config_spec.rb'
- './ee/spec/initializers/fog_google_https_private_urls_spec.rb'
- './ee/spec/initializers/session_store_spec.rb'
- './ee/spec/lib/analytics/devops_adoption/snapshot_calculator_spec.rb'
- './ee/spec/lib/analytics/group_activity_calculator_spec.rb'
- './ee/spec/lib/analytics/merge_request_metrics_calculator_spec.rb'
- './ee/spec/lib/analytics/merge_request_metrics_refresh_spec.rb'
- './ee/spec/lib/analytics/productivity_analytics_request_params_spec.rb'
- './ee/spec/lib/analytics/refresh_approvals_data_spec.rb'
- './ee/spec/lib/analytics/refresh_comments_data_spec.rb'
- './ee/spec/lib/analytics/refresh_reassign_data_spec.rb'
- './ee/spec/lib/api/entities/deployments/approval_spec.rb'
- './ee/spec/lib/api/entities/deployments/approval_summary_spec.rb'
- './ee/spec/lib/api/entities/merge_request_approval_setting_spec.rb'
- './ee/spec/lib/api/entities/pending_member_spec.rb'
- './ee/spec/lib/api/entities/protected_environments/approval_rule_for_summary_spec.rb'
- './ee/spec/lib/api/entities/protected_environments/approval_rule_spec.rb'
- './ee/spec/lib/api/entities/protected_environments/deploy_access_level_spec.rb'
- './ee/spec/lib/arkose/settings_spec.rb'
- './ee/spec/lib/arkose/verify_response_spec.rb'
- './ee/spec/lib/audit/changes_spec.rb'
- './ee/spec/lib/audit/details_spec.rb'
- './ee/spec/lib/merge_requests/external_status_check_changes_auditor_spec.rb'
- './ee/spec/lib/audit/group_merge_request_approval_setting_changes_auditor_spec.rb'
- './ee/spec/lib/banzai/filter/issuable_reference_expansion_filter_spec.rb'
- './ee/spec/lib/banzai/filter/jira_private_image_link_filter_spec.rb'
- './ee/spec/lib/banzai/filter/references/epic_reference_filter_spec.rb'
- './ee/spec/lib/banzai/filter/references/iteration_reference_filter_spec.rb'
- './ee/spec/lib/banzai/filter/references/label_reference_filter_spec.rb'
- './ee/spec/lib/banzai/filter/references/vulnerability_reference_filters_spec.rb'
- './ee/spec/lib/banzai/issuable_extractor_spec.rb'
- './ee/spec/lib/banzai/reference_parser/epic_parser_spec.rb'
- './ee/spec/lib/banzai/reference_parser/iteration_parser_spec.rb'
- './ee/spec/lib/banzai/reference_parser/vulnerability_parser_spec.rb'
- './ee/spec/lib/bulk_imports/common/pipelines/boards_pipeline_spec.rb'
- './ee/spec/lib/bulk_imports/common/pipelines/wiki_pipeline_spec.rb'
- './ee/spec/lib/bulk_imports/groups/graphql/get_iterations_query_spec.rb'
- './ee/spec/lib/bulk_imports/groups/pipelines/epics_pipeline_spec.rb'
- './ee/spec/lib/bulk_imports/groups/pipelines/iterations_pipeline_spec.rb'
- './ee/spec/lib/bulk_imports/projects/pipelines/issues_pipeline_spec.rb'
- './ee/spec/lib/bulk_imports/projects/pipelines/protected_branches_pipeline_spec.rb'
- './ee/spec/lib/bulk_imports/projects/pipelines/push_rule_pipeline_spec.rb'
- './ee/spec/lib/compliance_management/merge_request_approval_settings/resolver_spec.rb'
- './ee/spec/lib/container_registry/client_spec.rb'
- './ee/spec/lib/ee/api/entities/analytics/code_review/merge_request_spec.rb'
- './ee/spec/lib/ee/api/entities/analytics/group_activity_spec.rb'
- './ee/spec/lib/ee/api/entities/billable_member_spec.rb'
- './ee/spec/lib/ee/api/entities/ci/minutes/additional_pack_spec.rb'
- './ee/spec/lib/ee/api/entities/deployment_extended_spec.rb'
- './ee/spec/lib/ee/api/entities/experiment_spec.rb'
- './ee/spec/lib/ee/api/entities/geo_node_status_spec.rb'
- './ee/spec/lib/ee/api/entities/group_detail_spec.rb'
- './ee/spec/lib/ee/api/entities/groups/repository_storage_move_spec.rb'
- './ee/spec/lib/ee/api/entities/member_spec.rb'
- './ee/spec/lib/ee/api/entities/project_spec.rb'
- './ee/spec/lib/ee/api/entities/user_with_admin_spec.rb'
- './ee/spec/lib/ee/api/entities/vulnerability_export_spec.rb'
- './ee/spec/lib/ee/api/entities/vulnerability_spec.rb'
- './ee/spec/lib/ee/api/helpers/issues_helpers_spec.rb'
- './ee/spec/lib/ee/api/helpers/members_helpers_spec.rb'
- './ee/spec/lib/ee/api/helpers/notes_helpers_spec.rb'
- './ee/spec/lib/ee/api/helpers/scim_pagination_spec.rb'
- './ee/spec/lib/ee/api/helpers_spec.rb'
- './ee/spec/lib/ee/api/helpers/variables_helpers_spec.rb'
- './ee/spec/lib/ee/banzai/filter/sanitization_filter_spec.rb'
- './ee/spec/lib/ee/bulk_imports/groups/stage_spec.rb'
- './ee/spec/lib/ee/bulk_imports/projects/stage_spec.rb'
- './ee/spec/lib/ee/event_filter_spec.rb'
- './ee/spec/lib/ee/feature_spec.rb'
- './ee/spec/lib/ee/gitlab/alert_management/payload/generic_spec.rb'
- './ee/spec/lib/ee/gitlab/analytics/cycle_analytics/aggregated/base_query_builder_spec.rb'
- './ee/spec/lib/ee/gitlab/analytics/cycle_analytics/base_query_builder_spec.rb'
- './ee/spec/lib/ee/gitlab/application_context_spec.rb'
- './ee/spec/lib/ee/gitlab/application_rate_limiter_spec.rb'
- './ee/spec/lib/ee/gitlab/auth/auth_finders_spec.rb'
- './ee/spec/lib/ee/gitlab/auth/ldap/access_levels_spec.rb'
- './ee/spec/lib/ee/gitlab/auth/ldap/config_spec.rb'
- './ee/spec/lib/ee/gitlab/auth/ldap/group_spec.rb'
- './ee/spec/lib/ee/gitlab/auth/ldap/sync/admin_users_spec.rb'
- './ee/spec/lib/ee/gitlab/auth/ldap/sync/external_users_spec.rb'
- './ee/spec/lib/ee/gitlab/auth/ldap/sync/group_spec.rb'
- './ee/spec/lib/ee/gitlab/auth/ldap/sync/groups_spec.rb'
- './ee/spec/lib/ee/gitlab/auth/ldap/sync/proxy_spec.rb'
- './ee/spec/lib/ee/gitlab/auth/request_authenticator_spec.rb'
- './ee/spec/lib/ee/gitlab/auth/saml/identity_linker_spec.rb'
- './ee/spec/lib/ee/gitlab/background_migration/backfill_iteration_cadence_id_for_boards_spec.rb'
- './ee/spec/lib/ee/gitlab/background_migration/delete_invalid_epic_issues_spec.rb'
- './ee/spec/lib/ee/gitlab/background_migration/migrate_approver_to_approval_rules_check_progress_spec.rb'
- './ee/spec/lib/ee/gitlab/background_migration/migrate_approver_to_approval_rules_in_batch_spec.rb'
- './ee/spec/lib/ee/gitlab/background_migration/migrate_approver_to_approval_rules_spec.rb'
- './ee/spec/lib/ee/gitlab/checks/push_rule_check_spec.rb'
- './ee/spec/lib/ee/gitlab/checks/push_rules/branch_check_spec.rb'
- './ee/spec/lib/ee/gitlab/checks/push_rules/commit_check_spec.rb'
- './ee/spec/lib/ee/gitlab/checks/push_rules/tag_check_spec.rb'
- './ee/spec/lib/ee/gitlab/ci/config/entry/bridge_spec.rb'
- './ee/spec/lib/ee/gitlab/ci/config/entry/need_spec.rb'
- './ee/spec/lib/ee/gitlab/ci/config/entry/needs_spec.rb'
- './ee/spec/lib/ee/gitlab/ci/config_spec.rb'
- './ee/spec/lib/ee/gitlab/ci/jwt_spec.rb'
- './ee/spec/lib/ee/gitlab/ci/matching/runner_matcher_spec.rb'
- './ee/spec/lib/ee/gitlab/ci/parsers/security/common_spec.rb'
- './ee/spec/lib/ee/gitlab/ci/pipeline/chain/validate/abilities_spec.rb'
- './ee/spec/lib/ee/gitlab/ci/pipeline/chain/validate/after_config_spec.rb'
- './ee/spec/lib/ee/gitlab/ci/pipeline/chain/validate/external_spec.rb'
- './ee/spec/lib/ee/gitlab/ci/pipeline/chain/validate/security_orchestration_policy_spec.rb'
- './ee/spec/lib/ee/gitlab/ci/pipeline/quota/size_spec.rb'
- './ee/spec/lib/ee/gitlab/ci/status/build/manual_spec.rb'
- './ee/spec/lib/ee/gitlab/ci/templates/templates_spec.rb'
- './ee/spec/lib/ee/gitlab/cleanup/orphan_job_artifact_files_batch_spec.rb'
- './ee/spec/lib/ee/gitlab/cleanup/orphan_job_artifact_files_spec.rb'
- './ee/spec/lib/ee/gitlab/database_spec.rb'
- './ee/spec/lib/ee/gitlab/email/handler/service_desk_handler_spec.rb'
- './ee/spec/lib/ee/gitlab/etag_caching/router/rails_spec.rb'
- './ee/spec/lib/ee/gitlab/git_access_design_spec.rb'
- './ee/spec/lib/ee/gitlab/git_access_project_spec.rb'
- './ee/spec/lib/ee/gitlab/git_access_snippet_spec.rb'
- './ee/spec/lib/ee/gitlab/gon_helper_spec.rb'
- './ee/spec/lib/ee/gitlab/group_search_results_spec.rb'
- './ee/spec/lib/ee/gitlab/hook_data/group_member_builder_spec.rb'
- './ee/spec/lib/ee/gitlab/hook_data/issue_builder_spec.rb'
- './ee/spec/lib/ee/gitlab/import_export/group/tree_restorer_spec.rb'
- './ee/spec/lib/ee/gitlab/import_export/group/tree_saver_spec.rb'
- './ee/spec/lib/ee/gitlab/import_export/project/tree_restorer_spec.rb'
- './ee/spec/lib/ee/gitlab/import_export/project/tree_saver_spec.rb'
- './ee/spec/lib/ee/gitlab/import_export/repo_restorer_spec.rb'
- './ee/spec/lib/ee/gitlab/import_export/wiki_repo_saver_spec.rb'
- './ee/spec/lib/ee/gitlab/ip_restriction/enforcer_spec.rb'
- './ee/spec/lib/ee/gitlab/issuable/clone/copy_resource_events_service_spec.rb'
- './ee/spec/lib/ee/gitlab/issuable_metadata_spec.rb'
- './ee/spec/lib/ee/gitlab/metrics/samplers/database_sampler_spec.rb'
- './ee/spec/lib/ee/gitlab/middleware/read_only_spec.rb'
- './ee/spec/lib/ee/gitlab/namespace_storage_size_error_message_spec.rb'
- './ee/spec/lib/ee/gitlab/omniauth_initializer_spec.rb'
- './ee/spec/lib/ee/gitlab/rack_attack/request_spec.rb'
- './ee/spec/lib/ee/gitlab/repo_path_spec.rb'
- './ee/spec/lib/ee/gitlab/repository_size_checker_spec.rb'
- './ee/spec/lib/ee/gitlab/search_results_spec.rb'
- './ee/spec/lib/ee/gitlab/security/scan_configuration_spec.rb'
- './ee/spec/lib/ee/gitlab/snippet_search_results_spec.rb'
- './ee/spec/lib/ee/gitlab/template/gitlab_ci_yml_template_spec.rb'
- './ee/spec/lib/ee/gitlab/url_builder_spec.rb'
- './ee/spec/lib/ee/gitlab/usage_data_counters/issue_activity_unique_counter_spec.rb'
- './ee/spec/lib/ee/gitlab/usage_data_counters/merge_request_activity_unique_counter_spec.rb'
- './ee/spec/lib/ee/gitlab/usage_data_counters/work_item_activity_unique_counter_spec.rb'
- './ee/spec/lib/ee/gitlab/usage_data_non_sql_metrics_spec.rb'
- './ee/spec/lib/ee/gitlab/usage_data_spec.rb'
- './ee/spec/lib/ee/gitlab/usage/service_ping/payload_keys_processor_spec.rb'
- './ee/spec/lib/ee/gitlab/usage/service_ping_report_spec.rb'
- './ee/spec/lib/ee/gitlab/verify/lfs_objects_spec.rb'
- './ee/spec/lib/ee/gitlab/verify/uploads_spec.rb'
- './ee/spec/lib/ee/gitlab/web_hooks/rate_limiter_spec.rb'
- './ee/spec/lib/ee/web_ide/config/entry/global_spec.rb'
- './ee/spec/lib/ee/service_ping/build_payload_spec.rb'
- './ee/spec/lib/ee/service_ping/permit_data_categories_spec.rb'
- './ee/spec/lib/ee/service_ping/service_ping_settings_spec.rb'
- './ee/spec/lib/ee/sidebars/groups/menus/issues_menu_spec.rb'
- './ee/spec/lib/ee/sidebars/groups/menus/settings_menu_spec.rb'
- './ee/spec/lib/ee/sidebars/projects/menus/analytics_menu_spec.rb'
- './ee/spec/lib/ee/sidebars/projects/menus/ci_cd_menu_spec.rb'
- './ee/spec/lib/ee/sidebars/projects/menus/issues_menu_spec.rb'
- './ee/spec/lib/ee/sidebars/projects/menus/monitor_menu_spec.rb'
- './ee/spec/lib/ee/sidebars/projects/menus/repository_menu_spec.rb'
- './ee/spec/lib/ee/sidebars/projects/menus/security_compliance_menu_spec.rb'
- './ee/spec/lib/ee/sidebars/projects/panel_spec.rb'
- './ee/spec/lib/gitlab/analytics/cycle_analytics/request_params_spec.rb'
- './ee/spec/lib/gitlab/analytics/cycle_analytics/stage_events/issue_closed_spec.rb'
- './ee/spec/lib/gitlab/analytics/cycle_analytics/stage_events/issue_first_added_to_board_spec.rb'
- './ee/spec/lib/gitlab/analytics/cycle_analytics/stage_events/issue_first_associated_with_milestone_spec.rb'
- './ee/spec/lib/gitlab/analytics/cycle_analytics/stage_events/issue_label_added_spec.rb'
- './ee/spec/lib/gitlab/analytics/cycle_analytics/stage_events/issue_label_removed_spec.rb'
- './ee/spec/lib/gitlab/analytics/cycle_analytics/stage_events/issue_last_edited_spec.rb'
- './ee/spec/lib/gitlab/analytics/cycle_analytics/stage_events/merge_request_closed_spec.rb'
- './ee/spec/lib/gitlab/analytics/cycle_analytics/stage_events/merge_request_first_commit_at_spec.rb'
- './ee/spec/lib/gitlab/analytics/cycle_analytics/stage_events/merge_request_label_added_spec.rb'
- './ee/spec/lib/gitlab/analytics/cycle_analytics/stage_events/merge_request_label_removed_spec.rb'
- './ee/spec/lib/gitlab/analytics/cycle_analytics/stage_events/merge_request_last_edited_spec.rb'
- './ee/spec/lib/gitlab/analytics/cycle_analytics/summary/base_dora_summary_spec.rb'
- './ee/spec/lib/gitlab/analytics/cycle_analytics/summary/change_failure_rate_spec.rb'
- './ee/spec/lib/gitlab/analytics/cycle_analytics/summary/group/stage_summary_spec.rb'
- './ee/spec/lib/gitlab/analytics/cycle_analytics/summary/lead_time_for_changes_spec.rb'
- './ee/spec/lib/gitlab/analytics/cycle_analytics/summary/lead_time_spec.rb'
- './ee/spec/lib/gitlab/analytics/cycle_analytics/summary/stage_time_summary_spec.rb'
- './ee/spec/lib/gitlab/analytics/cycle_analytics/summary/time_to_restore_service_spec.rb'
- './ee/spec/lib/gitlab/analytics/type_of_work/tasks_by_type_spec.rb'
- './ee/spec/lib/gitlab/audit/auditor_spec.rb'
- './ee/spec/lib/gitlab/audit/events/preloader_spec.rb'
- './ee/spec/lib/gitlab/audit/levels/group_spec.rb'
- './ee/spec/lib/gitlab/audit/levels/instance_spec.rb'
- './ee/spec/lib/gitlab/audit/levels/project_spec.rb'
- './ee/spec/lib/gitlab/auth/group_saml/auth_hash_spec.rb'
- './ee/spec/lib/gitlab/auth/group_saml/dynamic_settings_spec.rb'
- './ee/spec/lib/gitlab/auth/group_saml/failure_handler_spec.rb'
- './ee/spec/lib/gitlab/auth/group_saml/gma_membership_enforcer_spec.rb'
- './ee/spec/lib/gitlab/auth/group_saml/group_lookup_spec.rb'
- './ee/spec/lib/gitlab/auth/group_saml/identity_linker_spec.rb'
- './ee/spec/lib/gitlab/auth/group_saml/membership_enforcer_spec.rb'
- './ee/spec/lib/gitlab/auth/group_saml/membership_updater_spec.rb'
- './ee/spec/lib/gitlab/auth/group_saml/response_check_spec.rb'
- './ee/spec/lib/gitlab/auth/group_saml/response_store_spec.rb'
- './ee/spec/lib/gitlab/auth/group_saml/session_enforcer_spec.rb'
- './ee/spec/lib/gitlab/auth/group_saml/sso_enforcer_spec.rb'
- './ee/spec/lib/gitlab/auth/group_saml/sso_state_spec.rb'
- './ee/spec/lib/gitlab/auth/group_saml/token_actor_spec.rb'
- './ee/spec/lib/gitlab/auth/group_saml/user_spec.rb'
- './ee/spec/lib/gitlab/auth/group_saml/xml_response_spec.rb'
- './ee/spec/lib/gitlab/auth/ldap/access_spec.rb'
- './ee/spec/lib/gitlab/auth/ldap/adapter_spec.rb'
- './ee/spec/lib/gitlab/auth/ldap/person_spec.rb'
- './ee/spec/lib/gitlab/auth/ldap/user_spec.rb'
- './ee/spec/lib/gitlab/auth/o_auth/auth_hash_spec.rb'
- './ee/spec/lib/gitlab/ci/minutes/cached_quota_spec.rb'
- './ee/spec/lib/gitlab/ci/templates/sast_iac_gitlab_ci_yaml_spec.rb'
- './ee/spec/lib/gitlab/ci/templates/sast_latest_gitlab_ci_yaml_spec.rb'
- './ee/spec/lib/gitlab/ci/templates/secret_detection_gitlab_ci_yaml_spec.rb'
- './ee/spec/lib/gitlab/ci/templates/secret_detection_latest_gitlab_ci_yaml_spec.rb'
- './ee/spec/lib/gitlab/ci/templates/secure_binaries_ci_yaml_spec.rb'
- './ee/spec/lib/gitlab/ci/templates/Verify/browser_performance_testing_gitlab_ci_yaml_spec.rb'
- './ee/spec/lib/gitlab/ci/templates/Verify/load_performance_testing_gitlab_ci_yaml_spec.rb'
- './ee/spec/lib/gitlab/ci/yaml_processor_spec.rb'
- './ee/spec/lib/gitlab/code_owners/entry_spec.rb'
- './ee/spec/lib/gitlab/code_owners/file_spec.rb'
- './ee/spec/lib/gitlab/code_owners/groups_loader_spec.rb'
- './ee/spec/lib/gitlab/code_owners/loader_spec.rb'
- './ee/spec/lib/gitlab/code_owners/reference_extractor_spec.rb'
- './ee/spec/lib/gitlab/code_owners_spec.rb'
- './ee/spec/lib/gitlab/code_owners/users_loader_spec.rb'
- './ee/spec/lib/gitlab/code_owners/validator_spec.rb'
- './ee/spec/lib/gitlab/compliance_management/violations/approved_by_committer_spec.rb'
- './ee/spec/lib/gitlab/compliance_management/violations/approved_by_insufficient_users_spec.rb'
- './ee/spec/lib/gitlab/compliance_management/violations/approved_by_merge_request_author_spec.rb'
- './ee/spec/lib/gitlab/com_spec.rb'
- './ee/spec/lib/gitlab/console_spec.rb'
- './ee/spec/lib/gitlab/contribution_analytics/data_collector_spec.rb'
- './ee/spec/lib/gitlab/customers_dot/jwt_spec.rb'
- './ee/spec/lib/gitlab/custom_file_templates_spec.rb'
- './ee/spec/lib/gitlab/cycle_analytics/stage_summary_spec.rb'
- './ee/spec/lib/gitlab/data_builder/vulnerability_spec.rb'
- './ee/spec/lib/gitlab/elastic/bulk_indexer_spec.rb'
- './ee/spec/lib/gitlab/elastic/group_search_results_spec.rb'
- './ee/spec/lib/gitlab/elastic/project_search_results_spec.rb'
- './ee/spec/lib/gitlab/email/handler/create_note_handler_spec.rb'
- './ee/spec/lib/gitlab/exclusive_lease_spec.rb'
- './ee/spec/lib/gitlab/expiring_subscription_message_spec.rb'
- './ee/spec/lib/gitlab/favicon_spec.rb'
- './ee/spec/lib/gitlab/geo/base_request_spec.rb'
- './ee/spec/lib/gitlab/geo/cron_manager_spec.rb'
- './ee/spec/lib/gitlab/geo/event_gap_tracking_spec.rb'
- './ee/spec/lib/gitlab/geo/geo_node_status_check_spec.rb'
- './ee/spec/lib/gitlab/geo/geo_tasks_spec.rb'
- './ee/spec/lib/gitlab/geo/git_push_http_spec.rb'
- './ee/spec/lib/gitlab/geo/git_ssh_proxy_spec.rb'
- './ee/spec/lib/gitlab/geo/health_check_spec.rb'
- './ee/spec/lib/gitlab/geo/json_request_spec.rb'
- './ee/spec/lib/gitlab/geo/jwt_request_decoder_spec.rb'
- './ee/spec/lib/gitlab/geo/log_cursor/daemon_spec.rb'
- './ee/spec/lib/gitlab/geo/log_cursor/event_logs_spec.rb'
- './ee/spec/lib/gitlab/geo/log_cursor/events/cache_invalidation_event_spec.rb'
- './ee/spec/lib/gitlab/geo/log_cursor/events/event_spec.rb'
- './ee/spec/lib/gitlab/geo/log_cursor/lease_spec.rb'
- './ee/spec/lib/gitlab/geo/log_cursor/logger_spec.rb'
- './ee/spec/lib/gitlab/geo/logger_spec.rb'
- './ee/spec/lib/gitlab/geo/log_helpers_spec.rb'
- './ee/spec/lib/gitlab/geo/oauth/login_state_spec.rb'
- './ee/spec/lib/gitlab/geo/oauth/logout_state_spec.rb'
- './ee/spec/lib/gitlab/geo/oauth/logout_token_spec.rb'
- './ee/spec/lib/gitlab/geo/oauth/session_spec.rb'
- './ee/spec/lib/gitlab/geo/registry_batcher_spec.rb'
- './ee/spec/lib/gitlab/geo/replication/blob_retriever_spec.rb'
- './ee/spec/lib/gitlab/geo/replicator_spec.rb'
- './ee/spec/lib/gitlab/geo/signed_data_spec.rb'
- './ee/spec/lib/gitlab/geo_spec.rb'
- './ee/spec/lib/gitlab/git_access_spec.rb'
- './ee/spec/lib/gitlab/git_access_wiki_spec.rb'
- './ee/spec/lib/gitlab/gl_repository/identifier_spec.rb'
- './ee/spec/lib/gitlab/gl_repository/repo_type_spec.rb'
- './ee/spec/lib/gitlab/gl_repository_spec.rb'
- './ee/spec/lib/gitlab/graphql/aggregations/epics/epic_node_spec.rb'
- './ee/spec/lib/gitlab/graphql/aggregations/epics/lazy_epic_aggregate_spec.rb'
- './ee/spec/lib/gitlab/graphql/aggregations/epics/lazy_links_aggregate_spec.rb'
- './ee/spec/lib/gitlab/graphql/aggregations/issuables/lazy_links_aggregate_spec.rb'
- './ee/spec/lib/gitlab/graphql/aggregations/issues/lazy_links_aggregate_spec.rb'
- './ee/spec/lib/gitlab/graphql/aggregations/security_orchestration_policies/lazy_dast_profile_aggregate_spec.rb'
- './ee/spec/lib/gitlab/graphql/aggregations/vulnerabilities/lazy_user_notes_count_aggregate_spec.rb'
- './ee/spec/lib/gitlab/graphql/aggregations/vulnerability_statistics/lazy_aggregate_spec.rb'
- './ee/spec/lib/gitlab/graphql/loaders/bulk_epic_aggregate_loader_spec.rb'
- './ee/spec/lib/gitlab/graphql/loaders/oncall_participant_loader_spec.rb'
- './ee/spec/lib/gitlab/group_plans_preloader_spec.rb'
- './ee/spec/lib/gitlab/import_export/attributes_permitter_spec.rb'
- './ee/spec/lib/gitlab/import_export/group/group_and_descendants_repo_restorer_spec.rb'
- './ee/spec/lib/gitlab/import_export/group/group_and_descendants_repo_saver_spec.rb'
- './ee/spec/lib/gitlab/import_export/group/relation_factory_spec.rb'
- './ee/spec/lib/gitlab/import_export/project/object_builder_spec.rb'
- './ee/spec/lib/gitlab/import_sources_spec.rb'
- './ee/spec/lib/gitlab/ingestion/bulk_insertable_task_spec.rb'
- './ee/spec/lib/gitlab/insights/executors/dora_executor_spec.rb'
- './ee/spec/lib/gitlab/insights/executors/issuable_executor_spec.rb'
- './ee/spec/lib/gitlab/insights/finders/issuable_finder_spec.rb'
- './ee/spec/lib/gitlab/insights/finders/projects_finder_spec.rb'
- './ee/spec/lib/gitlab/insights/loader_spec.rb'
- './ee/spec/lib/gitlab/insights/project_insights_config_spec.rb'
- './ee/spec/lib/gitlab/insights/reducers/base_reducer_spec.rb'
- './ee/spec/lib/gitlab/insights/reducers/count_per_label_reducer_spec.rb'
- './ee/spec/lib/gitlab/insights/reducers/count_per_period_reducer_spec.rb'
- './ee/spec/lib/gitlab/insights/reducers/label_count_per_period_reducer_spec.rb'
- './ee/spec/lib/gitlab/insights/serializers/chartjs/bar_serializer_spec.rb'
- './ee/spec/lib/gitlab/insights/serializers/chartjs/bar_time_series_serializer_spec.rb'
- './ee/spec/lib/gitlab/insights/serializers/chartjs/line_serializer_spec.rb'
- './ee/spec/lib/gitlab/insights/serializers/chartjs/multi_series_serializer_spec.rb'
- './ee/spec/lib/gitlab/insights/validators/params_validator_spec.rb'
- './ee/spec/lib/gitlab/instrumentation/elasticsearch_transport_spec.rb'
- './ee/spec/lib/gitlab/instrumentation_helper_spec.rb'
- './ee/spec/lib/gitlab/items_collection_spec.rb'
- './ee/spec/lib/gitlab/kerberos/authentication_spec.rb'
- './ee/spec/lib/gitlab/legacy_github_import/project_creator_spec.rb'
- './ee/spec/lib/gitlab/licenses/submit_license_usage_data_banner_spec.rb'
- './ee/spec/lib/gitlab/manual_quarterly_co_term_banner_spec.rb'
- './ee/spec/lib/gitlab/metrics/samplers/global_search_sampler_spec.rb'
- './ee/spec/lib/gitlab/middleware/ip_restrictor_spec.rb'
- './ee/spec/lib/gitlab/mirror_spec.rb'
- './ee/spec/lib/gitlab/object_hierarchy_spec.rb'
- './ee/spec/lib/gitlab/pagination/keyset/simple_order_builder_spec.rb'
- './ee/spec/lib/gitlab/patch/database_config_spec.rb'
- './ee/spec/lib/gitlab/patch/draw_route_spec.rb'
- './ee/spec/lib/gitlab/path_locks_finder_spec.rb'
- './ee/spec/lib/gitlab/project_template_spec.rb'
- './ee/spec/lib/gitlab/proxy_spec.rb'
- './ee/spec/lib/gitlab/quick_actions/users_extractor_spec.rb'
- './ee/spec/lib/gitlab/rack_attack_spec.rb'
- './ee/spec/lib/gitlab/reference_extractor_spec.rb'
- './ee/spec/lib/gitlab/regex_spec.rb'
- './ee/spec/lib/gitlab/return_to_location_spec.rb'
- './ee/spec/lib/gitlab/search/aggregation_spec.rb'
- './ee/spec/lib/gitlab/search_context/builder_spec.rb'
- './ee/spec/lib/gitlab/search/recent_epics_spec.rb'
- './ee/spec/lib/gitlab/sidekiq_config_spec.rb'
- './ee/spec/lib/gitlab/sitemaps/generator_spec.rb'
- './ee/spec/lib/gitlab/sitemaps/sitemap_file_spec.rb'
- './ee/spec/lib/gitlab/sitemaps/url_extractor_spec.rb'
- './ee/spec/lib/gitlab/slash_commands/presenters/issue_show_spec.rb'
- './ee/spec/lib/gitlab/spdx/catalogue_gateway_spec.rb'
- './ee/spec/lib/gitlab/spdx/catalogue_spec.rb'
- './ee/spec/lib/gitlab/subscription_portal/clients/graphql_spec.rb'
- './ee/spec/lib/gitlab/subscription_portal/client_spec.rb'
- './ee/spec/lib/gitlab/subscription_portal/clients/rest_spec.rb'
- './ee/spec/lib/gitlab_subscriptions/upcoming_reconciliation_entity_spec.rb'
- './ee/spec/lib/gitlab/template/custom_templates_spec.rb'
- './ee/spec/lib/gitlab/tracking/standard_context_spec.rb'
- './ee/spec/lib/gitlab/tree_summary_spec.rb'
- './ee/spec/lib/gitlab/usage_data_counters/epic_activity_unique_counter_spec.rb'
- './ee/spec/lib/gitlab/usage_data_metrics_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/advanced_search/build_type_metric_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/advanced_search/distribution_metric_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/advanced_search/lucene_version_metric_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/advanced_search/version_metric_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/approval_project_rules_with_user_metric_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/count_ci_builds_metric_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/count_ci_environments_approval_required_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/count_deployment_approvals_metric_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/count_distinct_merged_merge_requests_using_approval_rules_metric_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/count_event_streaming_destinations_metric_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/count_external_status_checks_metric_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/count_groups_with_event_streaming_destinations_metric_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/count_projects_with_external_status_checks_metric_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/count_saml_group_links_metric_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/count_users_associating_group_milestones_to_releases_metric_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/count_users_creating_ci_builds_metric_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/count_users_deployment_approvals_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/historical_max_users_metrics_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/licensee_metrics_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/license_metric_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/protected_environment_approval_rules_required_approvals_average_metric_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/protected_environments_required_approvals_average_metric_spec.rb'
- './ee/spec/lib/gitlab/usage/metrics/instrumentations/user_cap_setting_enabled_metric_spec.rb'
- './ee/spec/lib/gitlab/user_access_spec.rb'
- './ee/spec/lib/gitlab/visibility_level_spec.rb'
- './ee/spec/lib/web_ide/config/entry/schema/match_spec.rb'
- './ee/spec/lib/web_ide/config/entry/schema_spec.rb'
- './ee/spec/lib/web_ide/config/entry/schemas_spec.rb'
- './ee/spec/lib/web_ide/config/entry/schema/uri_spec.rb'
- './ee/spec/lib/omni_auth/strategies/group_saml_spec.rb'
- './ee/spec/lib/omni_auth/strategies/kerberos_spec.rb'
- './ee/spec/lib/peek/views/elasticsearch_spec.rb'
- './ee/spec/lib/sidebars/groups/menus/analytics_menu_spec.rb'
- './ee/spec/lib/sidebars/groups/menus/epics_menu_spec.rb'
- './ee/spec/lib/sidebars/groups/menus/security_compliance_menu_spec.rb'
- './ee/spec/lib/sidebars/groups/menus/wiki_menu_spec.rb'
- './ee/spec/lib/system_check/app/search_check_spec.rb'
- './ee/spec/lib/system_check/geo/authorized_keys_check_spec.rb'
- './ee/spec/lib/system_check/geo/authorized_keys_flag_check_spec.rb'
- './ee/spec/lib/system_check/geo/current_node_check_spec.rb'
- './ee/spec/lib/system_check/geo/geo_database_configured_check_spec.rb'
- './ee/spec/lib/system_check/geo/http_clone_enabled_check_spec.rb'
- './ee/spec/lib/system_check/geo/http_connection_check_spec.rb'
- './ee/spec/lib/system_check/geo/license_check_spec.rb'
- './ee/spec/lib/system_check/rake_task/geo_task_spec.rb'
- './ee/spec/lib/world_spec.rb'
- './ee/spec/mailers/ci_minutes_usage_mailer_spec.rb'
- './ee/spec/mailers/credentials_inventory_mailer_spec.rb'
- './ee/spec/mailers/devise_mailer_spec.rb'
- './ee/spec/mailers/ee/emails/admin_notification_spec.rb'
- './ee/spec/mailers/ee/emails/issues_spec.rb'
- './ee/spec/mailers/ee/emails/profile_spec.rb'
- './ee/spec/mailers/ee/emails/projects_spec.rb'
- './ee/spec/mailers/emails/epics_spec.rb'
- './ee/spec/mailers/emails/group_memberships_spec.rb'
- './ee/spec/mailers/emails/merge_commits_spec.rb'
- './ee/spec/mailers/emails/requirements_spec.rb'
- './ee/spec/mailers/emails/user_cap_spec.rb'
- './ee/spec/mailers/license_mailer_spec.rb'
- './ee/spec/mailers/notify_spec.rb'
- './ee/spec/migrations/geo/fix_state_column_in_file_registry_spec.rb'
- './ee/spec/migrations/geo/fix_state_column_in_lfs_object_registry_spec.rb'
- './ee/spec/migrations/geo/migrate_ci_job_artifacts_to_separate_registry_spec.rb'
- './ee/spec/migrations/geo/migrate_lfs_objects_to_separate_registry_spec.rb'
- './ee/spec/migrations/geo/set_resync_flag_for_retried_projects_spec.rb'
- './ee/spec/models/alert_management/alert_payload_field_spec.rb'
- './ee/spec/models/allowed_email_domain_spec.rb'
- './ee/spec/models/analytics/cycle_analytics/aggregation_context_spec.rb'
- './ee/spec/models/analytics/cycle_analytics/group_level_spec.rb'
- './ee/spec/models/analytics/devops_adoption/enabled_namespace_spec.rb'
- './ee/spec/models/analytics/devops_adoption/snapshot_spec.rb'
- './ee/spec/models/analytics/language_trend/repository_language_spec.rb'
- './ee/spec/models/approval_merge_request_rule_spec.rb'
- './ee/spec/models/approval_project_rule_spec.rb'
- './ee/spec/models/approvals/scan_finding_wrapped_rule_set_spec.rb'
- './ee/spec/models/approval_state_spec.rb'
- './ee/spec/models/approvals/wrapped_rule_set_spec.rb'
- './ee/spec/models/approval_wrapped_any_approver_rule_spec.rb'
- './ee/spec/models/approval_wrapped_code_owner_rule_spec.rb'
- './ee/spec/models/approval_wrapped_rule_spec.rb'
- './ee/spec/models/approver_group_spec.rb'
- './ee/spec/models/app_sec/fuzzing/api/ci_configuration_spec.rb'
- './ee/spec/models/app_sec/fuzzing/coverage/corpus_spec.rb'
- './ee/spec/models/audit_events/external_audit_event_destination_spec.rb'
- './ee/spec/models/audit_events/streaming/header_spec.rb'
- './ee/spec/models/board_assignee_spec.rb'
- './ee/spec/models/board_label_spec.rb'
- './ee/spec/models/boards/epic_board_label_spec.rb'
- './ee/spec/models/boards/epic_board_position_spec.rb'
- './ee/spec/models/boards/epic_board_recent_visit_spec.rb'
- './ee/spec/models/boards/epic_board_spec.rb'
- './ee/spec/models/boards/epic_list_spec.rb'
- './ee/spec/models/boards/epic_list_user_preference_spec.rb'
- './ee/spec/models/boards/epic_user_preference_spec.rb'
- './ee/spec/models/board_spec.rb'
- './ee/spec/models/board_user_preference_spec.rb'
- './ee/spec/models/burndown_spec.rb'
- './ee/spec/models/ci/bridge_spec.rb'
- './ee/spec/models/ci/daily_build_group_report_result_spec.rb'
- './ee/spec/models/ci/minutes/additional_pack_spec.rb'
- './ee/spec/models/ci/minutes/context_spec.rb'
- './ee/spec/models/ci/minutes/namespace_monthly_usage_spec.rb'
- './ee/spec/models/ci/minutes/notification_spec.rb'
- './ee/spec/models/ci/minutes/project_monthly_usage_spec.rb'
- './ee/spec/models/ci/minutes/usage_spec.rb'
- './ee/spec/models/ci/pipeline_spec.rb'
- './ee/spec/models/ci/sources/project_spec.rb'
- './ee/spec/models/ci/subscriptions/project_spec.rb'
- './ee/spec/models/commit_spec.rb'
- './ee/spec/models/compliance_management/compliance_framework/project_settings_spec.rb'
- './ee/spec/models/compliance_management/framework_spec.rb'
- './ee/spec/models/concerns/approval_rule_like_spec.rb'
- './ee/spec/models/concerns/approver_migrate_hook_spec.rb'
- './ee/spec/models/concerns/ee/issuable_spec.rb'
- './ee/spec/models/concerns/ee/mentionable_spec.rb'
- './ee/spec/models/concerns/ee/milestoneable_spec.rb'
- './ee/spec/models/concerns/ee/noteable_spec.rb'
- './ee/spec/models/concerns/ee/participable_spec.rb'
- './ee/spec/models/concerns/ee/project_security_scanners_information_spec.rb'
- './ee/spec/models/concerns/geo/verification_state_spec.rb'
- './ee/spec/models/dast/branch_spec.rb'
- './ee/spec/models/dast/profile_schedule_spec.rb'
- './ee/spec/models/dast_site_token_spec.rb'
- './ee/spec/models/dast_site_validation_spec.rb'
- './ee/spec/models/deployments/approval_spec.rb'
- './ee/spec/models/deployment_spec.rb'
- './ee/spec/models/dora/base_metric_spec.rb'
- './ee/spec/models/dora/change_failure_rate_metric_spec.rb'
- './ee/spec/models/dora/daily_metrics_spec.rb'
- './ee/spec/models/dora/deployment_frequency_metric_spec.rb'
- './ee/spec/models/dora/lead_time_for_changes_metric_spec.rb'
- './ee/spec/models/dora/time_to_restore_service_metric_spec.rb'
- './ee/spec/models/ee/alert_management/alert_spec.rb'
- './ee/spec/models/ee/analytics/usage_trends/measurement_spec.rb'
- './ee/spec/models/ee/appearance_spec.rb'
- './ee/spec/models/ee/audit_event_spec.rb'
- './ee/spec/models/ee/award_emoji_spec.rb'
- './ee/spec/models/ee/ci/build_dependencies_spec.rb'
- './ee/spec/models/ee/ci/job_artifact_spec.rb'
- './ee/spec/models/ee/ci/pending_build_spec.rb'
- './ee/spec/models/ee/ci/pipeline_artifact_spec.rb'
- './ee/spec/models/ee/ci/runner_spec.rb'
- './ee/spec/models/ee/ci/secure_file_spec.rb'
- './ee/spec/models/ee/clusters/agent_spec.rb'
- './ee/spec/models/ee/description_version_spec.rb'
- './ee/spec/models/ee/event_spec.rb'
- './ee/spec/models/ee/gpg_key_spec.rb'
- './ee/spec/models/ee/group_group_link_spec.rb'
- './ee/spec/models/ee/groups/feature_setting_spec.rb'
- './ee/spec/models/ee/group_spec.rb'
- './ee/spec/models/ee/integrations/jira_spec.rb'
- './ee/spec/models/ee/integration_spec.rb'
- './ee/spec/models/ee/iterations/cadence_spec.rb'
- './ee/spec/models/ee/key_spec.rb'
- './ee/spec/models/ee/label_spec.rb'
- './ee/spec/models/ee/lfs_object_spec.rb'
- './ee/spec/models/ee/list_spec.rb'
- './ee/spec/models/ee/members_preloader_spec.rb'
- './ee/spec/models/ee/merge_request_diff_spec.rb'
- './ee/spec/models/ee/merge_request/metrics_spec.rb'
- './ee/spec/models/ee/namespace_ci_cd_setting_spec.rb'
- './ee/spec/models/ee/namespace/root_storage_statistics_spec.rb'
- './ee/spec/models/ee/namespaces/namespace_ban_spec.rb'
- './ee/spec/models/ee/namespace_spec.rb'
- './ee/spec/models/ee/namespace_statistics_spec.rb'
- './ee/spec/models/ee/notification_setting_spec.rb'
- './ee/spec/models/ee/pages_deployment_spec.rb'
- './ee/spec/models/ee/personal_access_token_spec.rb'
- './ee/spec/models/ee/preloaders/group_policy_preloader_spec.rb'
- './ee/spec/models/ee/project_spec.rb'
- './ee/spec/models/ee/project_authorization_spec.rb'
- './ee/spec/models/ee/project_group_link_spec.rb'
- './ee/spec/models/ee/project_setting_spec.rb'
- './ee/spec/models/ee/protected_branch_spec.rb'
- './ee/spec/models/ee/protected_ref_spec.rb'
- './ee/spec/models/ee/release_spec.rb'
- './ee/spec/models/ee/resource_label_event_spec.rb'
- './ee/spec/models/ee/resource_state_event_spec.rb'
- './ee/spec/models/ee/service_desk_setting_spec.rb'
- './ee/spec/models/ee/system_note_metadata_spec.rb'
- './ee/spec/models/ee/terraform/state_version_spec.rb'
- './ee/spec/models/ee/user_highest_role_spec.rb'
- './ee/spec/models/ee/users/merge_request_interaction_spec.rb'
- './ee/spec/models/ee/user_spec.rb'
- './ee/spec/models/ee/users_statistics_spec.rb'
- './ee/spec/models/ee/vulnerability_spec.rb'
- './ee/spec/models/elastic/index_setting_spec.rb'
- './ee/spec/models/elastic/migration_record_spec.rb'
- './ee/spec/models/elastic/reindexing_slice_spec.rb'
- './ee/spec/models/elastic/reindexing_subtask_spec.rb'
- './ee/spec/models/elastic/reindexing_task_spec.rb'
- './ee/spec/models/elasticsearch_indexed_namespace_spec.rb'
- './ee/spec/models/elasticsearch_indexed_project_spec.rb'
- './ee/spec/models/environment_spec.rb'
- './ee/spec/models/epic_issue_spec.rb'
- './ee/spec/models/epic/related_epic_link_spec.rb'
- './ee/spec/models/epic_spec.rb'
- './ee/spec/models/epic_user_mention_spec.rb'
- './ee/spec/models/geo/cache_invalidation_event_spec.rb'
- './ee/spec/models/geo/ci_secure_file_registry_spec.rb'
- './ee/spec/models/geo/container_repository_registry_spec.rb'
- './ee/spec/models/geo/deleted_project_spec.rb'
- './ee/spec/models/geo/event_log_spec.rb'
- './ee/spec/models/geo/event_log_state_spec.rb'
- './ee/spec/models/geo/every_geo_event_spec.rb'
- './ee/spec/models/geo/group_wiki_repository_registry_spec.rb'
- './ee/spec/models/geo/job_artifact_registry_spec.rb'
- './ee/spec/models/geo/lfs_object_registry_spec.rb'
- './ee/spec/models/geo/merge_request_diff_registry_spec.rb'
- './ee/spec/models/geo_node_namespace_link_spec.rb'
- './ee/spec/models/geo_node_spec.rb'
- './ee/spec/models/geo_node_status_spec.rb'
- './ee/spec/models/geo/package_file_registry_spec.rb'
- './ee/spec/models/geo/pages_deployment_registry_spec.rb'
- './ee/spec/models/geo/pipeline_artifact_registry_spec.rb'
- './ee/spec/models/geo/push_user_spec.rb'
- './ee/spec/models/geo/secondary_usage_data_spec.rb'
- './ee/spec/models/geo/snippet_repository_registry_spec.rb'
- './ee/spec/models/geo/terraform_state_version_registry_spec.rb'
- './ee/spec/models/geo/tracking_base_spec.rb'
- './ee/spec/models/geo/upload_registry_spec.rb'
- './ee/spec/models/geo/upload_state_spec.rb'
- './ee/spec/models/gitlab/seat_link_data_spec.rb'
- './ee/spec/models/gitlab_subscriptions/features_spec.rb'
- './ee/spec/models/gitlab_subscription_spec.rb'
- './ee/spec/models/gitlab_subscriptions/upcoming_reconciliation_spec.rb'
- './ee/spec/models/group_deletion_schedule_spec.rb'
- './ee/spec/models/ee/group_member_spec.rb'
- './ee/spec/models/group_merge_request_approval_setting_spec.rb'
- './ee/spec/models/groups/repository_storage_move_spec.rb'
- './ee/spec/models/group_wiki_repository_spec.rb'
- './ee/spec/models/group_wiki_spec.rb'
- './ee/spec/models/historical_data_spec.rb'
- './ee/spec/models/hooks/group_hook_spec.rb'
- './ee/spec/models/identity_spec.rb'
- './ee/spec/models/instance_security_dashboard_spec.rb'
- './ee/spec/models/integrations/chat_message/vulnerability_message_spec.rb'
- './ee/spec/models/integrations/github/remote_project_spec.rb'
- './ee/spec/models/integrations/github_spec.rb'
- './ee/spec/models/integrations/github/status_message_spec.rb'
- './ee/spec/models/integrations/github/status_notifier_spec.rb'
- './ee/spec/models/ip_restriction_spec.rb'
- './ee/spec/models/issuable_metric_image_spec.rb'
- './ee/spec/models/issuables_analytics_spec.rb'
- './ee/spec/models/issuable_sla_spec.rb'
- './ee/spec/models/issue_link_spec.rb'
- './ee/spec/models/issue_spec.rb'
- './ee/spec/models/iteration_note_spec.rb'
- './ee/spec/models/label_note_spec.rb'
- './ee/spec/models/ldap_group_link_spec.rb'
- './ee/spec/models/license_spec.rb'
- './ee/spec/models/ee/member_spec.rb'
- './ee/spec/models/merge_request/blocking_spec.rb'
- './ee/spec/models/merge_request_block_spec.rb'
- './ee/spec/models/merge_requests/compliance_violation_spec.rb'
- './ee/spec/models/merge_requests/external_status_check_spec.rb'
- './ee/spec/models/merge_request_spec.rb'
- './ee/spec/models/merge_requests/status_check_response_spec.rb'
- './ee/spec/models/milestone_release_spec.rb'
- './ee/spec/models/milestone_spec.rb'
- './ee/spec/models/namespace_limit_spec.rb'
- './ee/spec/models/namespace_setting_spec.rb'
- './ee/spec/models/namespaces/free_user_cap_spec.rb'
- './ee/spec/models/namespaces/storage/root_size_spec.rb'
- './ee/spec/models/path_lock_spec.rb'
- './ee/spec/models/plan_spec.rb'
- './ee/spec/models/preloaders/environments/protected_environment_preloader_spec.rb'
- './ee/spec/models/productivity_analytics_spec.rb'
- './ee/spec/models/project_alias_spec.rb'
- './ee/spec/models/project_ci_cd_setting_spec.rb'
- './ee/spec/models/project_import_data_spec.rb'
- './ee/spec/models/ee/project_member_spec.rb'
- './ee/spec/models/protected_branch/required_code_owners_section_spec.rb'
- './ee/spec/models/protected_branch/unprotect_access_level_spec.rb'
- './ee/spec/models/protected_environments/approval_rule_spec.rb'
- './ee/spec/models/protected_environment_spec.rb'
- './ee/spec/models/push_rule_spec.rb'
- './ee/spec/models/release_highlight_spec.rb'
- './ee/spec/models/remote_mirror_spec.rb'
- './ee/spec/models/repository_spec.rb'
- './ee/spec/models/requirements_management/requirement_spec.rb'
- './ee/spec/models/requirements_management/test_report_spec.rb'
- './ee/spec/models/resource_iteration_event_spec.rb'
- './ee/spec/models/resource_weight_event_spec.rb'
- './ee/spec/models/saml_group_link_spec.rb'
- './ee/spec/models/saml_provider_spec.rb'
- './ee/spec/models/sbom/component_spec.rb'
- './ee/spec/models/sbom/component_version_spec.rb'
- './ee/spec/models/sbom/occurrence_spec.rb'
- './ee/spec/models/sbom/source_spec.rb'
- './ee/spec/models/sca/license_compliance_spec.rb'
- './ee/spec/models/sca/license_policy_spec.rb'
- './ee/spec/models/scim_identity_spec.rb'
- './ee/spec/models/scim_oauth_access_token_spec.rb'
- './ee/spec/models/scoped_label_set_spec.rb'
- './ee/spec/models/security/finding_spec.rb'
- './ee/spec/models/security/orchestration_policy_configuration_spec.rb'
- './ee/spec/models/security/orchestration_policy_rule_schedule_spec.rb'
- './ee/spec/models/security/scan_spec.rb'
- './ee/spec/models/security/training_provider_spec.rb'
- './ee/spec/models/security/training_spec.rb'
- './ee/spec/models/snippet_repository_spec.rb'
- './ee/spec/models/snippet_spec.rb'
- './ee/spec/models/software_license_policy_spec.rb'
- './ee/spec/models/software_license_spec.rb'
- './ee/spec/models/storage_shard_spec.rb'
- './ee/spec/models/uploads/local_spec.rb'
- './ee/spec/models/upload_spec.rb'
- './ee/spec/models/user_permission_export_upload_spec.rb'
- './ee/spec/models/user_preference_spec.rb'
- './ee/spec/models/users_security_dashboard_project_spec.rb'
- './ee/spec/models/visible_approvable_spec.rb'
- './ee/spec/models/vulnerabilities/export_spec.rb'
- './ee/spec/models/vulnerabilities/external_issue_link_spec.rb'
- './ee/spec/models/vulnerabilities/feedback_spec.rb'
- './ee/spec/models/vulnerabilities/finding/evidence_spec.rb'
- './ee/spec/models/vulnerabilities/finding_identifier_spec.rb'
- './ee/spec/models/vulnerabilities/finding_link_spec.rb'
- './ee/spec/models/vulnerabilities/finding_remediation_spec.rb'
- './ee/spec/models/vulnerabilities/finding_signature_spec.rb'
- './ee/spec/models/vulnerabilities/finding_spec.rb'
- './ee/spec/models/vulnerabilities/flag_spec.rb'
- './ee/spec/models/vulnerabilities/historical_statistic_spec.rb'
- './ee/spec/models/vulnerabilities/identifier_spec.rb'
- './ee/spec/models/vulnerabilities/issue_link_spec.rb'
- './ee/spec/models/vulnerabilities/merge_request_link_spec.rb'
- './ee/spec/models/vulnerabilities/projects_grade_spec.rb'
- './ee/spec/models/vulnerabilities/read_spec.rb'
- './ee/spec/models/vulnerabilities/remediation_spec.rb'
- './ee/spec/models/vulnerabilities/scanner_spec.rb'
- './ee/spec/models/vulnerabilities/stat_diff_spec.rb'
- './ee/spec/models/vulnerabilities/state_transition_spec.rb'
- './ee/spec/models/vulnerabilities/statistic_spec.rb'
- './ee/spec/models/vulnerability_user_mention_spec.rb'
- './ee/spec/models/weight_note_spec.rb'
- './ee/spec/models/work_item_spec.rb'
- './ee/spec/policies/approval_merge_request_rule_policy_spec.rb'
- './ee/spec/policies/approval_project_rule_policy_spec.rb'
- './ee/spec/policies/approval_state_policy_spec.rb'
- './ee/spec/policies/app_sec/fuzzing/coverage/corpus_policy_spec.rb'
- './ee/spec/policies/award_emoji_policy_spec.rb'
- './ee/spec/policies/base_policy_spec.rb'
- './ee/spec/policies/ci/build_policy_spec.rb'
- './ee/spec/policies/ci/minutes/namespace_monthly_usage_policy_spec.rb'
- './ee/spec/policies/clusters/instance_policy_spec.rb'
- './ee/spec/policies/compliance_management/framework_policy_spec.rb'
- './ee/spec/policies/dast/branch_policy_spec.rb'
- './ee/spec/policies/dast/profile_policy_spec.rb'
- './ee/spec/policies/dast/profile_schedule_policy_spec.rb'
- './ee/spec/policies/dast_scanner_profile_policy_spec.rb'
- './ee/spec/policies/dast_site_profile_policy_spec.rb'
- './ee/spec/policies/dast_site_validation_policy_spec.rb'
- './ee/spec/policies/ee/ci/runner_policy_spec.rb'
- './ee/spec/policies/ee/namespaces/user_namespace_policy_spec.rb'
- './ee/spec/policies/ee/readonly_abilities_spec.rb'
- './ee/spec/policies/environment_policy_spec.rb'
- './ee/spec/policies/epic_policy_spec.rb'
- './ee/spec/policies/event_policy_spec.rb'
- './ee/spec/policies/geo_node_policy_spec.rb'
- './ee/spec/policies/geo/registry_policy_spec.rb'
- './ee/spec/policies/global_policy_spec.rb'
- './ee/spec/policies/group_hook_policy_spec.rb'
- './ee/spec/policies/group_policy_spec.rb'
- './ee/spec/policies/instance_security_dashboard_policy_spec.rb'
- './ee/spec/policies/issuable_policy_spec.rb'
- './ee/spec/policies/issue_policy_spec.rb'
- './ee/spec/policies/merge_request_policy_spec.rb'
- './ee/spec/policies/note_policy_spec.rb'
- './ee/spec/policies/path_lock_policy_spec.rb'
- './ee/spec/policies/project_policy_spec.rb'
- './ee/spec/policies/project_snippet_policy_spec.rb'
- './ee/spec/policies/protected_branch_policy_spec.rb'
- './ee/spec/policies/requirements_management/requirement_policy_spec.rb'
- './ee/spec/policies/saml_provider_policy_spec.rb'
- './ee/spec/policies/security/scan_policy_spec.rb'
- './ee/spec/policies/user_policy_spec.rb'
- './ee/spec/policies/vulnerabilities/export_policy_spec.rb'
- './ee/spec/policies/vulnerabilities/external_issue_link_policy_spec.rb'
- './ee/spec/policies/vulnerabilities/feedback_policy_spec.rb'
- './ee/spec/policies/vulnerabilities/issue_link_policy_spec.rb'
- './ee/spec/policies/vulnerabilities/scanner_policy_spec.rb'
- './ee/spec/policies/vulnerability_policy_spec.rb'
- './ee/spec/presenters/analytics/cycle_analytics/stage_presenter_spec.rb'
- './ee/spec/presenters/approval_rule_presenter_spec.rb'
- './ee/spec/presenters/audit_event_presenter_spec.rb'
- './ee/spec/presenters/ci/build_presenter_spec.rb'
- './ee/spec/presenters/ci/build_runner_presenter_spec.rb'
- './ee/spec/presenters/ci/minutes/usage_presenter_spec.rb'
- './ee/spec/presenters/ci/pipeline_presenter_spec.rb'
- './ee/spec/presenters/dast/site_profile_presenter_spec.rb'
- './ee/spec/presenters/ee/blob_presenter_spec.rb'
- './ee/spec/presenters/ee/instance_clusterable_presenter_spec.rb'
- './ee/spec/presenters/ee/issue_presenter_spec.rb'
- './ee/spec/presenters/ee/projects/security/configuration_presenter_spec.rb'
- './ee/spec/presenters/epic_issue_presenter_spec.rb'
- './ee/spec/presenters/epic_presenter_spec.rb'
- './ee/spec/presenters/group_clusterable_presenter_spec.rb'
- './ee/spec/presenters/group_member_presenter_spec.rb'
- './ee/spec/presenters/label_presenter_spec.rb'
- './ee/spec/presenters/merge_request_approver_presenter_spec.rb'
- './ee/spec/presenters/merge_request_presenter_spec.rb'
- './ee/spec/presenters/project_member_presenter_spec.rb'
- './ee/spec/presenters/security/scan_presenter_spec.rb'
- './ee/spec/presenters/subscription_presenter_spec.rb'
- './ee/spec/presenters/vulnerabilities/finding_presenter_spec.rb'
- './ee/spec/presenters/vulnerability_presenter_spec.rb'
- './ee/spec/presenters/web_hooks/group/hook_presenter_spec.rb'
- './ee/spec/replicators/geo/ci_secure_file_replicator_spec.rb'
- './ee/spec/replicators/geo/group_wiki_repository_replicator_spec.rb'
- './ee/spec/replicators/geo/job_artifact_replicator_spec.rb'
- './ee/spec/replicators/geo/lfs_object_replicator_spec.rb'
- './ee/spec/replicators/geo/merge_request_diff_replicator_spec.rb'
- './ee/spec/replicators/geo/package_file_replicator_spec.rb'
- './ee/spec/replicators/geo/pages_deployment_replicator_spec.rb'
- './ee/spec/replicators/geo/pipeline_artifact_replicator_spec.rb'
- './ee/spec/replicators/geo/snippet_repository_replicator_spec.rb'
- './ee/spec/replicators/geo/terraform_state_version_replicator_spec.rb'
- './ee/spec/replicators/geo/upload_replicator_spec.rb'
- './ee/spec/requests/admin/audit_events_spec.rb'
- './ee/spec/requests/admin/credentials_controller_spec.rb'
- './ee/spec/requests/admin/geo/nodes_controller_spec.rb'
- './ee/spec/requests/admin/geo/replicables_controller_spec.rb'
- './ee/spec/requests/admin/subscriptions_controller_spec.rb'
- './ee/spec/requests/admin/user_permission_exports_controller_spec.rb'
- './ee/spec/requests/admin/users_controller_spec.rb'
- './ee/spec/requests/api/analytics/code_review_analytics_spec.rb'
- './ee/spec/requests/api/analytics/group_activity_analytics_spec.rb'
- './ee/spec/requests/api/analytics/project_deployment_frequency_spec.rb'
- './ee/spec/requests/api/api_spec.rb'
- './ee/spec/requests/api/audit_events_spec.rb'
- './ee/spec/requests/api/award_emoji_spec.rb'
- './ee/spec/requests/api/boards_spec.rb'
- './ee/spec/requests/api/branches_spec.rb'
- './ee/spec/requests/api/ci/jobs_spec.rb'
- './ee/spec/requests/api/ci/minutes_spec.rb'
- './ee/spec/requests/api/ci/pipelines_spec.rb'
- './ee/spec/requests/api/ci/runner/jobs_put_spec.rb'
- './ee/spec/requests/api/ci/runner/jobs_trace_spec.rb'
- './ee/spec/requests/api/ci/runner_spec.rb'
- './ee/spec/requests/api/ci/triggers_spec.rb'
- './ee/spec/requests/api/ci/variables_spec.rb'
- './ee/spec/requests/api/commits_spec.rb'
- './ee/spec/requests/api/dependencies_spec.rb'
- './ee/spec/requests/api/deployments_spec.rb'
- './ee/spec/requests/api/discussions_spec.rb'
- './ee/spec/requests/api/dora/metrics_spec.rb'
- './ee/spec/requests/api/elasticsearch_indexed_namespaces_spec.rb'
- './ee/spec/requests/api/epic_issues_spec.rb'
- './ee/spec/requests/api/epic_links_spec.rb'
- './ee/spec/requests/api/epics_spec.rb'
- './ee/spec/requests/api/experiments_spec.rb'
- './ee/spec/requests/api/features_spec.rb'
- './ee/spec/requests/api/files_spec.rb'
- './ee/spec/requests/api/geo_nodes_spec.rb'
- './ee/spec/requests/api/geo_spec.rb'
- './ee/spec/requests/api/graphql/analytics/devops_adoption/enabled_namespaces_spec.rb'
- './ee/spec/requests/api/graphql/app_sec/fuzzing/api/ci_configuration_type_spec.rb'
- './ee/spec/requests/api/graphql/app_sec/fuzzing/coverage/corpus_type_spec.rb'
- './ee/spec/requests/api/graphql/audit_events/streaming/headers/create_spec.rb'
- './ee/spec/requests/api/graphql/audit_events/streaming/headers/destroy_spec.rb'
- './ee/spec/requests/api/graphql/audit_events/streaming/headers/update_spec.rb'
- './ee/spec/requests/api/graphql/boards/board_list_query_spec.rb'
- './ee/spec/requests/api/graphql/boards/board_lists_query_spec.rb'
- './ee/spec/requests/api/graphql/boards/boards_query_spec.rb'
- './ee/spec/requests/api/graphql/boards/epic_board_list_epics_query_spec.rb'
- './ee/spec/requests/api/graphql/boards/epic_boards_query_spec.rb'
- './ee/spec/requests/api/graphql/boards/epic_list_query_spec.rb'
- './ee/spec/requests/api/graphql/boards/epic_lists_query_spec.rb'
- './ee/spec/requests/api/graphql/boards/epic_lists/update_spec.rb'
- './ee/spec/requests/api/graphql/ci/minutes/usage_spec.rb'
- './ee/spec/requests/api/graphql/ci/runner_spec.rb'
- './ee/spec/requests/api/graphql/ci/runners_spec.rb'
- './ee/spec/requests/api/graphql/compliance_management/merge_requests/compliance_violations_spec.rb'
- './ee/spec/requests/api/graphql/current_user/groups_query_spec.rb'
- './ee/spec/requests/api/graphql/current_user/todos_query_spec.rb'
- './ee/spec/requests/api/graphql/dora/dora_spec.rb'
- './ee/spec/requests/api/graphql/geo/geo_node_spec.rb'
- './ee/spec/requests/api/graphql/geo/registries_spec.rb'
- './ee/spec/requests/api/graphql/group/ci_cd_settings_spec.rb'
- './ee/spec/requests/api/graphql/group/dast_profile_schedule_spec.rb'
- './ee/spec/requests/api/graphql/group/epic/epic_aggregate_query_spec.rb'
- './ee/spec/requests/api/graphql/group/epic/epic_ancestors_spec.rb'
- './ee/spec/requests/api/graphql/group/epic/epic_issues_spec.rb'
- './ee/spec/requests/api/graphql/group/epic/notes_spec.rb'
- './ee/spec/requests/api/graphql/group/epics_spec.rb'
- './ee/spec/requests/api/graphql/group/external_audit_event_destinations_spec.rb'
- './ee/spec/requests/api/graphql/group_query_spec.rb'
- './ee/spec/requests/api/graphql/instance_security_dashboard_spec.rb'
- './ee/spec/requests/api/graphql/iterations/cadences_spec.rb'
- './ee/spec/requests/api/graphql/iterations/iterations_spec.rb'
- './ee/spec/requests/api/graphql/iteration_spec.rb'
- './ee/spec/requests/api/graphql/merge_request_reviewer_spec.rb'
- './ee/spec/requests/api/graphql/merge_requests/approval_state_spec.rb'
- './ee/spec/requests/api/graphql/milestone_spec.rb'
- './ee/spec/requests/api/graphql/mutations/alert_management/http_integration/create_spec.rb'
- './ee/spec/requests/api/graphql/mutations/alert_management/http_integration/update_spec.rb'
- './ee/spec/requests/api/graphql/mutations/analytics/devops_adoption/enabled_namespaces/bulk_enable_spec.rb'
- './ee/spec/requests/api/graphql/mutations/analytics/devops_adoption/enabled_namespaces/disable_spec.rb'
- './ee/spec/requests/api/graphql/mutations/analytics/devops_adoption/enabled_namespaces/enable_spec.rb'
- './ee/spec/requests/api/graphql/mutations/audit_events/external_audit_event_destinations/create_spec.rb'
- './ee/spec/requests/api/graphql/mutations/audit_events/external_audit_event_destinations/destroy_spec.rb'
- './ee/spec/requests/api/graphql/mutations/audit_events/external_audit_event_destinations/update_spec.rb'
- './ee/spec/requests/api/graphql/mutations/boards/create_spec.rb'
- './ee/spec/requests/api/graphql/mutations/boards/epic_boards/create_spec.rb'
- './ee/spec/requests/api/graphql/mutations/boards/epic_boards/destroy_spec.rb'
- './ee/spec/requests/api/graphql/mutations/boards/epic_boards/epic_move_list_spec.rb'
- './ee/spec/requests/api/graphql/mutations/boards/epic_boards/update_spec.rb'
- './ee/spec/requests/api/graphql/mutations/boards/epic_lists/create_spec.rb'
- './ee/spec/requests/api/graphql/mutations/boards/epic_lists/destroy_spec.rb'
- './ee/spec/requests/api/graphql/mutations/boards/epics/create_spec.rb'
- './ee/spec/requests/api/graphql/mutations/boards/issues/issue_move_list_spec.rb'
- './ee/spec/requests/api/graphql/mutations/boards/lists/create_spec.rb'
- './ee/spec/requests/api/graphql/mutations/boards/lists/update_limit_metrics_spec.rb'
- './ee/spec/requests/api/graphql/mutations/boards/update_epic_user_preferences_spec.rb'
- './ee/spec/requests/api/graphql/mutations/ci/namespace_ci_cd_settings_update_spec.rb'
- './ee/spec/requests/api/graphql/mutations/compliance_management/frameworks/create_spec.rb'
- './ee/spec/requests/api/graphql/mutations/compliance_management/frameworks/destroy_spec.rb'
- './ee/spec/requests/api/graphql/mutations/compliance_management/frameworks/update_spec.rb'
- './ee/spec/requests/api/graphql/mutations/dast_on_demand_scans/create_spec.rb'
- './ee/spec/requests/api/graphql/mutations/dast/profiles/create_spec.rb'
- './ee/spec/requests/api/graphql/mutations/dast/profiles/delete_spec.rb'
- './ee/spec/requests/api/graphql/mutations/dast/profiles/run_spec.rb'
- './ee/spec/requests/api/graphql/mutations/dast/profiles/update_spec.rb'
- './ee/spec/requests/api/graphql/mutations/dast_scanner_profiles/create_spec.rb'
- './ee/spec/requests/api/graphql/mutations/dast_scanner_profiles/delete_spec.rb'
- './ee/spec/requests/api/graphql/mutations/dast_scanner_profiles/update_spec.rb'
- './ee/spec/requests/api/graphql/mutations/dast_site_profiles/create_spec.rb'
- './ee/spec/requests/api/graphql/mutations/dast_site_profiles/delete_spec.rb'
- './ee/spec/requests/api/graphql/mutations/dast_site_profiles/update_spec.rb'
- './ee/spec/requests/api/graphql/mutations/dast_site_tokens/create_spec.rb'
- './ee/spec/requests/api/graphql/mutations/dast_site_validations/create_spec.rb'
- './ee/spec/requests/api/graphql/mutations/dast_site_validations/revoke_spec.rb'
- './ee/spec/requests/api/graphql/mutations/environments/canary_ingress/update_spec.rb'
- './ee/spec/requests/api/graphql/mutations/epics/add_issue_spec.rb'
- './ee/spec/requests/api/graphql/mutations/epics/create_spec.rb'
- './ee/spec/requests/api/graphql/mutations/epics/set_subscription_spec.rb'
- './ee/spec/requests/api/graphql/mutations/epics/update_spec.rb'
- './ee/spec/requests/api/graphql/mutations/epic_tree/reorder_spec.rb'
- './ee/spec/requests/api/graphql/mutations/gitlab_subscriptions/activate_spec.rb'
- './ee/spec/requests/api/graphql/mutations/issues/create_spec.rb'
- './ee/spec/requests/api/graphql/mutations/issues/promote_to_epic_spec.rb'
- './ee/spec/requests/api/graphql/mutations/issues/set_epic_spec.rb'
- './ee/spec/requests/api/graphql/mutations/issues/set_escalation_policy_spec.rb'
- './ee/spec/requests/api/graphql/mutations/issues/set_weight_spec.rb'
- './ee/spec/requests/api/graphql/mutations/issues/update_spec.rb'
- './ee/spec/requests/api/graphql/mutations/iterations/cadences/create_spec.rb'
- './ee/spec/requests/api/graphql/mutations/iterations/cadences/destroy_spec.rb'
- './ee/spec/requests/api/graphql/mutations/iterations/cadences/update_spec.rb'
- './ee/spec/requests/api/graphql/mutations/iterations/create_spec.rb'
- './ee/spec/requests/api/graphql/mutations/iterations/delete_spec.rb'
- './ee/spec/requests/api/graphql/mutations/iterations/update_spec.rb'
- './ee/spec/requests/api/graphql/mutations/merge_requests/set_assignees_spec.rb'
- './ee/spec/requests/api/graphql/mutations/notes/create/note_spec.rb'
- './ee/spec/requests/api/graphql/mutations/projects/lock_path_spec.rb'
- './ee/spec/requests/api/graphql/mutations/projects/set_compliance_framework_spec.rb'
- './ee/spec/requests/api/graphql/mutations/quality_management/test_cases/create_spec.rb'
- './ee/spec/requests/api/graphql/mutations/releases/create_spec.rb'
- './ee/spec/requests/api/graphql/mutations/releases/update_spec.rb'
- './ee/spec/requests/api/graphql/mutations/requirements_management/create_requirement_spec.rb'
- './ee/spec/requests/api/graphql/mutations/requirements_management/export_requirements_spec.rb'
- './ee/spec/requests/api/graphql/mutations/requirements_management/update_requirement_spec.rb'
- './ee/spec/requests/api/graphql/mutations/security_policy/assign_security_policy_project_spec.rb'
- './ee/spec/requests/api/graphql/mutations/security_policy/commit_scan_execution_policy_spec.rb'
- './ee/spec/requests/api/graphql/mutations/security_policy/create_security_policy_project_spec.rb'
- './ee/spec/requests/api/graphql/mutations/security_policy/unassign_security_policy_project_spec.rb'
- './ee/spec/requests/api/graphql/mutations/timelogs/create_spec.rb'
- './ee/spec/requests/api/graphql/mutations/users/abuse/namespace_bans/destroy_spec.rb'
- './ee/spec/requests/api/graphql/mutations/vulnerabilities/create_external_issue_link_spec.rb'
- './ee/spec/requests/api/graphql/mutations/vulnerabilities/destroy_external_issue_link_spec.rb'
- './ee/spec/requests/api/graphql/mutations/work_items/update_spec.rb'
- './ee/spec/requests/api/graphql/namespace/projects_spec.rb'
- './ee/spec/requests/api/graphql/project/alert_management/http_integrations_spec.rb'
- './ee/spec/requests/api/graphql/project/alert_management/integrations_spec.rb'
- './ee/spec/requests/api/graphql/project/alert_management/payload_fields_spec.rb'
- './ee/spec/requests/api/graphql/project/code_coverage_summary_spec.rb'
- './ee/spec/requests/api/graphql/project/dast_profile_schedule_spec.rb'
- './ee/spec/requests/api/graphql/project/dast_profile_spec.rb'
- './ee/spec/requests/api/graphql/project/dast_profiles_spec.rb'
- './ee/spec/requests/api/graphql/project/dast_scanner_profiles_spec.rb'
- './ee/spec/requests/api/graphql/project/dast_site_profile_spec.rb'
- './ee/spec/requests/api/graphql/project/dast_site_profiles_spec.rb'
- './ee/spec/requests/api/graphql/project/dast_site_validations_spec.rb'
- './ee/spec/requests/api/graphql/project/issues_spec.rb'
- './ee/spec/requests/api/graphql/project/merge_requests_spec.rb'
- './ee/spec/requests/api/graphql/project/path_locks_spec.rb'
- './ee/spec/requests/api/graphql/project/pipeline/code_quality_reports_spec.rb'
- './ee/spec/requests/api/graphql/project/pipeline/dast_profile_spec.rb'
- './ee/spec/requests/api/graphql/project/pipelines/dast_profile_spec.rb'
- './ee/spec/requests/api/graphql/project/pipeline/security_report_finding_spec.rb'
- './ee/spec/requests/api/graphql/project/pipeline/security_report_summary_spec.rb'
- './ee/spec/requests/api/graphql/project/push_rules_spec.rb'
- './ee/spec/requests/api/graphql/project/requirements_management/requirement_counts_spec.rb'
- './ee/spec/requests/api/graphql/project/requirements_management/requirements_spec.rb'
- './ee/spec/requests/api/graphql/project/requirements_management/test_reports_spec.rb'
- './ee/spec/requests/api/graphql/projects/compliance_frameworks_spec.rb'
- './ee/spec/requests/api/graphql/project/security_orchestration/scan_result_policy_spec.rb'
- './ee/spec/requests/api/graphql/project/vulnerability_severities_count_spec.rb'
- './ee/spec/requests/api/graphql/project/work_items_spec.rb'
- './ee/spec/requests/api/graphql/vulnerabilities/details_spec.rb'
- './ee/spec/requests/api/graphql/vulnerabilities/external_issue_links_spec.rb'
- './ee/spec/requests/api/graphql/vulnerabilities/identifiers_spec.rb'
- './ee/spec/requests/api/graphql/vulnerabilities/issue_links_spec.rb'
- './ee/spec/requests/api/graphql/vulnerabilities/location_spec.rb'
- './ee/spec/requests/api/graphql/vulnerabilities/primary_identifier_spec.rb'
- './ee/spec/requests/api/graphql/vulnerabilities/scanner_spec.rb'
- './ee/spec/requests/api/graphql/vulnerabilities/sort_spec.rb'
- './ee/spec/requests/api/graphql/work_item_spec.rb'
- './ee/spec/requests/api/group_boards_spec.rb'
- './ee/spec/requests/api/group_clusters_spec.rb'
- './ee/spec/requests/api/group_hooks_spec.rb'
- './ee/spec/requests/api/group_milestones_spec.rb'
- './ee/spec/requests/api/group_push_rule_spec.rb'
- './ee/spec/requests/api/group_repository_storage_moves_spec.rb'
- './ee/spec/requests/api/groups_spec.rb'
- './ee/spec/requests/api/group_variables_spec.rb'
- './ee/spec/requests/api/internal/app_sec/dast/site_validations_spec.rb'
- './ee/spec/requests/api/internal/base_spec.rb'
- './ee/spec/requests/api/internal/kubernetes_spec.rb'
- './ee/spec/requests/api/invitations_spec.rb'
- './ee/spec/requests/api/issue_links_spec.rb'
- './ee/spec/requests/api/issues_spec.rb'
- './ee/spec/requests/api/iterations_spec.rb'
- './ee/spec/requests/api/ldap_group_links_spec.rb'
- './ee/spec/requests/api/ldap_spec.rb'
- './ee/spec/requests/api/license_spec.rb'
- './ee/spec/requests/api/managed_licenses_spec.rb'
- './ee/spec/requests/api/members_spec.rb'
- './ee/spec/requests/api/merge_request_approval_rules_spec.rb'
- './ee/spec/requests/api/merge_request_approval_settings_spec.rb'
- './ee/spec/requests/api/merge_request_approvals_spec.rb'
- './ee/spec/requests/api/merge_requests_spec.rb'
- './ee/spec/requests/api/merge_trains_spec.rb'
- './ee/spec/requests/api/namespaces_spec.rb'
- './ee/spec/requests/api/notes_spec.rb'
- './ee/spec/requests/api/project_aliases_spec.rb'
- './ee/spec/requests/api/project_approval_rules_spec.rb'
- './ee/spec/requests/api/project_approval_settings_spec.rb'
- './ee/spec/requests/api/project_approvals_spec.rb'
- './ee/spec/requests/api/project_clusters_spec.rb'
- './ee/spec/requests/api/project_milestones_spec.rb'
- './ee/spec/requests/api/project_mirror_spec.rb'
- './ee/spec/requests/api/project_push_rule_spec.rb'
- './ee/spec/requests/api/project_snapshots_spec.rb'
- './ee/spec/requests/api/projects_spec.rb'
- './ee/spec/requests/api/protected_branches_spec.rb'
- './ee/spec/requests/api/protected_environments_spec.rb'
- './ee/spec/requests/api/protected_tags_spec.rb'
- './ee/spec/requests/api/related_epic_links_spec.rb'
- './ee/spec/requests/api/releases_spec.rb'
- './ee/spec/requests/api/repositories_spec.rb'
- './ee/spec/requests/api/resource_iteration_events_spec.rb'
- './ee/spec/requests/api/resource_label_events_spec.rb'
- './ee/spec/requests/api/resource_weight_events_spec.rb'
- './ee/spec/requests/api/saml_group_links_spec.rb'
- './ee/spec/requests/api/settings_spec.rb'
- './ee/spec/requests/api/status_checks_spec.rb'
- './ee/spec/requests/api/submodules_spec.rb'
- './ee/spec/requests/api/templates_spec.rb'
- './ee/spec/requests/api/todos_spec.rb'
- './ee/spec/requests/api/users_spec.rb'
- './ee/spec/requests/api/vulnerabilities_spec.rb'
- './ee/spec/requests/api/vulnerability_exports_spec.rb'
- './ee/spec/requests/api/vulnerability_findings_spec.rb'
- './ee/spec/requests/api/vulnerability_issue_links_spec.rb'
- './ee/spec/requests/api/wikis_spec.rb'
- './ee/spec/requests/callout_spec.rb'
- './ee/spec/requests/customers_dot/proxy_controller_spec.rb'
- './ee/spec/requests/ee/groups/autocomplete_sources_spec.rb'
- './ee/spec/requests/ee/groups/settings/repository_controller_spec.rb'
- './ee/spec/requests/ee/projects/deploy_tokens_controller_spec.rb'
- './ee/spec/requests/ee/projects/environments_controller_spec.rb'
- './ee/spec/requests/ee/projects/service_desk_controller_spec.rb'
- './ee/spec/requests/git_http_geo_spec.rb'
- './ee/spec/requests/git_http_spec.rb'
- './ee/spec/requests/groups/analytics/devops_adoption_controller_spec.rb'
- './ee/spec/requests/groups/audit_events_spec.rb'
- './ee/spec/requests/groups/clusters_controller_spec.rb'
- './ee/spec/requests/groups/contribution_analytics_spec.rb'
- './ee/spec/requests/groups_controller_spec.rb'
- './ee/spec/requests/groups/group_members_controller_spec.rb'
- './ee/spec/requests/groups/hook_logs_controller_spec.rb'
- './ee/spec/requests/groups/labels_spec.rb'
- './ee/spec/requests/groups/protected_environments_controller_spec.rb'
- './ee/spec/requests/groups/roadmap_controller_spec.rb'
- './ee/spec/requests/groups/security/credentials_controller_spec.rb'
- './ee/spec/requests/groups/settings/reporting_controller_spec.rb'
- './ee/spec/requests/jwt_controller_spec.rb'
- './ee/spec/requests/lfs_http_spec.rb'
- './ee/spec/requests/lfs_locks_api_spec.rb'
- './ee/spec/requests/omniauth_kerberos_spec.rb'
- './ee/spec/requests/projects/analytics/code_reviews_controller_spec.rb'
- './ee/spec/requests/projects/audit_events_spec.rb'
- './ee/spec/requests/projects/issue_feature_flags_controller_spec.rb'
- './ee/spec/requests/projects/issues_controller_spec.rb'
- './ee/spec/requests/projects/merge_requests_controller_spec.rb'
- './ee/spec/requests/projects/mirrors_controller_spec.rb'
- './ee/spec/requests/projects/on_demand_scans_controller_spec.rb'
- './ee/spec/requests/projects/pipelines_controller_spec.rb'
- './ee/spec/requests/projects/requirements_management/requirements_controller_spec.rb'
- './ee/spec/requests/projects/security/corpus_management_controller_spec.rb'
- './ee/spec/requests/projects/security/dast_configuration_controller_spec.rb'
- './ee/spec/requests/projects/security/dast_profiles_controller_spec.rb'
- './ee/spec/requests/projects/security/dast_scanner_profiles_controller_spec.rb'
- './ee/spec/requests/projects/security/dast_site_profiles_controller_spec.rb'
- './ee/spec/requests/projects/security/policies_controller_spec.rb'
- './ee/spec/requests/projects/security/scanned_resources_controller_spec.rb'
- './ee/spec/requests/projects/settings/access_tokens_controller_spec.rb'
- './ee/spec/requests/rack_attack_global_spec.rb'
- './ee/spec/requests/rack_attack_spec.rb'
- './ee/spec/requests/repositories/git_http_controller_spec.rb'
- './ee/spec/requests/smartcard_controller_spec.rb'
- './ee/spec/requests/trial_registrations_controller_spec.rb'
- './ee/spec/requests/user_activity_spec.rb'
- './ee/spec/services/admin/email_service_spec.rb'
- './ee/spec/services/alert_management/extract_alert_payload_fields_service_spec.rb'
- './ee/spec/services/alert_management/process_prometheus_alert_service_spec.rb'
- './ee/spec/services/analytics/cycle_analytics/aggregator_service_spec.rb'
- './ee/spec/services/analytics/cycle_analytics/consistency_check_service_spec.rb'
- './ee/spec/services/analytics/cycle_analytics/data_loader_service_spec.rb'
- './ee/spec/services/analytics/cycle_analytics/stages/list_service_spec.rb'
- './ee/spec/services/analytics/cycle_analytics/value_streams/create_service_spec.rb'
- './ee/spec/services/analytics/cycle_analytics/value_streams/update_service_spec.rb'
- './ee/spec/services/analytics/devops_adoption/enabled_namespaces/bulk_delete_service_spec.rb'
- './ee/spec/services/analytics/devops_adoption/enabled_namespaces/bulk_find_or_create_service_spec.rb'
- './ee/spec/services/analytics/devops_adoption/enabled_namespaces/create_service_spec.rb'
- './ee/spec/services/analytics/devops_adoption/enabled_namespaces/delete_service_spec.rb'
- './ee/spec/services/analytics/devops_adoption/enabled_namespaces/find_or_create_service_spec.rb'
- './ee/spec/services/analytics/devops_adoption/snapshots/calculate_and_save_service_spec.rb'
- './ee/spec/services/analytics/devops_adoption/snapshots/create_service_spec.rb'
- './ee/spec/services/analytics/devops_adoption/snapshots/update_service_spec.rb'
- './ee/spec/services/applications/create_service_spec.rb'
- './ee/spec/services/application_settings/update_service_spec.rb'
- './ee/spec/services/approval_rules/create_service_spec.rb'
- './ee/spec/services/approval_rules/finalize_service_spec.rb'
- './ee/spec/services/approval_rules/merge_request_rule_destroy_service_spec.rb'
- './ee/spec/services/approval_rules/params_filtering_service_spec.rb'
- './ee/spec/services/approval_rules/project_rule_destroy_service_spec.rb'
- './ee/spec/services/approval_rules/update_service_spec.rb'
- './ee/spec/services/app_sec/dast/builds/associate_service_spec.rb'
- './ee/spec/services/app_sec/dast/pipelines/find_latest_service_spec.rb'
- './ee/spec/services/app_sec/dast/profiles/audit/update_service_spec.rb'
- './ee/spec/services/app_sec/dast/profiles/build_config_service_spec.rb'
- './ee/spec/services/app_sec/dast/profile_schedules/audit/update_service_spec.rb'
- './ee/spec/services/app_sec/dast/profiles/create_associations_service_spec.rb'
- './ee/spec/services/app_sec/dast/profiles/create_service_spec.rb'
- './ee/spec/services/app_sec/dast/profiles/destroy_service_spec.rb'
- './ee/spec/services/app_sec/dast/profiles/update_service_spec.rb'
- './ee/spec/services/app_sec/dast/scan_configs/build_service_spec.rb'
- './ee/spec/services/app_sec/dast/site_validations/find_or_create_service_spec.rb'
- './ee/spec/services/app_sec/dast/site_validations/revoke_service_spec.rb'
- './ee/spec/services/app_sec/dast/site_validations/runner_service_spec.rb'
- './ee/spec/services/app_sec/fuzzing/api/ci_configuration_create_service_spec.rb'
- './ee/spec/services/app_sec/fuzzing/coverage/corpuses/create_service_spec.rb'
- './ee/spec/services/arkose/blocked_users_report_service_spec.rb'
- './ee/spec/services/audit_events/build_service_spec.rb'
- './ee/spec/services/audit_event_service_spec.rb'
- './ee/spec/services/audit_events/export_csv_service_spec.rb'
- './ee/spec/services/audit_events/protected_branch_audit_event_service_spec.rb'
- './ee/spec/services/audit_events/release_artifacts_downloaded_audit_event_service_spec.rb'
- './ee/spec/services/audit_events/release_associate_milestone_audit_event_service_spec.rb'
- './ee/spec/services/audit_events/release_created_audit_event_service_spec.rb'
- './ee/spec/services/audit_events/release_updated_audit_event_service_spec.rb'
- './ee/spec/services/audit_events/streaming/headers/base_spec.rb'
- './ee/spec/services/audit_events/streaming/headers/create_service_spec.rb'
- './ee/spec/services/audit_events/streaming/headers/destroy_service_spec.rb'
- './ee/spec/services/audit_events/streaming/headers/update_service_spec.rb'
- './ee/spec/services/audit_events/user_impersonation_group_audit_event_service_spec.rb'
- './ee/spec/services/auto_merge/merge_train_service_spec.rb'
- './ee/spec/services/award_emojis/add_service_spec.rb'
- './ee/spec/services/award_emojis/destroy_service_spec.rb'
- './ee/spec/services/base_count_service_spec.rb'
- './ee/spec/services/boards/create_service_spec.rb'
- './ee/spec/services/boards/epic_boards/create_service_spec.rb'
- './ee/spec/services/boards/epic_boards/destroy_service_spec.rb'
- './ee/spec/services/boards/epic_boards/update_service_spec.rb'
- './ee/spec/services/boards/epic_boards/visits/create_service_spec.rb'
- './ee/spec/services/boards/epic_lists/create_service_spec.rb'
- './ee/spec/services/boards/epic_lists/destroy_service_spec.rb'
- './ee/spec/services/boards/epic_lists/list_service_spec.rb'
- './ee/spec/services/boards/epic_lists/update_service_spec.rb'
- './ee/spec/services/boards/epics/create_service_spec.rb'
- './ee/spec/services/boards/epics/list_service_spec.rb'
- './ee/spec/services/boards/epics/move_service_spec.rb'
- './ee/spec/services/boards/epics/position_create_service_spec.rb'
- './ee/spec/services/boards/epic_user_preferences/update_service_spec.rb'
- './ee/spec/services/boards/lists/update_service_spec.rb'
- './ee/spec/services/boards/update_service_spec.rb'
- './ee/spec/services/boards/user_preferences/update_service_spec.rb'
- './ee/spec/services/ci/audit_variable_change_service_spec.rb'
- './ee/spec/services/ci_cd/github_integration_setup_service_spec.rb'
- './ee/spec/services/ci_cd/github_setup_service_spec.rb'
- './ee/spec/services/ci_cd/setup_project_spec.rb'
- './ee/spec/services/ci/compare_license_scanning_reports_collapsed_service_spec.rb'
- './ee/spec/services/ci/compare_license_scanning_reports_service_spec.rb'
- './ee/spec/services/ci/compare_metrics_reports_service_spec.rb'
- './ee/spec/services/ci/copy_cross_database_associations_service_spec.rb'
- './ee/spec/services/ci/create_pipeline_service/compliance_spec.rb'
- './ee/spec/services/ci/create_pipeline_service/cross_needs_artifacts_spec.rb'
- './ee/spec/services/ci/create_pipeline_service/dast_configuration_spec.rb'
- './ee/spec/services/ci/create_pipeline_service/needs_spec.rb'
- './ee/spec/services/ci/create_pipeline_service/runnable_builds_spec.rb'
- './ee/spec/services/ci/create_pipeline_service_spec.rb'
- './ee/spec/services/ci/destroy_pipeline_service_spec.rb'
- './ee/spec/services/ci/external_pull_requests/process_github_event_service_spec.rb'
- './ee/spec/services/ci/minutes/additional_packs/change_namespace_service_spec.rb'
- './ee/spec/services/ci/minutes/additional_packs/create_service_spec.rb'
- './ee/spec/services/ci/minutes/email_notification_service_spec.rb'
- './ee/spec/services/ci/minutes/refresh_cached_data_service_spec.rb'
- './ee/spec/services/ci/minutes/reset_usage_service_spec.rb'
- './ee/spec/services/ci/minutes/track_live_consumption_service_spec.rb'
- './ee/spec/services/ci/minutes/update_build_minutes_service_spec.rb'
- './ee/spec/services/ci/minutes/update_project_and_namespace_usage_service_spec.rb'
- './ee/spec/services/ci/pipeline_bridge_status_service_spec.rb'
- './ee/spec/services/ci/pipeline_creation/drop_not_runnable_builds_service_spec.rb'
- './ee/spec/services/ci/pipeline_creation/start_pipeline_service_spec.rb'
- './ee/spec/services/ci/pipeline_trigger_service_spec.rb'
- './ee/spec/services/ci/play_bridge_service_spec.rb'
- './ee/spec/services/ci/play_build_service_spec.rb'
- './ee/spec/services/ci/process_build_service_spec.rb'
- './ee/spec/services/ci/process_pipeline_service_spec.rb'
- './ee/spec/services/ci/register_job_service_spec.rb'
- './ee/spec/services/ci/retry_job_service_spec.rb'
- './ee/spec/services/ci/retry_pipeline_service_spec.rb'
- './ee/spec/services/ci/runners/assign_runner_service_spec.rb'
- './ee/spec/services/ci/runners/register_runner_service_spec.rb'
- './ee/spec/services/ci/runners/reset_registration_token_service_spec.rb'
- './ee/spec/services/ci/runners/stale_group_runners_prune_service_spec.rb'
- './ee/spec/services/ci/runners/unassign_runner_service_spec.rb'
- './ee/spec/services/ci/runners/unregister_runner_service_spec.rb'
- './ee/spec/services/ci/subscribe_bridge_service_spec.rb'
- './ee/spec/services/ci/sync_reports_to_approval_rules_service_spec.rb'
- './ee/spec/services/ci/trigger_downstream_subscription_service_spec.rb'
- './ee/spec/services/compliance_management/frameworks/create_service_spec.rb'
- './ee/spec/services/compliance_management/frameworks/destroy_service_spec.rb'
- './ee/spec/services/compliance_management/frameworks/update_service_spec.rb'
- './ee/spec/services/compliance_management/merge_requests/create_compliance_violations_service_spec.rb'
- './ee/spec/services/concerns/epics/related_epic_links/usage_data_helper_spec.rb'
- './ee/spec/services/dashboard/environments/list_service_spec.rb'
- './ee/spec/services/dashboard/operations/list_service_spec.rb'
- './ee/spec/services/dashboard/projects/create_service_spec.rb'
- './ee/spec/services/dashboard/projects/list_service_spec.rb'
- './ee/spec/services/deploy_keys/create_service_spec.rb'
- './ee/spec/services/deployments/approval_service_spec.rb'
- './ee/spec/services/deployments/auto_rollback_service_spec.rb'
- './ee/spec/services/dora/aggregate_metrics_service_spec.rb'
- './ee/spec/services/ee/alert_management/alerts/update_service_spec.rb'
- './ee/spec/services/ee/alert_management/create_alert_issue_service_spec.rb'
- './ee/spec/services/ee/alert_management/http_integrations/create_service_spec.rb'
- './ee/spec/services/ee/alert_management/http_integrations/update_service_spec.rb'
- './ee/spec/services/ee/allowed_email_domains/update_service_spec.rb'
- './ee/spec/services/ee/auth/container_registry_authentication_service_spec.rb'
- './ee/spec/services/ee/auto_merge_service_spec.rb'
- './ee/spec/services/ee/boards/issues/create_service_spec.rb'
- './ee/spec/services/ee/boards/issues/list_service_spec.rb'
- './ee/spec/services/ee/boards/issues/move_service_spec.rb'
- './ee/spec/services/ee/boards/lists/create_service_spec.rb'
- './ee/spec/services/ee/boards/lists/list_service_spec.rb'
- './ee/spec/services/ee/boards/lists/max_limits_spec.rb'
- './ee/spec/services/ee/ci/change_variable_service_spec.rb'
- './ee/spec/services/ee/ci/change_variables_service_spec.rb'
- './ee/spec/services/ee/ci/job_artifacts/create_service_spec.rb'
- './ee/spec/services/ee/ci/job_artifacts/destroy_all_expired_service_spec.rb'
- './ee/spec/services/ee/ci/job_artifacts/destroy_batch_service_spec.rb'
- './ee/spec/services/ee/ci/pipeline_processing/atomic_processing_service_spec.rb'
- './ee/spec/services/ee/commits/create_service_spec.rb'
- './ee/spec/services/ee/deployments/update_environment_service_spec.rb'
- './ee/spec/services/ee/design_management/delete_designs_service_spec.rb'
- './ee/spec/services/ee/design_management/save_designs_service_spec.rb'
- './ee/spec/services/ee/event_create_service_spec.rb'
- './ee/spec/services/ee/git/wiki_push_service_spec.rb'
- './ee/spec/services/ee/gpg_keys/create_service_spec.rb'
- './ee/spec/services/ee/gpg_keys/destroy_service_spec.rb'
- './ee/spec/services/ee/groups/autocomplete_service_spec.rb'
- './ee/spec/services/ee/groups/deploy_tokens/create_service_spec.rb'
- './ee/spec/services/ee/groups/deploy_tokens/destroy_service_spec.rb'
- './ee/spec/services/ee/groups/deploy_tokens/revoke_service_spec.rb'
- './ee/spec/services/ee/groups/import_export/export_service_spec.rb'
- './ee/spec/services/ee/groups/import_export/import_service_spec.rb'
- './ee/spec/services/ee/integrations/test/project_service_spec.rb'
- './ee/spec/services/ee/ip_restrictions/update_service_spec.rb'
- './ee/spec/services/ee/issuable/bulk_update_service_spec.rb'
- './ee/spec/services/ee/issuable/common_system_notes_service_spec.rb'
- './ee/spec/services/ee/issuable/destroy_service_spec.rb'
- './ee/spec/services/ee/issue_links/create_service_spec.rb'
- './ee/spec/services/ee/issues/after_create_service_spec.rb'
- './ee/spec/services/ee/issues/create_service_spec.rb'
- './ee/spec/services/ee/issues/update_service_spec.rb'
- './ee/spec/services/ee/keys/create_service_spec.rb'
- './ee/spec/services/ee/keys/destroy_service_spec.rb'
- './ee/spec/services/ee/labels/promote_service_spec.rb'
- './ee/spec/services/ee/members/create_service_spec.rb'
- './ee/spec/services/ee/members/import_project_team_service_spec.rb'
- './ee/spec/services/ee/members/invite_service_spec.rb'
- './ee/spec/services/ee/members/update_service_spec.rb'
- './ee/spec/services/ee/merge_request_metrics_service_spec.rb'
- './ee/spec/services/ee/merge_requests/after_create_service_spec.rb'
- './ee/spec/services/ee/merge_requests/base_service_spec.rb'
- './ee/spec/services/ee/merge_requests/create_approval_event_service_spec.rb'
- './ee/spec/services/merge_requests/create_from_vulnerability_data_service_spec.rb'
- './ee/spec/services/ee/merge_requests/create_pipeline_service_spec.rb'
- './ee/spec/services/ee/merge_requests/create_service_spec.rb'
- './ee/spec/services/ee/merge_requests/execute_approval_hooks_service_spec.rb'
- './ee/spec/services/ee/merge_requests/handle_assignees_change_service_spec.rb'
- './ee/spec/services/ee/merge_requests/post_merge_service_spec.rb'
- './ee/spec/services/ee/merge_requests/refresh_service_spec.rb'
- './ee/spec/services/ee/merge_requests/update_assignees_service_spec.rb'
- './ee/spec/services/ee/merge_requests/update_reviewers_service_spec.rb'
- './ee/spec/services/ee/merge_requests/update_service_spec.rb'
- './ee/spec/services/ee/notes/create_service_spec.rb'
- './ee/spec/services/ee/notes/destroy_service_spec.rb'
- './ee/spec/services/ee/notes/post_process_service_spec.rb'
- './ee/spec/services/ee/notes/quick_actions_service_spec.rb'
- './ee/spec/services/ee/notes/update_service_spec.rb'
- './ee/spec/services/ee/notification_service_spec.rb'
- './ee/spec/services/ee/null_notification_service_spec.rb'
- './ee/spec/services/ee/personal_access_tokens/revoke_service_spec.rb'
- './ee/spec/services/ee/post_receive_service_spec.rb'
- './ee/spec/services/ee/preview_markdown_service_spec.rb'
- './ee/spec/services/ee/projects/autocomplete_service_spec.rb'
- './ee/spec/services/ee/projects/deploy_tokens/create_service_spec.rb'
- './ee/spec/services/ee/projects/deploy_tokens/destroy_service_spec.rb'
- './ee/spec/services/ee/protected_branches/create_service_spec.rb'
- './ee/spec/services/ee/protected_branches/destroy_service_spec.rb'
- './ee/spec/services/ee/protected_branches/update_service_spec.rb'
- './ee/spec/services/ee/quick_actions/target_service_spec.rb'
- './ee/spec/services/ee/releases/create_evidence_service_spec.rb'
- './ee/spec/services/ee/resource_events/change_iteration_service_spec.rb'
- './ee/spec/services/ee/resource_events/change_labels_service_spec.rb'
- './ee/spec/services/ee/resource_events/merge_into_notes_service_spec.rb'
- './ee/spec/services/ee/resource_events/synthetic_iteration_notes_builder_service_spec.rb'
- './ee/spec/services/ee/resource_events/synthetic_weight_notes_builder_service_spec.rb'
- './ee/spec/services/ee/system_notes/issuables_service_spec.rb'
- './ee/spec/services/ee/terraform/states/destroy_service_spec.rb'
- './ee/spec/services/ee/todos/destroy/entity_leave_service_spec.rb'
- './ee/spec/services/ee/two_factor/destroy_service_spec.rb'
- './ee/spec/services/ee/users/approve_service_spec.rb'
- './ee/spec/services/ee/users/authorized_build_service_spec.rb'
- './ee/spec/services/ee/users/block_service_spec.rb'
- './ee/spec/services/ee/users/build_service_spec.rb'
- './ee/spec/services/ee/users/create_service_spec.rb'
- './ee/spec/services/ee/users/destroy_service_spec.rb'
- './ee/spec/services/ee/users/reject_service_spec.rb'
- './ee/spec/services/ee/users/update_service_spec.rb'
- './ee/spec/services/ee/vulnerability_feedback_module/update_service_spec.rb'
- './ee/spec/services/elastic/data_migration_service_spec.rb'
- './ee/spec/services/elastic/index_projects_by_id_service_spec.rb'
- './ee/spec/services/elastic/index_projects_by_range_service_spec.rb'
- './ee/spec/services/emails/create_service_spec.rb'
- './ee/spec/services/emails/destroy_service_spec.rb'
- './ee/spec/services/epic_issues/destroy_service_spec.rb'
- './ee/spec/services/epic_issues/update_service_spec.rb'
- './ee/spec/services/epics/descendant_count_service_spec.rb'
- './ee/spec/services/epics/epic_links/destroy_service_spec.rb'
- './ee/spec/services/epics/epic_links/update_service_spec.rb'
- './ee/spec/services/epics/issue_promote_service_spec.rb'
- './ee/spec/services/epics/transfer_service_spec.rb'
- './ee/spec/services/epics/update_dates_service_spec.rb'
- './ee/spec/services/external_status_checks/create_service_spec.rb'
- './ee/spec/services/external_status_checks/destroy_service_spec.rb'
- './ee/spec/services/external_status_checks/dispatch_service_spec.rb'
- './ee/spec/services/external_status_checks/update_service_spec.rb'
- './ee/spec/services/feature_flag_issues/destroy_service_spec.rb'
- './ee/spec/services/geo/base_file_service_spec.rb'
- './ee/spec/services/geo/blob_download_service_spec.rb'
- './ee/spec/services/geo/blob_upload_service_spec.rb'
- './ee/spec/services/geo/cache_invalidation_event_store_spec.rb'
- './ee/spec/services/geo/container_repository_sync_service_spec.rb'
- './ee/spec/services/geo/container_repository_sync_spec.rb'
- './ee/spec/services/geo/file_registry_removal_service_spec.rb'
- './ee/spec/services/geo/framework_repository_sync_service_spec.rb'
- './ee/spec/services/geo/graphql_request_service_spec.rb'
- './ee/spec/services/geo/metrics_update_service_spec.rb'
- './ee/spec/services/geo/node_create_service_spec.rb'
- './ee/spec/services/geo/node_status_request_service_spec.rb'
- './ee/spec/services/geo/node_update_service_spec.rb'
- './ee/spec/services/geo/prune_event_log_service_spec.rb'
- './ee/spec/services/geo/registry_consistency_service_spec.rb'
- './ee/spec/services/geo/replication_toggle_request_service_spec.rb'
- './ee/spec/services/geo/repository_registry_removal_service_spec.rb'
- './ee/spec/services/gitlab_subscriptions/activate_service_spec.rb'
- './ee/spec/services/gitlab_subscriptions/check_future_renewal_service_spec.rb'
- './ee/spec/services/gitlab_subscriptions/create_company_lead_service_spec.rb'
- './ee/spec/services/gitlab_subscriptions/fetch_subscription_plans_service_spec.rb'
- './ee/spec/services/gitlab_subscriptions/preview_billable_user_change_service_spec.rb'
- './ee/spec/services/gitlab_subscriptions/reconciliations/calculate_seat_count_data_service_spec.rb'
- './ee/spec/services/gitlab_subscriptions/reconciliations/check_seat_usage_alerts_eligibility_service_spec.rb'
- './ee/spec/services/group_saml/identity/destroy_service_spec.rb'
- './ee/spec/services/group_saml/saml_group_links/create_service_spec.rb'
- './ee/spec/services/group_saml/saml_group_links/destroy_service_spec.rb'
- './ee/spec/services/group_saml/saml_provider/create_service_spec.rb'
- './ee/spec/services/group_saml/saml_provider/update_service_spec.rb'
- './ee/spec/services/ee/groups/create_service_spec.rb'
- './ee/spec/services/groups/destroy_service_spec.rb'
- './ee/spec/services/groups/epics_count_service_spec.rb'
- './ee/spec/services/groups/mark_for_deletion_service_spec.rb'
- './ee/spec/services/groups/participants_service_spec.rb'
- './ee/spec/services/groups/restore_service_spec.rb'
- './ee/spec/services/groups/schedule_bulk_repository_shard_moves_service_spec.rb'
- './ee/spec/services/groups/seat_usage_export_service_spec.rb'
- './ee/spec/services/groups/sync_service_spec.rb'
- './ee/spec/services/groups/update_repository_storage_service_spec.rb'
- './ee/spec/services/groups/update_service_spec.rb'
- './ee/spec/services/historical_user_data/csv_service_spec.rb'
- './ee/spec/services/ide/schemas_config_service_spec.rb'
- './ee/spec/services/issuable/destroy_label_links_service_spec.rb'
- './ee/spec/services/issue_feature_flags/list_service_spec.rb'
- './ee/spec/services/issues/build_service_spec.rb'
- './ee/spec/services/issues/duplicate_service_spec.rb'
- './ee/spec/services/issues/export_csv_service_spec.rb'
- './ee/spec/services/iterations/cadences/create_iterations_in_advance_service_spec.rb'
- './ee/spec/services/iterations/cadences/create_service_spec.rb'
- './ee/spec/services/iterations/cadences/destroy_service_spec.rb'
- './ee/spec/services/iterations/cadences/update_service_spec.rb'
- './ee/spec/services/iterations/create_service_spec.rb'
- './ee/spec/services/iterations/delete_service_spec.rb'
- './ee/spec/services/iterations/roll_over_issues_service_spec.rb'
- './ee/spec/services/iterations/update_service_spec.rb'
- './ee/spec/services/jira/jql_builder_service_spec.rb'
- './ee/spec/services/jira/requests/issues/list_service_spec.rb'
- './ee/spec/services/keys/last_used_service_spec.rb'
- './ee/spec/services/ldap_group_reset_service_spec.rb'
- './ee/spec/services/lfs/lock_file_service_spec.rb'
- './ee/spec/services/lfs/unlock_file_service_spec.rb'
- './ee/spec/services/licenses/destroy_service_spec.rb'
- './ee/spec/services/members/activate_service_spec.rb'
- './ee/spec/services/members/await_service_spec.rb'
- './ee/spec/services/merge_request_approval_settings/update_service_spec.rb'
- './ee/spec/services/merge_requests/approval_service_spec.rb'
- './ee/spec/services/merge_requests/build_service_spec.rb'
- './ee/spec/services/merge_requests/mergeability/check_approved_service_spec.rb'
- './ee/spec/services/merge_requests/mergeability/check_blocked_by_other_mrs_service_spec.rb'
- './ee/spec/services/merge_requests/merge_service_spec.rb'
- './ee/spec/services/merge_requests/merge_to_ref_service_spec.rb'
- './ee/spec/services/merge_requests/push_options_handler_service_spec.rb'
- './ee/spec/services/merge_requests/reload_merge_head_diff_service_spec.rb'
- './ee/spec/services/merge_requests/remove_approval_service_spec.rb'
- './ee/spec/services/merge_requests/reset_approvals_service_spec.rb'
- './ee/spec/services/merge_requests/stream_approval_audit_event_service_spec.rb'
- './ee/spec/services/merge_requests/sync_code_owner_approval_rules_spec.rb'
- './ee/spec/services/merge_requests/sync_report_approver_approval_rules_spec.rb'
- './ee/spec/services/merge_requests/update_blocks_service_spec.rb'
- './ee/spec/services/merge_trains/check_status_service_spec.rb'
- './ee/spec/services/merge_trains/create_pipeline_service_spec.rb'
- './ee/spec/services/merge_trains/refresh_merge_request_service_spec.rb'
- './ee/spec/services/merge_trains/refresh_service_spec.rb'
- './ee/spec/services/milestones/destroy_service_spec.rb'
- './ee/spec/services/milestones/promote_service_spec.rb'
- './ee/spec/services/milestones/update_service_spec.rb'
- './ee/spec/services/namespaces/storage/email_notification_service_spec.rb'
- './ee/spec/services/path_locks/lock_service_spec.rb'
- './ee/spec/services/path_locks/unlock_service_spec.rb'
- './ee/spec/services/personal_access_tokens/create_service_audit_log_spec.rb'
- './ee/spec/services/personal_access_tokens/groups/update_lifetime_service_spec.rb'
- './ee/spec/services/personal_access_tokens/instance/update_lifetime_service_spec.rb'
- './ee/spec/services/personal_access_tokens/revoke_invalid_tokens_spec.rb'
- './ee/spec/services/personal_access_tokens/revoke_service_audit_log_spec.rb'
- './ee/spec/services/projects/alerting/notify_service_spec.rb'
- './ee/spec/services/projects/cleanup_service_spec.rb'
- './ee/spec/services/projects/create_from_template_service_spec.rb'
- './ee/spec/services/projects/create_service_spec.rb'
- './ee/spec/services/projects/destroy_service_spec.rb'
- './ee/spec/services/projects/disable_deploy_key_service_spec.rb'
- './ee/spec/services/projects/disable_legacy_inactive_projects_service_spec.rb'
- './ee/spec/services/projects/enable_deploy_key_service_spec.rb'
- './ee/spec/services/projects/fork_service_spec.rb'
- './ee/spec/services/projects/gitlab_projects_import_service_spec.rb'
- './ee/spec/services/projects/group_links/create_service_spec.rb'
- './ee/spec/services/projects/group_links/destroy_service_spec.rb'
- './ee/spec/services/projects/group_links/update_service_spec.rb'
- './ee/spec/services/projects/import_export/export_service_spec.rb'
- './ee/spec/services/projects/import_service_spec.rb'
- './ee/spec/services/projects/mark_for_deletion_service_spec.rb'
- './ee/spec/services/projects/open_issues_count_service_spec.rb'
- './ee/spec/services/projects/operations/update_service_spec.rb'
- './ee/spec/services/projects/protect_default_branch_service_spec.rb'
- './ee/spec/services/projects/restore_service_spec.rb'
- './ee/spec/services/projects/setup_ci_cd_spec.rb'
- './ee/spec/services/projects/transfer_service_spec.rb'
- './ee/spec/services/projects/update_mirror_service_spec.rb'
- './ee/spec/services/projects/update_service_spec.rb'
- './ee/spec/services/protected_environments/base_service_spec.rb'
- './ee/spec/services/protected_environments/create_service_spec.rb'
- './ee/spec/services/protected_environments/destroy_service_spec.rb'
- './ee/spec/services/protected_environments/environment_dropdown_service_spec.rb'
- './ee/spec/services/protected_environments/search_service_spec.rb'
- './ee/spec/services/protected_environments/update_service_spec.rb'
- './ee/spec/services/push_rules/create_or_update_service_spec.rb'
- './ee/spec/services/quality_management/test_cases/create_service_spec.rb'
- './ee/spec/services/quick_actions/interpret_service_spec.rb'
- './ee/spec/services/releases/create_service_spec.rb'
- './ee/spec/services/releases/update_service_spec.rb'
- './ee/spec/services/repositories/housekeeping_service_spec.rb'
- './ee/spec/services/requirements_management/export_csv_service_spec.rb'
- './ee/spec/services/requirements_management/import_csv_service_spec.rb'
- './ee/spec/services/requirements_management/prepare_import_csv_service_spec.rb'
- './ee/spec/services/requirements_management/process_test_reports_service_spec.rb'
- './ee/spec/services/resource_access_tokens/create_service_spec.rb'
- './ee/spec/services/resource_access_tokens/revoke_service_spec.rb'
- './ee/spec/services/resource_events/change_weight_service_spec.rb'
- './ee/spec/services/security/ingestion/finding_map_collection_spec.rb'
- './ee/spec/services/security/ingestion/finding_map_spec.rb'
- './ee/spec/services/security/ingestion/ingest_report_service_spec.rb'
- './ee/spec/services/security/ingestion/ingest_report_slice_service_spec.rb'
- './ee/spec/services/security/ingestion/ingest_reports_service_spec.rb'
- './ee/spec/services/security/ingestion/mark_as_resolved_service_spec.rb'
- './ee/spec/services/security/merge_reports_service_spec.rb'
- './ee/spec/services/security/orchestration/assign_service_spec.rb'
- './ee/spec/services/security/orchestration/unassign_service_spec.rb'
- './ee/spec/services/security/override_uuids_service_spec.rb'
- './ee/spec/services/security/report_summary_service_spec.rb'
- './ee/spec/services/security/scanned_resources_counting_service_spec.rb'
- './ee/spec/services/security/scanned_resources_service_spec.rb'
- './ee/spec/services/security/security_orchestration_policies/ci_configuration_service_spec.rb'
- './ee/spec/services/security/security_orchestration_policies/create_pipeline_service_spec.rb'
- './ee/spec/services/security/security_orchestration_policies/fetch_policy_approvers_service_spec.rb'
- './ee/spec/services/security/security_orchestration_policies/fetch_policy_service_spec.rb'
- './ee/spec/services/security/security_orchestration_policies/on_demand_scan_pipeline_configuration_service_spec.rb'
- './ee/spec/services/security/security_orchestration_policies/operational_vulnerabilities_configuration_service_spec.rb'
- './ee/spec/services/security/security_orchestration_policies/policy_commit_service_spec.rb'
- './ee/spec/services/security/security_orchestration_policies/policy_configuration_validation_service_spec.rb'
- './ee/spec/services/security/security_orchestration_policies/process_policy_service_spec.rb'
- './ee/spec/services/security/security_orchestration_policies/process_rule_service_spec.rb'
- './ee/spec/services/security/security_orchestration_policies/project_create_service_spec.rb'
- './ee/spec/services/security/security_orchestration_policies/rule_schedule_service_spec.rb'
- './ee/spec/services/security/security_orchestration_policies/scan_pipeline_service_spec.rb'
- './ee/spec/services/security/security_orchestration_policies/sync_opened_merge_requests_service_spec.rb'
- './ee/spec/services/security/security_orchestration_policies/validate_policy_service_spec.rb'
- './ee/spec/services/security/store_grouped_scans_service_spec.rb'
- './ee/spec/services/security/store_scan_service_spec.rb'
- './ee/spec/services/security/token_revocation_service_spec.rb'
- './ee/spec/services/security/track_scan_service_spec.rb'
- './ee/spec/services/security/update_training_service_spec.rb'
- './ee/spec/services/security/vulnerability_counting_service_spec.rb'
- './ee/spec/services/sitemap/create_service_spec.rb'
- './ee/spec/services/software_license_policies/create_service_spec.rb'
- './ee/spec/services/start_pull_mirroring_service_spec.rb'
- './ee/spec/services/system_notes/epics_service_spec.rb'
- './ee/spec/services/system_note_service_spec.rb'
- './ee/spec/services/system_notes/escalations_service_spec.rb'
- './ee/spec/services/system_notes/merge_train_service_spec.rb'
- './ee/spec/services/system_notes/vulnerabilities_service_spec.rb'
- './ee/spec/services/timebox_report_service_spec.rb'
- './ee/spec/services/timelogs/create_service_spec.rb'
- './ee/spec/services/todos/allowed_target_filter_service_spec.rb'
- './ee/spec/services/todos/destroy/confidential_epic_service_spec.rb'
- './ee/spec/services/todo_service_spec.rb'
- './ee/spec/services/user_permissions/export_service_spec.rb'
- './ee/spec/services/users/abuse/git_abuse/namespace_throttle_service_spec.rb'
- './ee/spec/services/users/abuse/namespace_bans/create_service_spec.rb'
- './ee/spec/services/users/abuse/namespace_bans/destroy_service_spec.rb'
- './ee/spec/services/users_ops_dashboard_projects/destroy_service_spec.rb'
- './ee/spec/services/users/update_highest_member_role_service_spec.rb'
- './ee/spec/services/web_hook_service_spec.rb'
- './ee/spec/services/wiki_pages/create_service_spec.rb'
- './ee/spec/services/wiki_pages/destroy_service_spec.rb'
- './ee/spec/services/wiki_pages/update_service_spec.rb'
- './ee/spec/services/wikis/create_attachment_service_spec.rb'
- './ee/spec/services/work_items/update_service_spec.rb'
- './ee/spec/tasks/geo_rake_spec.rb'
- './ee/spec/tasks/gitlab/check_rake_spec.rb'
- './ee/spec/tasks/gitlab/geo_rake_spec.rb'
- './ee/spec/tasks/gitlab/license_rake_spec.rb'
- './ee/spec/tasks/gitlab/seed/group_seed_rake_spec.rb'
- './ee/spec/tasks/gitlab/spdx_rake_spec.rb'
- './ee/spec/validators/json_schema_validator_spec.rb'
- './ee/spec/validators/ldap_filter_validator_spec.rb'
- './ee/spec/validators/password/complexity_validator_spec.rb'
- './ee/spec/validators/user_existence_validator_spec.rb'
- './ee/spec/views/admin/application_settings/general.html.haml_spec.rb'
- './ee/spec/views/admin/application_settings/_git_abuse_rate_limit.html.haml_spec.rb'
- './ee/spec/views/admin/dashboard/index.html.haml_spec.rb'
- './ee/spec/views/admin/dev_ops_report/show.html.haml_spec.rb'
- './ee/spec/views/admin/groups/_form.html.haml_spec.rb'
- './ee/spec/views/admin/identities/index.html.haml_spec.rb'
- './ee/spec/views/admin/push_rules/_merge_request_approvals.html.haml_spec.rb'
- './ee/spec/views/admin/users/_credit_card_info.html.haml_spec.rb'
- './ee/spec/views/admin/users/index.html.haml_spec.rb'
- './ee/spec/views/admin/users/show.html.haml_spec.rb'
- './ee/spec/views/clusters/clusters/show.html.haml_spec.rb'
- './ee/spec/views/compliance_management/compliance_framework/_compliance_frameworks_info.html.haml_spec.rb'
- './ee/spec/views/devise/sessions/new.html.haml_spec.rb'
- './ee/spec/views/groups/billings/index.html.haml_spec.rb'
- './ee/spec/views/groups/edit.html.haml_spec.rb'
- './ee/spec/views/groups/hook_logs/show.html.haml_spec.rb'
- './ee/spec/views/groups/hooks/edit.html.haml_spec.rb'
- './ee/spec/views/groups/security/discover/show.html.haml_spec.rb'
- './ee/spec/views/groups/settings/_remove.html.haml_spec.rb'
- './ee/spec/views/groups/settings/reporting/show.html.haml_spec.rb'
- './ee/spec/views/layouts/application.html.haml_spec.rb'
- './ee/spec/views/layouts/header/_ee_subscribable_banner.html.haml_spec.rb'
- './ee/spec/views/layouts/header/_read_only_banner.html.haml_spec.rb'
- './ee/spec/views/operations/environments.html.haml_spec.rb'
- './ee/spec/views/operations/index.html.haml_spec.rb'
- './ee/spec/views/profiles/preferences/show.html.haml_spec.rb'
- './ee/spec/views/projects/edit.html.haml_spec.rb'
- './ee/spec/views/projects/on_demand_scans/index.html.haml_spec.rb'
- './ee/spec/views/projects/security/corpus_management/show.html.haml_spec.rb'
- './ee/spec/views/projects/security/dast_profiles/show.html.haml_spec.rb'
- './ee/spec/views/projects/security/dast_scanner_profiles/edit.html.haml_spec.rb'
- './ee/spec/views/projects/security/dast_scanner_profiles/new.html.haml_spec.rb'
- './ee/spec/views/projects/security/dast_site_profiles/edit.html.haml_spec.rb'
- './ee/spec/views/projects/security/dast_site_profiles/new.html.haml_spec.rb'
- './ee/spec/views/projects/security/discover/show.html.haml_spec.rb'
- './ee/spec/views/projects/security/policies/index.html.haml_spec.rb'
- './ee/spec/views/projects/security/sast_configuration/show.html.haml_spec.rb'
- './ee/spec/views/projects/settings/merge_requests/_merge_request_status_checks_settings.html.haml_spec.rb'
- './ee/spec/views/registrations/groups/new.html.haml_spec.rb'
- './ee/spec/views/shared/billings/_billing_plan_actions.html.haml_spec.rb'
- './ee/spec/views/shared/billings/_billing_plan.html.haml_spec.rb'
- './ee/spec/views/shared/billings/_billing_plans.html.haml_spec.rb'
- './ee/spec/views/shared/billings/_trial_status.html.haml_spec.rb'
- './ee/spec/views/shared/_clone_panel.html.haml_spec.rb'
- './ee/spec/views/shared/credentials_inventory/_expiry_date.html.haml_spec.rb'
- './ee/spec/views/shared/credentials_inventory/gpg_keys/_gpg_key.html.haml_spec.rb'
- './ee/spec/views/shared/credentials_inventory/personal_access_tokens/_personal_access_token.html.haml_spec.rb'
- './ee/spec/views/shared/credentials_inventory/ssh_keys/_ssh_key.html.haml_spec.rb'
- './ee/spec/views/shared/issuable/_approver_suggestion.html.haml_spec.rb'
- './ee/spec/views/shared/issuable/_epic_dropdown.html.haml_spec.rb'
- './ee/spec/views/shared/issuable/_health_status_dropdown.html.haml_spec.rb'
- './ee/spec/views/shared/issuable/_iterations_dropdown.html.haml_spec.rb'
- './ee/spec/views/shared/issuable/_sidebar.html.haml_spec.rb'
- './ee/spec/views/shared/_kerberos_clone_button.html.haml_spec.rb'
- './ee/spec/views/shared/labels/_create_label_help_text.html.haml_spec.rb'
- './ee/spec/views/shared/milestones/_milestone.html.haml_spec.rb'
- './ee/spec/views/shared/_mirror_status.html.haml_spec.rb'
- './ee/spec/views/shared/_mirror_update_button.html.haml_spec.rb'
- './ee/spec/views/shared/_namespace_user_cap_reached_alert.html.haml_spec.rb'
- './ee/spec/views/shared/promotions/_promotion_link_project.html.haml_spec.rb'
- './ee/spec/workers/active_user_count_threshold_worker_spec.rb'
- './ee/spec/workers/adjourned_projects_deletion_cron_worker_spec.rb'
- './ee/spec/workers/admin_emails_worker_spec.rb'
- './ee/spec/workers/analytics/code_review_metrics_worker_spec.rb'
- './ee/spec/workers/analytics/cycle_analytics/consistency_worker_spec.rb'
- './ee/spec/workers/analytics/cycle_analytics/incremental_worker_spec.rb'
- './ee/spec/workers/analytics/cycle_analytics/reaggregation_worker_spec.rb'
- './ee/spec/workers/analytics/devops_adoption/create_all_snapshots_worker_spec.rb'
- './ee/spec/workers/analytics/devops_adoption/create_snapshot_worker_spec.rb'
- './ee/spec/workers/approval_rules/external_approval_rule_payload_worker_spec.rb'
- './ee/spec/workers/app_sec/dast/profile_schedule_worker_spec.rb'
- './ee/spec/workers/app_sec/dast/scanner_profiles_builds/consistency_worker_spec.rb'
- './ee/spec/workers/app_sec/dast/scans/consistency_worker_spec.rb'
- './ee/spec/workers/app_sec/dast/site_profiles_builds/consistency_worker_spec.rb'
- './ee/spec/workers/audit_events/audit_event_streaming_worker_spec.rb'
- './ee/spec/workers/audit_events/user_impersonation_event_create_worker_spec.rb'
- './ee/spec/workers/auth/saml_group_sync_worker_spec.rb'
- './ee/spec/workers/ci/initial_pipeline_process_worker_spec.rb'
- './ee/spec/workers/ci/minutes/refresh_cached_data_worker_spec.rb'
- './ee/spec/workers/ci/minutes/update_project_and_namespace_usage_worker_spec.rb'
- './ee/spec/workers/ci/runners/stale_group_runners_prune_cron_worker_spec.rb'
- './ee/spec/workers/ci/sync_reports_to_report_approval_rules_worker_spec.rb'
- './ee/spec/workers/ci/trigger_downstream_subscriptions_worker_spec.rb'
- './ee/spec/workers/ci/upstream_projects_subscriptions_cleanup_worker_spec.rb'
- './ee/spec/workers/compliance_management/chain_of_custody_report_worker_spec.rb'
- './ee/spec/workers/compliance_management/merge_requests/compliance_violations_worker_spec.rb'
- './ee/spec/workers/concerns/elastic/migration_options_spec.rb'
- './ee/spec/workers/concerns/update_orchestration_policy_configuration_spec.rb'
- './ee/spec/workers/create_github_webhook_worker_spec.rb'
- './ee/spec/workers/deployments/auto_rollback_worker_spec.rb'
- './ee/spec/workers/dora/daily_metrics/refresh_worker_spec.rb'
- './ee/spec/workers/ee/arkose/blocked_users_report_worker_spec.rb'
- './ee/spec/workers/ee/ci/build_finished_worker_spec.rb'
- './ee/spec/workers/ee/issuable_export_csv_worker_spec.rb'
- './ee/spec/workers/ee/namespaces/root_statistics_worker_spec.rb'
- './ee/spec/workers/ee/projects/inactive_projects_deletion_cron_worker_spec.rb'
- './ee/spec/workers/ee/repository_check/batch_worker_spec.rb'
- './ee/spec/workers/ee/repository_check/single_repository_worker_spec.rb'
- './ee/spec/workers/elastic_cluster_reindexing_cron_worker_spec.rb'
- './ee/spec/workers/elastic_delete_project_worker_spec.rb'
- './ee/spec/workers/elastic_full_index_worker_spec.rb'
- './ee/spec/workers/elastic_index_initial_bulk_cron_worker_spec.rb'
- './ee/spec/workers/elastic_namespace_indexer_worker_spec.rb'
- './ee/spec/workers/elastic_namespace_rollout_worker_spec.rb'
- './ee/spec/workers/elastic/project_transfer_worker_spec.rb'
- './ee/spec/workers/elastic_remove_expired_namespace_subscriptions_from_index_cron_worker_spec.rb'
- './ee/spec/workers/epics/new_epic_issue_worker_spec.rb'
- './ee/spec/workers/geo/batch_event_create_worker_spec.rb'
- './ee/spec/workers/geo/container_repository_sync_worker_spec.rb'
- './ee/spec/workers/geo/create_repository_updated_event_worker_spec.rb'
- './ee/spec/workers/geo/destroy_worker_spec.rb'
- './ee/spec/workers/geo/metrics_update_worker_spec.rb'
- './ee/spec/workers/geo/prune_event_log_worker_spec.rb'
- './ee/spec/workers/geo/registry_sync_worker_spec.rb'
- './ee/spec/workers/geo/reverification_batch_worker_spec.rb'
- './ee/spec/workers/geo/scheduler/scheduler_worker_spec.rb'
- './ee/spec/workers/geo/secondary/registry_consistency_worker_spec.rb'
- './ee/spec/workers/geo/secondary_usage_data_cron_worker_spec.rb'
- './ee/spec/workers/geo/sidekiq_cron_config_worker_spec.rb'
- './ee/spec/workers/geo/sync_timeout_cron_worker_spec.rb'
- './ee/spec/workers/geo/verification_batch_worker_spec.rb'
- './ee/spec/workers/geo/verification_cron_worker_spec.rb'
- './ee/spec/workers/geo/verification_state_backfill_service_spec.rb'
- './ee/spec/workers/geo/verification_state_backfill_worker_spec.rb'
- './ee/spec/workers/geo/verification_timeout_worker_spec.rb'
- './ee/spec/workers/group_saml_group_sync_worker_spec.rb'
- './ee/spec/workers/groups/create_event_worker_spec.rb'
- './ee/spec/workers/groups/export_memberships_worker_spec.rb'
- './ee/spec/workers/groups/schedule_bulk_repository_shard_moves_worker_spec.rb'
- './ee/spec/workers/groups/update_repository_storage_worker_spec.rb'
- './ee/spec/workers/group_wikis/git_garbage_collect_worker_spec.rb'
- './ee/spec/workers/historical_data_worker_spec.rb'
- './ee/spec/workers/import_software_licenses_worker_spec.rb'
- './ee/spec/workers/iterations/cadences/create_iterations_worker_spec.rb'
- './ee/spec/workers/iterations/cadences/schedule_create_iterations_worker_spec.rb'
- './ee/spec/workers/iterations/roll_over_issues_worker_spec.rb'
- './ee/spec/workers/iterations_update_status_worker_spec.rb'
- './ee/spec/workers/ldap_all_groups_sync_worker_spec.rb'
- './ee/spec/workers/ldap_group_sync_worker_spec.rb'
- './ee/spec/workers/ldap_sync_worker_spec.rb'
- './ee/spec/workers/licenses/reset_submit_license_usage_data_banner_worker_spec.rb'
- './ee/spec/workers/merge_request_reset_approvals_worker_spec.rb'
- './ee/spec/workers/merge_requests/stream_approval_audit_event_worker_spec.rb'
- './ee/spec/workers/merge_requests/sync_code_owner_approval_rules_worker_spec.rb'
- './ee/spec/workers/namespaces/sync_namespace_name_worker_spec.rb'
- './ee/spec/workers/personal_access_tokens/groups/policy_worker_spec.rb'
- './ee/spec/workers/personal_access_tokens/instance/policy_worker_spec.rb'
- './ee/spec/workers/post_receive_spec.rb'
- './ee/spec/workers/project_cache_worker_spec.rb'
- './ee/spec/workers/project_import_schedule_worker_spec.rb'
- './ee/spec/workers/projects/disable_legacy_open_source_license_for_inactive_projects_worker_spec.rb'
- './ee/spec/workers/project_template_export_worker_spec.rb'
- './ee/spec/workers/repository_import_worker_spec.rb'
- './ee/spec/workers/repository_update_mirror_worker_spec.rb'
- './ee/spec/workers/requirements_management/import_requirements_csv_worker_spec.rb'
- './ee/spec/workers/requirements_management/process_requirements_reports_worker_spec.rb'
- './ee/spec/workers/sbom/ingest_reports_worker_spec.rb'
- './ee/spec/workers/scan_security_report_secrets_worker_spec.rb'
- './ee/spec/workers/security/create_orchestration_policy_worker_spec.rb'
- './ee/spec/workers/security/orchestration_policy_rule_schedule_namespace_worker_spec.rb'
- './ee/spec/workers/security/orchestration_policy_rule_schedule_worker_spec.rb'
- './ee/spec/workers/security/sync_scan_policies_worker_spec.rb'
- './ee/spec/workers/security/track_secure_scans_worker_spec.rb'
- './ee/spec/workers/set_user_status_based_on_user_cap_setting_worker_spec.rb'
- './ee/spec/workers/sync_seat_link_request_worker_spec.rb'
- './ee/spec/workers/sync_seat_link_worker_spec.rb'
- './ee/spec/workers/update_all_mirrors_worker_spec.rb'
- './ee/spec/workers/vulnerabilities/historical_statistics/deletion_worker_spec.rb'
- './ee/spec/workers/vulnerabilities/statistics/adjustment_worker_spec.rb'
- './ee/spec/workers/vulnerabilities/statistics/schedule_worker_spec.rb'
- './ee/spec/workers/vulnerability_exports/export_deletion_worker_spec.rb'
- './ee/spec/workers/vulnerability_exports/export_worker_spec.rb'
- './spec/bin/feature_flag_spec.rb'
- './spec/bin/sidekiq_cluster_spec.rb'
- './spec/channels/application_cable/connection_spec.rb'
- './spec/commands/metrics_server/metrics_server_spec.rb'
- './spec/commands/sidekiq_cluster/cli_spec.rb'
- './spec/config/application_spec.rb'
- './spec/config/inject_enterprise_edition_module_spec.rb'
- './spec/config/mail_room_spec.rb'
- './spec/config/object_store_settings_spec.rb'
- './spec/config/settings_spec.rb'
- './spec/config/smime_signature_settings_spec.rb'
- './spec/controllers/acme_challenges_controller_spec.rb'
- './spec/controllers/admin/applications_controller_spec.rb'
- './spec/controllers/admin/application_settings/appearances_controller_spec.rb'
- './spec/controllers/admin/application_settings_controller_spec.rb'
- './spec/controllers/admin/ci/variables_controller_spec.rb'
- './spec/controllers/admin/clusters_controller_spec.rb'
- './spec/controllers/admin/cohorts_controller_spec.rb'
- './spec/controllers/admin/dashboard_controller_spec.rb'
- './spec/controllers/admin/dev_ops_report_controller_spec.rb'
- './spec/controllers/admin/gitaly_servers_controller_spec.rb'
- './spec/controllers/admin/groups_controller_spec.rb'
- './spec/controllers/admin/health_check_controller_spec.rb'
- './spec/controllers/admin/hooks_controller_spec.rb'
- './spec/controllers/admin/identities_controller_spec.rb'
- './spec/controllers/admin/impersonations_controller_spec.rb'
- './spec/controllers/admin/instance_review_controller_spec.rb'
- './spec/controllers/admin/integrations_controller_spec.rb'
- './spec/controllers/admin/jobs_controller_spec.rb'
- './spec/controllers/admin/plan_limits_controller_spec.rb'
- './spec/controllers/admin/projects_controller_spec.rb'
- './spec/controllers/admin/sessions_controller_spec.rb'
- './spec/controllers/admin/spam_logs_controller_spec.rb'
- './spec/controllers/admin/topics/avatars_controller_spec.rb'
- './spec/controllers/admin/topics_controller_spec.rb'
- './spec/controllers/admin/usage_trends_controller_spec.rb'
- './spec/controllers/admin/users_controller_spec.rb'
- './spec/controllers/application_controller_spec.rb'
- './spec/controllers/autocomplete_controller_spec.rb'
- './spec/controllers/chaos_controller_spec.rb'
- './spec/controllers/confirmations_controller_spec.rb'
- './spec/controllers/dashboard_controller_spec.rb'
- './spec/controllers/dashboard/groups_controller_spec.rb'
- './spec/controllers/dashboard/labels_controller_spec.rb'
- './spec/controllers/dashboard/milestones_controller_spec.rb'
- './spec/controllers/dashboard/projects_controller_spec.rb'
- './spec/controllers/dashboard/snippets_controller_spec.rb'
- './spec/controllers/dashboard/todos_controller_spec.rb'
- './spec/controllers/every_controller_spec.rb'
- './spec/controllers/explore/groups_controller_spec.rb'
- './spec/controllers/explore/projects_controller_spec.rb'
- './spec/controllers/explore/snippets_controller_spec.rb'
- './spec/controllers/google_api/authorizations_controller_spec.rb'
- './spec/controllers/graphql_controller_spec.rb'
- './spec/controllers/health_check_controller_spec.rb'
- './spec/controllers/help_controller_spec.rb'
- './spec/controllers/import/bitbucket_controller_spec.rb'
- './spec/controllers/import/bitbucket_server_controller_spec.rb'
- './spec/controllers/import/bulk_imports_controller_spec.rb'
- './spec/controllers/import/fogbugz_controller_spec.rb'
- './spec/controllers/import/gitea_controller_spec.rb'
- './spec/controllers/import/github_controller_spec.rb'
- './spec/controllers/import/manifest_controller_spec.rb'
- './spec/controllers/invites_controller_spec.rb'
- './spec/controllers/jira_connect/app_descriptor_controller_spec.rb'
- './spec/controllers/jira_connect/branches_controller_spec.rb'
- './spec/controllers/jira_connect/events_controller_spec.rb'
- './spec/controllers/jira_connect/subscriptions_controller_spec.rb'
- './spec/controllers/ldap/omniauth_callbacks_controller_spec.rb'
- './spec/controllers/metrics_controller_spec.rb'
- './spec/controllers/oauth/applications_controller_spec.rb'
- './spec/controllers/oauth/authorizations_controller_spec.rb'
- './spec/controllers/oauth/authorized_applications_controller_spec.rb'
- './spec/controllers/oauth/token_info_controller_spec.rb'
- './spec/controllers/oauth/tokens_controller_spec.rb'
- './spec/controllers/omniauth_callbacks_controller_spec.rb'
- './spec/controllers/passwords_controller_spec.rb'
- './spec/controllers/profiles/accounts_controller_spec.rb'
- './spec/controllers/user_settings/active_sessions_controller_spec.rb'
- './spec/controllers/profiles/avatars_controller_spec.rb'
- './spec/controllers/profiles_controller_spec.rb'
- './spec/controllers/profiles/emails_controller_spec.rb'
- './spec/controllers/profiles/notifications_controller_spec.rb'
- './spec/controllers/profiles/preferences_controller_spec.rb'
- './spec/controllers/projects/alerting/notifications_controller_spec.rb'
- './spec/controllers/projects/analytics/cycle_analytics/stages_controller_spec.rb'
- './spec/controllers/projects/analytics/cycle_analytics/summary_controller_spec.rb'
- './spec/controllers/projects/analytics/cycle_analytics/value_streams_controller_spec.rb'
- './spec/controllers/projects/artifacts_controller_spec.rb'
- './spec/controllers/projects/autocomplete_sources_controller_spec.rb'
- './spec/controllers/projects/avatars_controller_spec.rb'
- './spec/controllers/projects/badges_controller_spec.rb'
- './spec/controllers/projects/blame_controller_spec.rb'
- './spec/controllers/projects/blob_controller_spec.rb'
- './spec/controllers/projects/boards_controller_spec.rb'
- './spec/controllers/projects/branches_controller_spec.rb'
- './spec/controllers/projects/ci/daily_build_group_report_results_controller_spec.rb'
- './spec/controllers/projects/ci/lints_controller_spec.rb'
- './spec/controllers/projects/ci/pipeline_editor_controller_spec.rb'
- './spec/controllers/projects/clusters_controller_spec.rb'
- './spec/controllers/projects/commit_controller_spec.rb'
- './spec/controllers/projects/commits_controller_spec.rb'
- './spec/controllers/projects/compare_controller_spec.rb'
- './spec/controllers/projects_controller_spec.rb'
- './spec/controllers/projects/cycle_analytics_controller_spec.rb'
- './spec/controllers/projects/cycle_analytics/events_controller_spec.rb'
- './spec/controllers/projects/deploy_keys_controller_spec.rb'
- './spec/controllers/projects/deployments_controller_spec.rb'
- './spec/controllers/projects/design_management/designs/raw_images_controller_spec.rb'
- './spec/controllers/projects/design_management/designs/resized_image_controller_spec.rb'
- './spec/controllers/projects/discussions_controller_spec.rb'
- './spec/controllers/projects/environments_controller_spec.rb'
- './spec/controllers/projects/error_tracking_controller_spec.rb'
- './spec/controllers/projects/error_tracking/projects_controller_spec.rb'
- './spec/controllers/projects/error_tracking/stack_traces_controller_spec.rb'
- './spec/controllers/projects/feature_flags_clients_controller_spec.rb'
- './spec/controllers/projects/feature_flags_controller_spec.rb'
- './spec/controllers/projects/feature_flags_user_lists_controller_spec.rb'
- './spec/controllers/projects/find_file_controller_spec.rb'
- './spec/controllers/projects/forks_controller_spec.rb'
- './spec/controllers/projects/graphs_controller_spec.rb'
- './spec/controllers/projects/group_links_controller_spec.rb'
- './spec/controllers/projects/hooks_controller_spec.rb'
- './spec/controllers/projects/import/jira_controller_spec.rb'
- './spec/controllers/projects/imports_controller_spec.rb'
- './spec/controllers/projects/issue_links_controller_spec.rb'
- './spec/controllers/projects/issues_controller_spec.rb'
- './spec/controllers/projects/jobs_controller_spec.rb'
- './spec/controllers/projects/labels_controller_spec.rb'
- './spec/controllers/projects/mattermosts_controller_spec.rb'
- './spec/controllers/projects/merge_requests/conflicts_controller_spec.rb'
- './spec/controllers/projects/merge_requests/content_controller_spec.rb'
- './spec/controllers/projects/merge_requests_controller_spec.rb'
- './spec/controllers/projects/merge_requests/creations_controller_spec.rb'
- './spec/controllers/projects/merge_requests/diffs_controller_spec.rb'
- './spec/controllers/projects/merge_requests/drafts_controller_spec.rb'
- './spec/controllers/projects/milestones_controller_spec.rb'
- './spec/controllers/projects/mirrors_controller_spec.rb'
- './spec/controllers/projects/notes_controller_spec.rb'
- './spec/controllers/projects/packages/infrastructure_registry_controller_spec.rb'
- './spec/controllers/projects/packages/packages_controller_spec.rb'
- './spec/controllers/projects/pages_controller_spec.rb'
- './spec/controllers/projects/pages_domains_controller_spec.rb'
- './spec/controllers/projects/pipeline_schedules_controller_spec.rb'
- './spec/controllers/projects/pipelines_controller_spec.rb'
- './spec/controllers/projects/pipelines_settings_controller_spec.rb'
- './spec/controllers/projects/pipelines/stages_controller_spec.rb'
- './spec/controllers/projects/pipelines/tests_controller_spec.rb'
- './spec/controllers/projects/project_members_controller_spec.rb'
- './spec/controllers/projects/protected_branches_controller_spec.rb'
- './spec/controllers/projects/protected_tags_controller_spec.rb'
- './spec/controllers/projects/raw_controller_spec.rb'
- './spec/controllers/projects/refs_controller_spec.rb'
- './spec/controllers/projects/registry/repositories_controller_spec.rb'
- './spec/controllers/projects/registry/tags_controller_spec.rb'
- './spec/controllers/projects/releases_controller_spec.rb'
- './spec/controllers/projects/releases/evidences_controller_spec.rb'
- './spec/controllers/projects/repositories_controller_spec.rb'
- './spec/controllers/projects/security/configuration_controller_spec.rb'
- './spec/controllers/projects/settings/ci_cd_controller_spec.rb'
- './spec/controllers/projects/settings/integration_hook_logs_controller_spec.rb'
- './spec/controllers/projects/settings/integrations_controller_spec.rb'
- './spec/controllers/projects/settings/operations_controller_spec.rb'
- './spec/controllers/projects/settings/repository_controller_spec.rb'
- './spec/controllers/projects/snippets/blobs_controller_spec.rb'
- './spec/controllers/projects/snippets_controller_spec.rb'
- './spec/controllers/projects/starrers_controller_spec.rb'
- './spec/controllers/projects/tags_controller_spec.rb'
- './spec/controllers/projects/templates_controller_spec.rb'
- './spec/controllers/projects/terraform_controller_spec.rb'
- './spec/controllers/projects/todos_controller_spec.rb'
- './spec/controllers/projects/tree_controller_spec.rb'
- './spec/controllers/projects/uploads_controller_spec.rb'
- './spec/controllers/projects/usage_quotas_controller_spec.rb'
- './spec/controllers/projects/variables_controller_spec.rb'
- './spec/controllers/projects/web_ide_schemas_controller_spec.rb'
- './spec/controllers/projects/web_ide_terminals_controller_spec.rb'
- './spec/controllers/projects/wikis_controller_spec.rb'
- './spec/controllers/registrations_controller_spec.rb'
- './spec/controllers/repositories/git_http_controller_spec.rb'
- './spec/controllers/repositories/lfs_storage_controller_spec.rb'
- './spec/controllers/root_controller_spec.rb'
- './spec/controllers/sent_notifications_controller_spec.rb'
- './spec/controllers/sessions_controller_spec.rb'
- './spec/controllers/snippets/blobs_controller_spec.rb'
- './spec/controllers/snippets_controller_spec.rb'
- './spec/controllers/snippets/notes_controller_spec.rb'
- './spec/controllers/uploads_controller_spec.rb'
- './spec/controllers/users/callouts_controller_spec.rb'
- './spec/controllers/users/terms_controller_spec.rb'
- './spec/controllers/users/unsubscribes_controller_spec.rb'
- './spec/db/development/create_base_work_item_types_spec.rb'
- './spec/db/docs_spec.rb'
- './spec/db/migration_spec.rb'
- './spec/db/production/create_base_work_item_types_spec.rb'
- './spec/db/production/settings_spec.rb'
- './spec/dependencies/omniauth_saml_spec.rb'
- './spec/experiments/application_experiment_spec.rb'
- './spec/features/abuse_report_spec.rb'
- './spec/features/admin/admin_abuse_reports_spec.rb'
- './spec/features/admin/admin_appearance_spec.rb'
- './spec/features/admin/admin_browse_spam_logs_spec.rb'
- './spec/features/admin/admin_deploy_keys_spec.rb'
- './spec/features/admin/admin_dev_ops_reports_spec.rb'
- './spec/features/admin/admin_disables_git_access_protocol_spec.rb'
- './spec/features/admin/admin_disables_two_factor_spec.rb'
- './spec/features/admin/admin_groups_spec.rb'
- './spec/features/admin/admin_health_check_spec.rb'
- './spec/features/admin/admin_hook_logs_spec.rb'
- './spec/features/admin/admin_hooks_spec.rb'
- './spec/features/admin/admin_labels_spec.rb'
- './spec/features/admin/admin_manage_applications_spec.rb'
- './spec/features/admin/admin_mode/login_spec.rb'
- './spec/features/admin/admin_mode/logout_spec.rb'
- './spec/features/admin/admin_mode_spec.rb'
- './spec/features/admin/admin_mode/workers_spec.rb'
- './spec/features/admin/admin_projects_spec.rb'
- './spec/features/admin/admin_search_settings_spec.rb'
- './spec/features/admin/admin_sees_background_migrations_spec.rb'
- './spec/features/admin/admin_sees_projects_statistics_spec.rb'
- './spec/features/admin/admin_sees_project_statistics_spec.rb'
- './spec/features/admin/admin_settings_spec.rb'
- './spec/features/admin/admin_system_info_spec.rb'
- './spec/features/admin/admin_users_impersonation_tokens_spec.rb'
- './spec/features/admin/admin_users_spec.rb'
- './spec/features/admin/admin_uses_repository_checks_spec.rb'
- './spec/features/admin/dashboard_spec.rb'
- './spec/features/admin/integrations/instance_integrations_spec.rb'
- './spec/features/admin/integrations/user_activates_mattermost_slash_command_spec.rb'
- './spec/features/admin/users/users_spec.rb'
- './spec/features/admin_variables_spec.rb'
- './spec/features/alert_management/alert_details_spec.rb'
- './spec/features/alert_management/alert_management_list_spec.rb'
- './spec/features/alert_management_spec.rb'
- './spec/features/alert_management/user_filters_alerts_by_status_spec.rb'
- './spec/features/alert_management/user_searches_alerts_spec.rb'
- './spec/features/alert_management/user_updates_alert_status_spec.rb'
- './spec/features/alerts_settings/user_views_alerts_settings_spec.rb'
- './spec/features/atom/dashboard_issues_spec.rb'
- './spec/features/atom/dashboard_spec.rb'
- './spec/features/atom/issues_spec.rb'
- './spec/features/atom/merge_requests_spec.rb'
- './spec/features/atom/users_spec.rb'
- './spec/features/boards/board_filters_spec.rb'
- './spec/features/boards/boards_spec.rb'
- './spec/features/boards/focus_mode_spec.rb'
- './spec/features/boards/issue_ordering_spec.rb'
- './spec/features/boards/keyboard_shortcut_spec.rb'
- './spec/features/boards/multiple_boards_spec.rb'
- './spec/features/boards/multi_select_spec.rb'
- './spec/features/boards/new_issue_spec.rb'
- './spec/features/boards/reload_boards_on_browser_back_spec.rb'
- './spec/features/boards/sidebar_assignee_spec.rb'
- './spec/features/boards/sidebar_labels_in_namespaces_spec.rb'
- './spec/features/boards/sidebar_labels_spec.rb'
- './spec/features/boards/sidebar_spec.rb'
- './spec/features/boards/user_adds_lists_to_board_spec.rb'
- './spec/features/boards/user_visits_board_spec.rb'
- './spec/features/breadcrumbs_schema_markup_spec.rb'
- './spec/features/broadcast_messages_spec.rb'
- './spec/features/calendar_spec.rb'
- './spec/features/callouts/registration_enabled_spec.rb'
- './spec/features/canonical_link_spec.rb'
- './spec/features/clusters/cluster_detail_page_spec.rb'
- './spec/features/clusters/create_agent_spec.rb'
- './spec/features/commit_spec.rb'
- './spec/features/commits_spec.rb'
- './spec/features/commits/user_uses_quick_actions_spec.rb'
- './spec/features/commits/user_view_commits_spec.rb'
- './spec/features/cycle_analytics_spec.rb'
- './spec/features/dashboard/activity_spec.rb'
- './spec/features/dashboard/archived_projects_spec.rb'
- './spec/features/dashboard/datetime_on_tooltips_spec.rb'
- './spec/features/dashboard/groups_list_spec.rb'
- './spec/features/dashboard/group_spec.rb'
- './spec/features/dashboard/issuables_counter_spec.rb'
- './spec/features/dashboard/issues_filter_spec.rb'
- './spec/features/dashboard/issues_spec.rb'
- './spec/features/dashboard/merge_requests_spec.rb'
- './spec/features/dashboard/milestones_spec.rb'
- './spec/features/dashboard/project_member_activity_index_spec.rb'
- './spec/features/dashboard/projects_spec.rb'
- './spec/features/dashboard/root_explore_spec.rb'
- './spec/features/dashboard/shortcuts_spec.rb'
- './spec/features/dashboard/snippets_spec.rb'
- './spec/features/dashboard/todos/target_state_spec.rb'
- './spec/features/dashboard/todos/todos_filtering_spec.rb'
- './spec/features/dashboard/todos/todos_sorting_spec.rb'
- './spec/features/dashboard/todos/todos_spec.rb'
- './spec/features/dashboard/user_filters_projects_spec.rb'
- './spec/features/discussion_comments/commit_spec.rb'
- './spec/features/discussion_comments/issue_spec.rb'
- './spec/features/discussion_comments/merge_request_spec.rb'
- './spec/features/discussion_comments/snippets_spec.rb'
- './spec/features/display_system_header_and_footer_bar_spec.rb'
- './spec/features/error_pages_spec.rb'
- './spec/features/error_tracking/user_filters_errors_by_status_spec.rb'
- './spec/features/error_tracking/user_searches_sentry_errors_spec.rb'
- './spec/features/error_tracking/user_sees_error_details_spec.rb'
- './spec/features/error_tracking/user_sees_error_index_spec.rb'
- './spec/features/expand_collapse_diffs_spec.rb'
- './spec/features/explore/groups_list_spec.rb'
- './spec/features/explore/groups_spec.rb'
- './spec/features/explore/topics_spec.rb'
- './spec/features/explore/user_explores_projects_spec.rb'
- './spec/features/file_uploads/attachment_spec.rb'
- './spec/features/file_uploads/ci_artifact_spec.rb'
- './spec/features/file_uploads/git_lfs_spec.rb'
- './spec/features/file_uploads/graphql_add_design_spec.rb'
- './spec/features/file_uploads/group_import_spec.rb'
- './spec/features/file_uploads/maven_package_spec.rb'
- './spec/features/file_uploads/multipart_invalid_uploads_spec.rb'
- './spec/features/file_uploads/nuget_package_spec.rb'
- './spec/features/file_uploads/project_import_spec.rb'
- './spec/features/file_uploads/rubygem_package_spec.rb'
- './spec/features/file_uploads/user_avatar_spec.rb'
- './spec/features/frequently_visited_projects_and_groups_spec.rb'
- './spec/features/gitlab_experiments_spec.rb'
- './spec/features/global_search_spec.rb'
- './spec/features/graphql_known_operations_spec.rb'
- './spec/features/groups/activity_spec.rb'
- './spec/features/groups/board_sidebar_spec.rb'
- './spec/features/groups/board_spec.rb'
- './spec/features/groups/clusters/user_spec.rb'
- './spec/features/groups/container_registry_spec.rb'
- './spec/features/groups/crm/contacts/create_spec.rb'
- './spec/features/groups/dependency_proxy_for_containers_spec.rb'
- './spec/features/groups/dependency_proxy_spec.rb'
- './spec/features/groups/empty_states_spec.rb'
- './spec/features/groups/group_page_with_external_authorization_service_spec.rb'
- './spec/features/groups/group_settings_spec.rb'
- './spec/features/groups/import_export/connect_instance_spec.rb'
- './spec/features/groups/import_export/export_file_spec.rb'
- './spec/features/groups/import_export/import_file_spec.rb'
- './spec/features/groups/import_export/migration_history_spec.rb'
- './spec/features/groups/integrations/group_integrations_spec.rb'
- './spec/features/groups/integrations/user_activates_mattermost_slash_command_spec.rb'
- './spec/features/groups/issues_spec.rb'
- './spec/features/groups/labels/create_spec.rb'
- './spec/features/groups/labels/edit_spec.rb'
- './spec/features/groups/labels/index_spec.rb'
- './spec/features/groups/labels/search_labels_spec.rb'
- './spec/features/groups/labels/sort_labels_spec.rb'
- './spec/features/groups/labels/subscription_spec.rb'
- './spec/features/groups/labels/user_sees_links_to_issuables_spec.rb'
- './spec/features/groups/members/filter_members_spec.rb'
- './spec/features/groups/members/leave_group_spec.rb'
- './spec/features/groups/members/list_members_spec.rb'
- './spec/features/groups/members/manage_groups_spec.rb'
- './spec/features/groups/members/manage_members_spec.rb'
- './spec/features/groups/members/master_adds_member_with_expiration_date_spec.rb'
- './spec/features/groups/members/master_manages_access_requests_spec.rb'
- './spec/features/groups/members/request_access_spec.rb'
- './spec/features/groups/members/search_members_spec.rb'
- './spec/features/groups/members/sort_members_spec.rb'
- './spec/features/groups/members/tabs_spec.rb'
- './spec/features/groups/merge_requests_spec.rb'
- './spec/features/groups/milestones/gfm_autocomplete_spec.rb'
- './spec/features/groups/milestone_spec.rb'
- './spec/features/groups/milestones_sorting_spec.rb'
- './spec/features/groups/navbar_spec.rb'
- './spec/features/groups/packages_spec.rb'
- './spec/features/groups/settings/access_tokens_spec.rb'
- './spec/features/groups/settings/ci_cd_spec.rb'
- './spec/features/groups/settings/group_badges_spec.rb'
- './spec/features/groups/settings/manage_applications_spec.rb'
- './spec/features/groups/settings/packages_and_registries_spec.rb'
- './spec/features/groups/settings/repository_spec.rb'
- './spec/features/groups/settings/user_searches_in_settings_spec.rb'
- './spec/features/groups/share_lock_spec.rb'
- './spec/features/groups/show_spec.rb'
- './spec/features/groups_spec.rb'
- './spec/features/groups/user_sees_package_sidebar_spec.rb'
- './spec/features/groups/user_sees_users_dropdowns_in_issuables_list_spec.rb'
- './spec/features/group_variables_spec.rb'
- './spec/features/help_dropdown_spec.rb'
- './spec/features/help_pages_spec.rb'
- './spec/features/ics/dashboard_issues_spec.rb'
- './spec/features/ics/group_issues_spec.rb'
- './spec/features/ics/project_issues_spec.rb'
- './spec/features/ide_spec.rb'
- './spec/features/ide/static_object_external_storage_csp_spec.rb'
- './spec/features/ide/user_opens_merge_request_spec.rb'
- './spec/features/import/manifest_import_spec.rb'
- './spec/features/issuables/issuable_list_spec.rb'
- './spec/features/issuables/issuable_templates_spec.rb'
- './spec/features/issuables/markdown_references/internal_references_spec.rb'
- './spec/features/issuables/markdown_references/jira_spec.rb'
- './spec/features/issuables/shortcuts_issuable_spec.rb'
- './spec/features/issuables/sorting_list_spec.rb'
- './spec/features/issuables/task_lists_spec.rb'
- './spec/features/issuables/user_sees_sidebar_spec.rb'
- './spec/features/issue_rebalancing_spec.rb'
- './spec/features/issues/action_cable_logging_spec.rb'
- './spec/features/issues/create_issue_for_discussions_in_merge_request_spec.rb'
- './spec/features/issues/create_issue_for_single_discussion_in_merge_request_spec.rb'
- './spec/features/issues/design_management/user_links_to_designs_in_issue_spec.rb'
- './spec/features/issues/design_management/user_paginates_designs_spec.rb'
- './spec/features/issues/design_management/user_permissions_upload_spec.rb'
- './spec/features/issues/design_management/user_uploads_designs_spec.rb'
- './spec/features/issues/design_management/user_views_design_images_spec.rb'
- './spec/features/issues/design_management/user_views_design_spec.rb'
- './spec/features/issues/design_management/user_views_designs_spec.rb'
- './spec/features/issues/design_management/user_views_designs_with_svg_xss_spec.rb'
- './spec/features/issues/discussion_lock_spec.rb'
- './spec/features/issues/email_participants_spec.rb'
- './spec/features/issues/gfm_autocomplete_spec.rb'
- './spec/features/issues/group_label_sidebar_spec.rb'
- './spec/features/issues/issue_detail_spec.rb'
- './spec/features/issues/issue_header_spec.rb'
- './spec/features/issues/issue_sidebar_spec.rb'
- './spec/features/issues/issue_state_spec.rb'
- './spec/features/issues/keyboard_shortcut_spec.rb'
- './spec/features/issues/labels_hierarchy_spec.rb'
- './spec/features/issues/list/csv_spec.rb'
- './spec/features/issues/list/filtered_search/dropdown_assignee_spec.rb'
- './spec/features/issues/list/filtered_search/dropdown_author_spec.rb'
- './spec/features/issues/list/filtered_search/dropdown_base_spec.rb'
- './spec/features/issues/list/filtered_search/dropdown_emoji_spec.rb'
- './spec/features/issues/list/filtered_search/dropdown_hint_spec.rb'
- './spec/features/issues/list/filtered_search/dropdown_label_spec.rb'
- './spec/features/issues/list/filtered_search/dropdown_milestone_spec.rb'
- './spec/features/issues/list/filtered_search/dropdown_release_spec.rb'
- './spec/features/issues/list/filtered_search/filter_issues_spec.rb'
- './spec/features/issues/list/filtered_search/recent_searches_spec.rb'
- './spec/features/issues/list/filtered_search/search_bar_spec.rb'
- './spec/features/issues/list/filtered_search/visual_tokens_spec.rb'
- './spec/features/issues/list/rss_spec.rb'
- './spec/features/issues/list/user_bulk_edits_issues_labels_spec.rb'
- './spec/features/issues/list/user_bulk_edits_issues_spec.rb'
- './spec/features/issues/list/user_creates_issue_by_email_spec.rb'
- './spec/features/issues/list/user_filters_issues_spec.rb'
- './spec/features/issues/list/user_sees_empty_state_spec.rb'
- './spec/features/issues/list/user_sorts_issues_spec.rb'
- './spec/features/issues/list/user_views_issues_spec.rb'
- './spec/features/issues/markdown_toolbar_spec.rb'
- './spec/features/issues/move_spec.rb'
- './spec/features/issues/new/form_spec.rb'
- './spec/features/issues/new/spam_akismet_issue_creation_spec.rb'
- './spec/features/issues/new/user_creates_issue_spec.rb'
- './spec/features/issues/note_polling_spec.rb'
- './spec/features/issues/notes_on_issues_spec.rb'
- './spec/features/issues/related_issues_spec.rb'
- './spec/features/issues/resource_label_events_spec.rb'
- './spec/features/issues/service_desk_spec.rb'
- './spec/features/issues/todo_spec.rb'
- './spec/features/issues/user_comments_on_issue_spec.rb'
- './spec/features/issues/user_creates_branch_and_merge_request_spec.rb'
- './spec/features/issues/user_creates_confidential_merge_request_spec.rb'
- './spec/features/issues/user_edits_issue_spec.rb'
- './spec/features/issues/user_interacts_with_awards_spec.rb'
- './spec/features/issues/user_resets_their_incoming_email_token_spec.rb'
- './spec/features/issues/user_scrolls_to_deeplinked_note_spec.rb'
- './spec/features/issues/user_sees_breadcrumb_links_spec.rb'
- './spec/features/issues/user_sees_live_update_spec.rb'
- './spec/features/issues/user_sees_sidebar_updates_in_realtime_spec.rb'
- './spec/features/issues/user_sorts_issue_comments_spec.rb'
- './spec/features/issues/user_toggles_subscription_spec.rb'
- './spec/features/issues/user_uploads_file_to_note_spec.rb'
- './spec/features/issues/user_uses_quick_actions_spec.rb'
- './spec/features/issues/user_views_issue_spec.rb'
- './spec/features/issues/viewing_issues_with_external_authorization_enabled_spec.rb'
- './spec/features/issues/viewing_relocated_issues_spec.rb'
- './spec/features/jira_connect/branches_spec.rb'
- './spec/features/jira_connect/subscriptions_spec.rb'
- './spec/features/markdown/copy_as_gfm_spec.rb'
- './spec/features/markdown/gitlab_flavored_markdown_spec.rb'
- './spec/features/markdown/json_table_spec.rb'
- './spec/features/markdown/keyboard_shortcuts_spec.rb'
- './spec/features/markdown/kroki_spec.rb'
- './spec/features/markdown/markdown_spec.rb'
- './spec/features/markdown/math_spec.rb'
- './spec/features/markdown/sandboxed_mermaid_spec.rb'
- './spec/features/merge_request/batch_comments_spec.rb'
- './spec/features/merge_request/close_reopen_report_toggle_spec.rb'
- './spec/features/merge_request/maintainer_edits_fork_spec.rb'
- './spec/features/merge_request/merge_request_discussion_lock_spec.rb'
- './spec/features/merge_requests/filters_generic_behavior_spec.rb'
- './spec/features/merge_requests/rss_spec.rb'
- './spec/features/merge_requests/user_exports_as_csv_spec.rb'
- './spec/features/merge_requests/user_filters_by_approvals_spec.rb'
- './spec/features/merge_requests/user_filters_by_assignees_spec.rb'
- './spec/features/merge_requests/user_filters_by_deployments_spec.rb'
- './spec/features/merge_requests/user_filters_by_draft_spec.rb'
- './spec/features/merge_requests/user_filters_by_labels_spec.rb'
- './spec/features/merge_requests/user_filters_by_milestones_spec.rb'
- './spec/features/merge_requests/user_filters_by_multiple_criteria_spec.rb'
- './spec/features/merge_requests/user_filters_by_target_branch_spec.rb'
- './spec/features/merge_requests/user_lists_merge_requests_spec.rb'
- './spec/features/merge_requests/user_mass_updates_spec.rb'
- './spec/features/merge_requests/user_sees_empty_state_spec.rb'
- './spec/features/merge_requests/user_sorts_merge_requests_spec.rb'
- './spec/features/merge_requests/user_views_all_merge_requests_spec.rb'
- './spec/features/merge_requests/user_views_closed_merge_requests_spec.rb'
- './spec/features/merge_requests/user_views_merged_merge_requests_spec.rb'
- './spec/features/merge_requests/user_views_open_merge_requests_spec.rb'
- './spec/features/merge_request/user_accepts_merge_request_spec.rb'
- './spec/features/merge_request/user_allows_commits_from_memebers_who_can_merge_spec.rb'
- './spec/features/merge_request/user_approves_spec.rb'
- './spec/features/merge_request/user_assigns_themselves_spec.rb'
- './spec/features/merge_request/user_awards_emoji_spec.rb'
- './spec/features/merge_request/user_clicks_merge_request_tabs_spec.rb'
- './spec/features/merge_request/user_closes_reopens_merge_request_state_spec.rb'
- './spec/features/merge_request/user_comments_on_commit_spec.rb'
- './spec/features/merge_request/user_comments_on_diff_spec.rb'
- './spec/features/merge_request/user_comments_on_merge_request_spec.rb'
- './spec/features/merge_request/user_creates_image_diff_notes_spec.rb'
- './spec/features/merge_request/user_creates_merge_request_spec.rb'
- './spec/features/merge_request/user_creates_mr_spec.rb'
- './spec/features/merge_request/user_customizes_merge_commit_message_spec.rb'
- './spec/features/merge_request/user_edits_assignees_sidebar_spec.rb'
- './spec/features/merge_request/user_edits_merge_request_spec.rb'
- './spec/features/merge_request/user_edits_mr_spec.rb'
- './spec/features/merge_request/user_edits_reviewers_sidebar_spec.rb'
- './spec/features/merge_request/user_expands_diff_spec.rb'
- './spec/features/merge_request/user_interacts_with_batched_mr_diffs_spec.rb'
- './spec/features/merge_request/user_locks_discussion_spec.rb'
- './spec/features/merge_request/user_manages_subscription_spec.rb'
- './spec/features/merge_request/user_marks_merge_request_as_draft_spec.rb'
- './spec/features/merge_request/user_merges_immediately_spec.rb'
- './spec/features/merge_request/user_merges_merge_request_spec.rb'
- './spec/features/merge_request/user_merges_only_if_pipeline_succeeds_spec.rb'
- './spec/features/merge_request/user_opens_checkout_branch_modal_spec.rb'
- './spec/features/merge_request/user_opens_context_commits_modal_spec.rb'
- './spec/features/merge_request/user_posts_diff_notes_spec.rb'
- './spec/features/merge_request/user_posts_notes_spec.rb'
- './spec/features/merge_request/user_rebases_merge_request_spec.rb'
- './spec/features/merge_request/user_resolves_conflicts_spec.rb'
- './spec/features/merge_request/user_resolves_diff_notes_and_discussions_resolve_spec.rb'
- './spec/features/merge_request/user_resolves_outdated_diff_discussions_spec.rb'
- './spec/features/merge_request/user_resolves_wip_mr_spec.rb'
- './spec/features/merge_request/user_reverts_merge_request_spec.rb'
- './spec/features/merge_request/user_reviews_image_spec.rb'
- './spec/features/merge_request/user_scrolls_to_note_on_load_spec.rb'
- './spec/features/merge_request/user_sees_avatar_on_diff_notes_spec.rb'
- './spec/features/merge_request/user_sees_breadcrumb_links_spec.rb'
- './spec/features/merge_request/user_sees_check_out_branch_modal_spec.rb'
- './spec/features/merge_request/user_sees_cherry_pick_modal_spec.rb'
- './spec/features/merge_request/user_sees_closing_issues_message_spec.rb'
- './spec/features/merge_request/user_sees_deleted_target_branch_spec.rb'
- './spec/features/merge_request/user_sees_deployment_widget_spec.rb'
- './spec/features/merge_request/user_sees_diff_spec.rb'
- './spec/features/merge_request/user_sees_discussions_spec.rb'
- './spec/features/merge_request/user_sees_merge_button_depending_on_unresolved_discussions_spec.rb'
- './spec/features/merge_request/user_sees_merge_request_pipelines_spec.rb'
- './spec/features/merge_request/user_sees_merge_widget_spec.rb'
- './spec/features/merge_request/user_sees_mini_pipeline_graph_spec.rb'
- './spec/features/merge_request/user_sees_mr_from_deleted_forked_project_spec.rb'
- './spec/features/merge_request/user_sees_mr_with_deleted_source_branch_spec.rb'
- './spec/features/merge_request/user_sees_notes_from_forked_project_spec.rb'
- './spec/features/merge_request/user_sees_page_metadata_spec.rb'
- './spec/features/merge_request/user_sees_pipelines_from_forked_project_spec.rb'
- './spec/features/merge_request/user_sees_pipelines_spec.rb'
- './spec/features/merge_request/user_sees_suggest_pipeline_spec.rb'
- './spec/features/merge_request/user_sees_system_notes_spec.rb'
- './spec/features/merge_request/user_sees_versions_spec.rb'
- './spec/features/merge_request/user_selects_branches_for_new_mr_spec.rb'
- './spec/features/merge_request/user_squashes_merge_request_spec.rb'
- './spec/features/merge_request/user_suggests_changes_on_diff_spec.rb'
- './spec/features/merge_request/user_toggles_whitespace_changes_spec.rb'
- './spec/features/merge_request/user_tries_to_access_private_project_info_through_new_mr_spec.rb'
- './spec/features/merge_request/user_uses_quick_actions_spec.rb'
- './spec/features/merge_request/user_views_auto_expanding_diff_spec.rb'
- './spec/features/merge_request/user_views_diffs_commit_spec.rb'
- './spec/features/merge_request/user_views_diffs_file_by_file_spec.rb'
- './spec/features/merge_request/user_views_diffs_spec.rb'
- './spec/features/merge_request/user_views_merge_request_from_deleted_fork_spec.rb'
- './spec/features/merge_request/user_views_open_merge_request_spec.rb'
- './spec/features/milestone_spec.rb'
- './spec/features/milestones/user_creates_milestone_spec.rb'
- './spec/features/milestones/user_deletes_milestone_spec.rb'
- './spec/features/milestones/user_edits_milestone_spec.rb'
- './spec/features/milestones/user_promotes_milestone_spec.rb'
- './spec/features/milestones/user_sees_breadcrumb_links_spec.rb'
- './spec/features/milestones/user_views_milestone_spec.rb'
- './spec/features/milestones/user_views_milestones_spec.rb'
- './spec/features/monitor_sidebar_link_spec.rb'
- './spec/features/oauth_login_spec.rb'
- './spec/features/oauth_provider_authorize_spec.rb'
- './spec/features/one_trust_spec.rb'
- './spec/features/participants_autocomplete_spec.rb'
- './spec/features/password_reset_spec.rb'
- './spec/features/populate_new_pipeline_vars_with_params_spec.rb'
- './spec/features/profiles/account_spec.rb'
- './spec/features/user_settings/active_sessions_spec.rb'
- './spec/features/profiles/chat_names_spec.rb'
- './spec/features/profiles/emails_spec.rb'
- './spec/features/profiles/gpg_keys_spec.rb'
- './spec/features/profiles/oauth_applications_spec.rb'
- './spec/features/profile_spec.rb'
- './spec/features/profiles/two_factor_auths_spec.rb'
- './spec/features/profiles/user_changes_notified_of_own_activity_spec.rb'
- './spec/features/profiles/user_edit_preferences_spec.rb'
- './spec/features/profiles/user_manages_applications_spec.rb'
- './spec/features/profiles/user_manages_emails_spec.rb'
- './spec/features/profiles/user_search_settings_spec.rb'
- './spec/features/profiles/user_visits_notifications_tab_spec.rb'
- './spec/features/profiles/user_visits_profile_authentication_log_spec.rb'
- './spec/features/profiles/user_visits_profile_preferences_page_spec.rb'
- './spec/features/profiles/user_visits_profile_spec.rb'
- './spec/features/project_group_variables_spec.rb'
- './spec/features/projects/active_tabs_spec.rb'
- './spec/features/projects/activity/rss_spec.rb'
- './spec/features/projects/activity/user_sees_activity_spec.rb'
- './spec/features/projects/activity/user_sees_design_activity_spec.rb'
- './spec/features/projects/activity/user_sees_design_comment_spec.rb'
- './spec/features/projects/activity/user_sees_private_activity_spec.rb'
- './spec/features/projects/artifacts/file_spec.rb'
- './spec/features/projects/artifacts/raw_spec.rb'
- './spec/features/projects/artifacts/user_browses_artifacts_spec.rb'
- './spec/features/projects/artifacts/user_downloads_artifacts_spec.rb'
- './spec/features/projects/badges/coverage_spec.rb'
- './spec/features/projects/badges/list_spec.rb'
- './spec/features/projects/badges/pipeline_badge_spec.rb'
- './spec/features/projects/blobs/blame_spec.rb'
- './spec/features/projects/blobs/blob_show_spec.rb'
- './spec/features/projects/blobs/edit_spec.rb'
- './spec/features/projects/blobs/user_views_pipeline_editor_button_spec.rb'
- './spec/features/projects/branches/download_buttons_spec.rb'
- './spec/features/projects/branches/new_branch_ref_dropdown_spec.rb'
- './spec/features/projects/branches_spec.rb'
- './spec/features/projects/branches/user_creates_branch_spec.rb'
- './spec/features/projects/branches/user_deletes_branch_spec.rb'
- './spec/features/projects/branches/user_views_branches_spec.rb'
- './spec/features/projects/ci/editor_spec.rb'
- './spec/features/projects/ci/lint_spec.rb'
- './spec/features/projects/classification_label_on_project_pages_spec.rb'
- './spec/features/projects/cluster_agents_spec.rb'
- './spec/features/projects/clusters/gcp_spec.rb'
- './spec/features/projects/clusters_spec.rb'
- './spec/features/projects/clusters/user_spec.rb'
- './spec/features/projects/commit/builds_spec.rb'
- './spec/features/projects/commit/cherry_pick_spec.rb'
- './spec/features/projects/commit/comments/user_adds_comment_spec.rb'
- './spec/features/projects/commit/comments/user_deletes_comments_spec.rb'
- './spec/features/projects/commit/comments/user_edits_comments_spec.rb'
- './spec/features/projects/commit/diff_notes_spec.rb'
- './spec/features/projects/commit/mini_pipeline_graph_spec.rb'
- './spec/features/projects/commits/multi_view_diff_spec.rb'
- './spec/features/projects/commits/rss_spec.rb'
- './spec/features/projects/commits/user_browses_commits_spec.rb'
- './spec/features/projects/commit/user_comments_on_commit_spec.rb'
- './spec/features/projects/commit/user_reverts_commit_spec.rb'
- './spec/features/projects/commit/user_views_user_status_on_commit_spec.rb'
- './spec/features/projects/compare_spec.rb'
- './spec/features/projects/confluence/user_views_confluence_page_spec.rb'
- './spec/features/projects/container_registry_spec.rb'
- './spec/features/projects/deploy_keys_spec.rb'
- './spec/features/projects/diffs/diff_show_spec.rb'
- './spec/features/projects/environments/environment_spec.rb'
- './spec/features/projects/environments/environments_spec.rb'
- './spec/features/projects/feature_flags/user_creates_feature_flag_spec.rb'
- './spec/features/projects/feature_flags/user_deletes_feature_flag_spec.rb'
- './spec/features/projects/feature_flags/user_sees_feature_flag_list_spec.rb'
- './spec/features/projects/feature_flags/user_updates_feature_flag_spec.rb'
- './spec/features/projects/feature_flag_user_lists/user_deletes_feature_flag_user_list_spec.rb'
- './spec/features/projects/feature_flag_user_lists/user_edits_feature_flag_user_list_spec.rb'
- './spec/features/projects/feature_flag_user_lists/user_sees_feature_flag_user_list_details_spec.rb'
- './spec/features/projects/features_visibility_spec.rb'
- './spec/features/projects/files/dockerfile_dropdown_spec.rb'
- './spec/features/projects/files/download_buttons_spec.rb'
- './spec/features/projects/files/edit_file_soft_wrap_spec.rb'
- './spec/features/projects/files/editing_a_file_spec.rb'
- './spec/features/projects/files/files_sort_submodules_with_folders_spec.rb'
- './spec/features/projects/files/find_file_keyboard_spec.rb'
- './spec/features/projects/files/gitignore_dropdown_spec.rb'
- './spec/features/projects/files/gitlab_ci_yml_dropdown_spec.rb'
- './spec/features/projects/files/project_owner_creates_license_file_spec.rb'
- './spec/features/projects/files/project_owner_sees_link_to_create_license_file_in_empty_project_spec.rb'
- './spec/features/projects/files/template_selector_menu_spec.rb'
- './spec/features/projects/files/undo_template_spec.rb'
- './spec/features/projects/files/user_browses_a_tree_with_a_folder_containing_only_a_folder_spec.rb'
- './spec/features/projects/files/user_browses_files_spec.rb'
- './spec/features/projects/files/user_browses_lfs_files_spec.rb'
- './spec/features/projects/files/user_creates_directory_spec.rb'
- './spec/features/projects/files/user_creates_files_spec.rb'
- './spec/features/projects/files/user_deletes_files_spec.rb'
- './spec/features/projects/files/user_edits_files_spec.rb'
- './spec/features/projects/files/user_find_file_spec.rb'
- './spec/features/projects/files/user_reads_pipeline_status_spec.rb'
- './spec/features/projects/files/user_replaces_files_spec.rb'
- './spec/features/projects/files/user_searches_for_files_spec.rb'
- './spec/features/projects/files/user_uploads_files_spec.rb'
- './spec/features/projects/forks/fork_list_spec.rb'
- './spec/features/projects/fork_spec.rb'
- './spec/features/projects/graph_spec.rb'
- './spec/features/projects/hook_logs/user_reads_log_spec.rb'
- './spec/features/projects/import_export/export_file_spec.rb'
- './spec/features/projects/import_export/import_file_spec.rb'
- './spec/features/projects/infrastructure_registry_spec.rb'
- './spec/features/projects/integrations/disable_triggers_spec.rb'
- './spec/features/projects/integrations/project_integrations_spec.rb'
- './spec/features/projects/integrations/user_activates_asana_spec.rb'
- './spec/features/projects/integrations/user_activates_assembla_spec.rb'
- './spec/features/projects/integrations/user_activates_atlassian_bamboo_ci_spec.rb'
- './spec/features/projects/integrations/user_activates_emails_on_push_spec.rb'
- './spec/features/projects/integrations/user_activates_irker_spec.rb'
- './spec/features/projects/integrations/user_activates_issue_tracker_spec.rb'
- './spec/features/projects/integrations/user_activates_jetbrains_teamcity_ci_spec.rb'
- './spec/features/projects/integrations/user_activates_jira_spec.rb'
- './spec/features/projects/integrations/user_activates_mattermost_slash_command_spec.rb'
- './spec/features/projects/integrations/user_activates_packagist_spec.rb'
- './spec/features/projects/integrations/user_activates_pivotaltracker_spec.rb'
- './spec/features/projects/integrations/user_activates_pushover_spec.rb'
- './spec/features/projects/integrations/user_activates_slack_notifications_spec.rb'
- './spec/features/projects/integrations/user_activates_slack_slash_command_spec.rb'
- './spec/features/projects/integrations/user_uses_inherited_settings_spec.rb'
- './spec/features/projects/integrations/user_views_services_spec.rb'
- './spec/features/projects/jobs/permissions_spec.rb'
- './spec/features/projects/jobs_spec.rb'
- './spec/features/projects/jobs/user_browses_job_spec.rb'
- './spec/features/projects/jobs/user_browses_jobs_spec.rb'
- './spec/features/projects/jobs/user_triggers_manual_job_with_variables_spec.rb'
- './spec/features/projects/labels/issues_sorted_by_priority_spec.rb'
- './spec/features/projects/labels/search_labels_spec.rb'
- './spec/features/projects/labels/sort_labels_spec.rb'
- './spec/features/projects/labels/subscription_spec.rb'
- './spec/features/projects/labels/update_prioritization_spec.rb'
- './spec/features/projects/labels/user_creates_labels_spec.rb'
- './spec/features/projects/labels/user_edits_labels_spec.rb'
- './spec/features/projects/labels/user_promotes_label_spec.rb'
- './spec/features/projects/labels/user_removes_labels_spec.rb'
- './spec/features/projects/labels/user_sees_breadcrumb_links_spec.rb'
- './spec/features/projects/labels/user_sees_links_to_issuables_spec.rb'
- './spec/features/projects/labels/user_views_labels_spec.rb'
- './spec/features/projects/members/anonymous_user_sees_members_spec.rb'
- './spec/features/projects/members/group_member_cannot_leave_group_project_spec.rb'
- './spec/features/projects/members/group_member_cannot_request_access_to_his_group_project_spec.rb'
- './spec/features/projects/members/group_members_spec.rb'
- './spec/features/projects/members/group_requester_cannot_request_access_to_project_spec.rb'
- './spec/features/projects/members/groups_with_access_list_spec.rb'
- './spec/features/projects/members/manage_groups_spec.rb'
- './spec/features/projects/members/manage_members_spec.rb'
- './spec/features/projects/members/master_adds_member_with_expiration_date_spec.rb'
- './spec/features/projects/members/master_manages_access_requests_spec.rb'
- './spec/features/projects/members/member_cannot_request_access_to_his_project_spec.rb'
- './spec/features/projects/members/member_leaves_project_spec.rb'
- './spec/features/projects/members/owner_cannot_leave_project_spec.rb'
- './spec/features/projects/members/owner_cannot_request_access_to_his_project_spec.rb'
- './spec/features/projects/members/sorting_spec.rb'
- './spec/features/projects/members/tabs_spec.rb'
- './spec/features/projects/members/user_requests_access_spec.rb'
- './spec/features/projects/merge_request_button_spec.rb'
- './spec/features/projects/milestones/gfm_autocomplete_spec.rb'
- './spec/features/projects/milestones/milestone_spec.rb'
- './spec/features/projects/milestones/milestones_sorting_spec.rb'
- './spec/features/projects/milestones/new_spec.rb'
- './spec/features/projects/milestones/user_interacts_with_labels_spec.rb'
- './spec/features/projects/navbar_spec.rb'
- './spec/features/projects/network_graph_spec.rb'
- './spec/features/projects/new_project_from_template_spec.rb'
- './spec/features/projects/new_project_spec.rb'
- './spec/features/projects/package_files_spec.rb'
- './spec/features/projects/packages_spec.rb'
- './spec/features/projects/pages/user_adds_domain_spec.rb'
- './spec/features/projects/pages/user_configures_pages_pipeline_spec.rb'
- './spec/features/projects/pages/user_edits_lets_encrypt_settings_spec.rb'
- './spec/features/projects/pages/user_edits_settings_spec.rb'
- './spec/features/projects/pipeline_schedules_spec.rb'
- './spec/features/projects/pipelines/pipeline_spec.rb'
- './spec/features/projects/pipelines/pipelines_spec.rb'
- './spec/features/projects/raw/user_interacts_with_raw_endpoint_spec.rb'
- './spec/features/projects/releases/user_creates_release_spec.rb'
- './spec/features/projects/releases/user_views_edit_release_spec.rb'
- './spec/features/projects/releases/user_views_release_spec.rb'
- './spec/features/projects/releases/user_views_releases_spec.rb'
- './spec/features/projects/remote_mirror_spec.rb'
- './spec/features/projects/settings/access_tokens_spec.rb'
- './spec/features/projects/settings/branch_rules_settings_spec.rb'
- './spec/features/projects/settings/external_authorization_service_settings_spec.rb'
- './spec/features/projects/settings/forked_project_settings_spec.rb'
- './spec/features/projects/settings/lfs_settings_spec.rb'
- './spec/features/projects/settings/monitor_settings_spec.rb'
- './spec/features/projects/settings/packages_settings_spec.rb'
- './spec/features/projects/settings/pipelines_settings_spec.rb'
- './spec/features/projects/settings/project_badges_spec.rb'
- './spec/features/projects/settings/project_settings_spec.rb'
- './spec/features/projects/settings/registry_settings_cleanup_tags_spec.rb'
- './spec/features/projects/settings/registry_settings_spec.rb'
- './spec/features/projects/settings/repository_settings_spec.rb'
- './spec/features/projects/settings/secure_files_spec.rb'
- './spec/features/projects/settings/user_archives_project_spec.rb'
- './spec/features/projects/settings/user_changes_avatar_spec.rb'
- './spec/features/projects/settings/user_changes_default_branch_spec.rb'
- './spec/features/projects/settings/user_interacts_with_deploy_keys_spec.rb'
- './spec/features/projects/settings/user_manages_merge_requests_settings_spec.rb'
- './spec/features/projects/members/user_manages_project_members_spec.rb'
- './spec/features/projects/settings/user_renames_a_project_spec.rb'
- './spec/features/projects/settings/user_searches_in_settings_spec.rb'
- './spec/features/projects/settings/user_sees_revoke_deploy_token_modal_spec.rb'
- './spec/features/projects/settings/user_tags_project_spec.rb'
- './spec/features/projects/settings/user_transfers_a_project_spec.rb'
- './spec/features/projects/settings/visibility_settings_spec.rb'
- './spec/features/projects/settings/webhooks_settings_spec.rb'
- './spec/features/projects/show/download_buttons_spec.rb'
- './spec/features/projects/show/no_password_spec.rb'
- './spec/features/projects/show/redirects_spec.rb'
- './spec/features/projects/show/rss_spec.rb'
- './spec/features/projects/show/schema_markup_spec.rb'
- './spec/features/projects/show/user_interacts_with_auto_devops_banner_spec.rb'
- './spec/features/projects/show/user_interacts_with_stars_spec.rb'
- './spec/features/projects/show/user_manages_notifications_spec.rb'
- './spec/features/projects/show/user_sees_collaboration_links_spec.rb'
- './spec/features/projects/show/user_sees_deletion_failure_message_spec.rb'
- './spec/features/projects/show/user_sees_git_instructions_spec.rb'
- './spec/features/projects/show/user_sees_last_commit_ci_status_spec.rb'
- './spec/features/projects/show/user_sees_readme_spec.rb'
- './spec/features/projects/show/user_sees_setup_shortcut_buttons_spec.rb'
- './spec/features/projects/show/user_uploads_files_spec.rb'
- './spec/features/projects/snippets/create_snippet_spec.rb'
- './spec/features/projects/snippets/user_comments_on_snippet_spec.rb'
- './spec/features/projects/snippets/user_deletes_snippet_spec.rb'
- './spec/features/projects/snippets/user_updates_snippet_spec.rb'
- './spec/features/projects/snippets/user_views_snippets_spec.rb'
- './spec/features/projects/sourcegraph_csp_spec.rb'
- './spec/features/projects_spec.rb'
- './spec/features/projects/sub_group_issuables_spec.rb'
- './spec/features/projects/tags/download_buttons_spec.rb'
- './spec/features/projects/tags/user_edits_tags_spec.rb'
- './spec/features/projects/tags/user_views_tag_spec.rb'
- './spec/features/projects/tags/user_views_tags_spec.rb'
- './spec/features/projects/terraform_spec.rb'
- './spec/features/projects/tree/rss_spec.rb'
- './spec/features/projects/tree/tree_show_spec.rb'
- './spec/features/projects/user_changes_project_visibility_spec.rb'
- './spec/features/projects/user_creates_project_spec.rb'
- './spec/features/projects/user_sees_sidebar_spec.rb'
- './spec/features/projects/user_sees_user_popover_spec.rb'
- './spec/features/projects/user_sorts_projects_spec.rb'
- './spec/features/projects/user_uses_shortcuts_spec.rb'
- './spec/features/projects/user_views_empty_project_spec.rb'
- './spec/features/projects/view_on_env_spec.rb'
- './spec/features/projects/wikis_spec.rb'
- './spec/features/projects/wiki/user_views_wiki_empty_spec.rb'
- './spec/features/projects/wiki/user_views_wiki_in_project_page_spec.rb'
- './spec/features/project_variables_spec.rb'
- './spec/features/promotion_spec.rb'
- './spec/features/protected_branches_spec.rb'
- './spec/features/protected_tags_spec.rb'
- './spec/features/read_only_spec.rb'
- './spec/features/reportable_note/commit_spec.rb'
- './spec/features/reportable_note/issue_spec.rb'
- './spec/features/reportable_note/merge_request_spec.rb'
- './spec/features/reportable_note/snippets_spec.rb'
- './spec/features/search/user_searches_for_code_spec.rb'
- './spec/features/search/user_searches_for_comments_spec.rb'
- './spec/features/search/user_searches_for_commits_spec.rb'
- './spec/features/search/user_searches_for_issues_spec.rb'
- './spec/features/search/user_searches_for_merge_requests_spec.rb'
- './spec/features/search/user_searches_for_milestones_spec.rb'
- './spec/features/search/user_searches_for_projects_spec.rb'
- './spec/features/search/user_searches_for_users_spec.rb'
- './spec/features/search/user_searches_for_wiki_pages_spec.rb'
- './spec/features/search/user_uses_search_filters_spec.rb'
- './spec/features/security/admin_access_spec.rb'
- './spec/features/security/dashboard_access_spec.rb'
- './spec/features/security/group/internal_access_spec.rb'
- './spec/features/security/group/private_access_spec.rb'
- './spec/features/security/group/public_access_spec.rb'
- './spec/features/security/profile_access_spec.rb'
- './spec/features/security/project/internal_access_spec.rb'
- './spec/features/security/project/private_access_spec.rb'
- './spec/features/security/project/public_access_spec.rb'
- './spec/features/security/project/snippet/internal_access_spec.rb'
- './spec/features/security/project/snippet/private_access_spec.rb'
- './spec/features/security/project/snippet/public_access_spec.rb'
- './spec/features/sentry_js_spec.rb'
- './spec/features/signed_commits_spec.rb'
- './spec/features/snippets/embedded_snippet_spec.rb'
- './spec/features/snippets/explore_spec.rb'
- './spec/features/snippets/internal_snippet_spec.rb'
- './spec/features/snippets/notes_on_personal_snippets_spec.rb'
- './spec/features/snippets/private_snippets_spec.rb'
- './spec/features/snippets/public_snippets_spec.rb'
- './spec/features/snippets/search_snippets_spec.rb'
- './spec/features/snippets/spam_snippets_spec.rb'
- './spec/features/snippets_spec.rb'
- './spec/features/snippets/user_creates_snippet_spec.rb'
- './spec/features/snippets/user_deletes_snippet_spec.rb'
- './spec/features/snippets/user_edits_snippet_spec.rb'
- './spec/features/snippets/user_snippets_spec.rb'
- './spec/features/tags/developer_creates_tag_spec.rb'
- './spec/features/tags/developer_deletes_tag_spec.rb'
- './spec/features/tags/developer_views_tags_spec.rb'
- './spec/features/tags/maintainer_deletes_protected_tag_spec.rb'
- './spec/features/topic_show_spec.rb'
- './spec/features/triggers_spec.rb'
- './spec/features/unsubscribe_links_spec.rb'
- './spec/features/uploads/user_uploads_avatar_to_group_spec.rb'
- './spec/features/uploads/user_uploads_avatar_to_profile_spec.rb'
- './spec/features/usage_stats_consent_spec.rb'
- './spec/features/user_can_display_performance_bar_spec.rb'
- './spec/features/user_opens_link_to_comment_spec.rb'
- './spec/features/users/active_sessions_spec.rb'
- './spec/features/users/add_email_to_existing_account_spec.rb'
- './spec/features/users/bizible_csp_spec.rb'
- './spec/features/users/confirmation_spec.rb'
- './spec/features/user_sees_revert_modal_spec.rb'
- './spec/features/users/email_verification_on_login_spec.rb'
- './spec/features/users/login_spec.rb'
- './spec/features/users/logout_spec.rb'
- './spec/features/users/one_trust_csp_spec.rb'
- './spec/features/user_sorts_things_spec.rb'
- './spec/features/users/overview_spec.rb'
- './spec/features/users/password_spec.rb'
- './spec/features/users/rss_spec.rb'
- './spec/features/users/show_spec.rb'
- './spec/features/users/signup_spec.rb'
- './spec/features/users/snippets_spec.rb'
- './spec/features/users/terms_spec.rb'
- './spec/features/users/user_browses_projects_on_user_page_spec.rb'
- './spec/features/webauthn_spec.rb'
- './spec/features/whats_new_spec.rb'
- './spec/finders/abuse_reports_finder_spec.rb'
- './spec/finders/access_requests_finder_spec.rb'
- './spec/finders/admin/plans_finder_spec.rb'
- './spec/finders/admin/projects_finder_spec.rb'
- './spec/finders/alert_management/alerts_finder_spec.rb'
- './spec/finders/alert_management/http_integrations_finder_spec.rb'
- './spec/finders/analytics/cycle_analytics/stage_finder_spec.rb'
- './spec/finders/applications_finder_spec.rb'
- './spec/finders/autocomplete/deploy_keys_with_write_access_finder_spec.rb'
- './spec/finders/autocomplete/group_finder_spec.rb'
- './spec/finders/autocomplete/move_to_project_finder_spec.rb'
- './spec/finders/autocomplete/project_finder_spec.rb'
- './spec/finders/autocomplete/routes_finder_spec.rb'
- './spec/finders/autocomplete/users_finder_spec.rb'
- './spec/finders/award_emojis_finder_spec.rb'
- './spec/finders/boards/boards_finder_spec.rb'
- './spec/finders/boards/visits_finder_spec.rb'
- './spec/finders/branches_finder_spec.rb'
- './spec/finders/bulk_imports/entities_finder_spec.rb'
- './spec/finders/bulk_imports/imports_finder_spec.rb'
- './spec/finders/ci/auth_job_finder_spec.rb'
- './spec/finders/ci/commit_statuses_finder_spec.rb'
- './spec/finders/ci/daily_build_group_report_results_finder_spec.rb'
- './spec/finders/ci/job_artifacts_finder_spec.rb'
- './spec/finders/ci/jobs_finder_spec.rb'
- './spec/finders/ci/pipeline_schedules_finder_spec.rb'
- './spec/finders/ci/pipelines_finder_spec.rb'
- './spec/finders/ci/pipelines_for_merge_request_finder_spec.rb'
- './spec/finders/ci/runner_jobs_finder_spec.rb'
- './spec/finders/ci/runners_finder_spec.rb'
- './spec/finders/ci/variables_finder_spec.rb'
- './spec/finders/cluster_ancestors_finder_spec.rb'
- './spec/finders/clusters/agents_finder_spec.rb'
- './spec/finders/clusters_finder_spec.rb'
- './spec/finders/clusters/knative_services_finder_spec.rb'
- './spec/finders/clusters/kubernetes_namespace_finder_spec.rb'
- './spec/finders/concerns/finder_methods_spec.rb'
- './spec/finders/concerns/finder_with_cross_project_access_spec.rb'
- './spec/finders/concerns/finder_with_group_hierarchy_spec.rb'
- './spec/finders/concerns/packages/finder_helper_spec.rb'
- './spec/finders/container_repositories_finder_spec.rb'
- './spec/finders/context_commits_finder_spec.rb'
- './spec/finders/contributed_projects_finder_spec.rb'
- './spec/finders/crm/contacts_finder_spec.rb'
- './spec/finders/crm/organizations_finder_spec.rb'
- './spec/finders/database/batched_background_migrations_finder_spec.rb'
- './spec/finders/deployments_finder_spec.rb'
- './spec/finders/deploy_tokens/tokens_finder_spec.rb'
- './spec/finders/design_management/designs_finder_spec.rb'
- './spec/finders/design_management/versions_finder_spec.rb'
- './spec/finders/environments/environment_names_finder_spec.rb'
- './spec/finders/environments/environments_by_deployments_finder_spec.rb'
- './spec/finders/environments/environments_finder_spec.rb'
- './spec/finders/events_finder_spec.rb'
- './spec/finders/feature_flags_finder_spec.rb'
- './spec/finders/feature_flags_user_lists_finder_spec.rb'
- './spec/finders/fork_projects_finder_spec.rb'
- './spec/finders/fork_targets_finder_spec.rb'
- './spec/finders/group_members_finder_spec.rb'
- './spec/finders/group_projects_finder_spec.rb'
- './spec/finders/groups/accepting_project_transfers_finder_spec.rb'
- './spec/finders/groups_finder_spec.rb'
- './spec/finders/groups/projects_requiring_authorizations_refresh/on_direct_membership_finder_spec.rb'
- './spec/finders/groups/projects_requiring_authorizations_refresh/on_transfer_finder_spec.rb'
- './spec/finders/groups/user_groups_finder_spec.rb'
- './spec/finders/issuables/crm_contact_filter_spec.rb'
- './spec/finders/issuables/crm_organization_filter_spec.rb'
- './spec/finders/issues_finder_spec.rb'
- './spec/finders/joined_groups_finder_spec.rb'
- './spec/finders/keys_finder_spec.rb'
- './spec/finders/labels_finder_spec.rb'
- './spec/finders/lfs_pointers_finder_spec.rb'
- './spec/finders/license_template_finder_spec.rb'
- './spec/finders/merge_request/metrics_finder_spec.rb'
- './spec/finders/merge_requests/by_approvals_finder_spec.rb'
- './spec/finders/merge_requests_finder_spec.rb'
- './spec/finders/merge_requests/oldest_per_commit_finder_spec.rb'
- './spec/finders/merge_request_target_project_finder_spec.rb'
- './spec/finders/milestones_finder_spec.rb'
- './spec/finders/namespaces/projects_finder_spec.rb'
- './spec/finders/notes_finder_spec.rb'
- './spec/finders/packages/build_infos_finder_spec.rb'
- './spec/finders/packages/conan/package_file_finder_spec.rb'
- './spec/finders/packages/conan/package_finder_spec.rb'
- './spec/finders/packages/debian/distributions_finder_spec.rb'
- './spec/finders/packages/generic/package_finder_spec.rb'
- './spec/finders/packages/go/module_finder_spec.rb'
- './spec/finders/packages/go/package_finder_spec.rb'
- './spec/finders/packages/go/version_finder_spec.rb'
- './spec/finders/packages/group_or_project_package_finder_spec.rb'
- './spec/finders/packages/group_packages_finder_spec.rb'
- './spec/finders/packages/helm/package_files_finder_spec.rb'
- './spec/finders/packages/helm/packages_finder_spec.rb'
- './spec/finders/packages/maven/package_finder_spec.rb'
- './spec/finders/packages/npm/package_finder_spec.rb'
- './spec/finders/packages/nuget/package_finder_spec.rb'
- './spec/finders/packages/package_file_finder_spec.rb'
- './spec/finders/packages/package_finder_spec.rb'
- './spec/finders/packages/packages_finder_spec.rb'
- './spec/finders/packages/pypi/package_finder_spec.rb'
- './spec/finders/packages/pypi/packages_finder_spec.rb'
- './spec/finders/pending_todos_finder_spec.rb'
- './spec/finders/personal_access_tokens_finder_spec.rb'
- './spec/finders/personal_projects_finder_spec.rb'
- './spec/finders/projects/export_job_finder_spec.rb'
- './spec/finders/projects_finder_spec.rb'
- './spec/finders/projects/groups_finder_spec.rb'
- './spec/finders/projects/members/effective_access_level_finder_spec.rb'
- './spec/finders/projects/members/effective_access_level_per_user_finder_spec.rb'
- './spec/finders/projects/topics_finder_spec.rb'
- './spec/finders/protected_branches_finder_spec.rb'
- './spec/finders/releases/evidence_pipeline_finder_spec.rb'
- './spec/finders/releases_finder_spec.rb'
- './spec/finders/releases/group_releases_finder_spec.rb'
- './spec/finders/repositories/branch_names_finder_spec.rb'
- './spec/finders/repositories/changelog_commits_finder_spec.rb'
- './spec/finders/repositories/changelog_tag_finder_spec.rb'
- './spec/finders/repositories/tree_finder_spec.rb'
- './spec/finders/resource_milestone_event_finder_spec.rb'
- './spec/finders/resource_state_event_finder_spec.rb'
- './spec/finders/security/jobs_finder_spec.rb'
- './spec/finders/security/license_compliance_jobs_finder_spec.rb'
- './spec/finders/security/security_jobs_finder_spec.rb'
- './spec/finders/sentry_issue_finder_spec.rb'
- './spec/finders/snippets_finder_spec.rb'
- './spec/finders/starred_projects_finder_spec.rb'
- './spec/finders/tags_finder_spec.rb'
- './spec/finders/template_finder_spec.rb'
- './spec/finders/terraform/states_finder_spec.rb'
- './spec/finders/todos_finder_spec.rb'
- './spec/finders/uploader_finder_spec.rb'
- './spec/finders/user_finder_spec.rb'
- './spec/finders/user_group_notification_settings_finder_spec.rb'
- './spec/finders/user_groups_counter_spec.rb'
- './spec/finders/user_recent_events_finder_spec.rb'
- './spec/finders/users_finder_spec.rb'
- './spec/finders/users_star_projects_finder_spec.rb'
- './spec/finders/work_items/work_items_finder_spec.rb'
- './spec/frontend/fixtures/admin_users.rb'
- './spec/frontend/fixtures/analytics.rb'
- './spec/frontend/fixtures/api_deploy_keys.rb'
- './spec/frontend/fixtures/api_merge_requests.rb'
- './spec/frontend/fixtures/api_projects.rb'
- './spec/frontend/fixtures/application_settings.rb'
- './spec/frontend/fixtures/autocomplete.rb'
- './spec/frontend/fixtures/autocomplete_sources.rb'
- './spec/frontend/fixtures/blob.rb'
- './spec/frontend/fixtures/branches.rb'
- './spec/frontend/fixtures/clusters.rb'
- './spec/frontend/fixtures/commit.rb'
- './spec/frontend/fixtures/deploy_keys.rb'
- './spec/frontend/fixtures/freeze_period.rb'
- './spec/frontend/fixtures/groups.rb'
- './spec/frontend/fixtures/integrations.rb'
- './spec/frontend/fixtures/issues.rb'
- './spec/frontend/fixtures/jobs.rb'
- './spec/frontend/fixtures/labels.rb'
- './spec/frontend/fixtures/listbox.rb'
- './spec/frontend/fixtures/merge_requests_diffs.rb'
- './spec/frontend/fixtures/merge_requests.rb'
- './spec/frontend/fixtures/namespaces.rb'
- './spec/frontend/fixtures/pipeline_schedules.rb'
- './spec/frontend/fixtures/pipelines.rb'
- './spec/frontend/fixtures/projects_json.rb'
- './spec/frontend/fixtures/raw.rb'
- './spec/frontend/fixtures/releases.rb'
- './spec/frontend/fixtures/search.rb'
- './spec/frontend/fixtures/sessions.rb'
- './spec/frontend/fixtures/snippet.rb'
- './spec/frontend/fixtures/tabs.rb'
- './spec/frontend/fixtures/tags.rb'
- './spec/frontend/fixtures/timezones.rb'
- './spec/frontend/fixtures/webauthn.rb'
- './spec/graphql/features/authorization_spec.rb'
- './spec/graphql/gitlab_schema_spec.rb'
- './spec/graphql/graphql_triggers_spec.rb'
- './spec/graphql/mutations/alert_management/alerts/set_assignees_spec.rb'
- './spec/graphql/mutations/alert_management/alerts/todo/create_spec.rb'
- './spec/graphql/mutations/alert_management/create_alert_issue_spec.rb'
- './spec/graphql/mutations/alert_management/http_integration/create_spec.rb'
- './spec/graphql/mutations/alert_management/http_integration/destroy_spec.rb'
- './spec/graphql/mutations/alert_management/http_integration/reset_token_spec.rb'
- './spec/graphql/mutations/alert_management/http_integration/update_spec.rb'
- './spec/graphql/mutations/alert_management/prometheus_integration/create_spec.rb'
- './spec/graphql/mutations/alert_management/prometheus_integration/reset_token_spec.rb'
- './spec/graphql/mutations/alert_management/prometheus_integration/update_spec.rb'
- './spec/graphql/mutations/alert_management/update_alert_status_spec.rb'
- './spec/graphql/mutations/boards/issues/issue_move_list_spec.rb'
- './spec/graphql/mutations/boards/lists/create_spec.rb'
- './spec/graphql/mutations/boards/lists/update_spec.rb'
- './spec/graphql/mutations/boards/update_spec.rb'
- './spec/graphql/mutations/branches/create_spec.rb'
- './spec/graphql/mutations/ci/job_token_scope/add_project_spec.rb'
- './spec/graphql/mutations/ci/job_token_scope/remove_project_spec.rb'
- './spec/graphql/mutations/ci/runner/bulk_delete_spec.rb'
- './spec/graphql/mutations/ci/runner/delete_spec.rb'
- './spec/graphql/mutations/ci/runner/update_spec.rb'
- './spec/graphql/mutations/clusters/agents/create_spec.rb'
- './spec/graphql/mutations/clusters/agents/delete_spec.rb'
- './spec/graphql/mutations/clusters/agent_tokens/create_spec.rb'
- './spec/graphql/mutations/clusters/agent_tokens/revoke_spec.rb'
- './spec/graphql/mutations/commits/create_spec.rb'
- './spec/graphql/mutations/concerns/mutations/resolves_group_spec.rb'
- './spec/graphql/mutations/concerns/mutations/resolves_issuable_spec.rb'
- './spec/graphql/mutations/container_expiration_policies/update_spec.rb'
- './spec/graphql/mutations/container_repositories/destroy_spec.rb'
- './spec/graphql/mutations/container_repositories/destroy_tags_spec.rb'
- './spec/graphql/mutations/custom_emoji/create_spec.rb'
- './spec/graphql/mutations/custom_emoji/destroy_spec.rb'
- './spec/graphql/mutations/customer_relations/contacts/create_spec.rb'
- './spec/graphql/mutations/customer_relations/contacts/update_spec.rb'
- './spec/graphql/mutations/customer_relations/organizations/create_spec.rb'
- './spec/graphql/mutations/customer_relations/organizations/update_spec.rb'
- './spec/graphql/mutations/dependency_proxy/group_settings/update_spec.rb'
- './spec/graphql/mutations/dependency_proxy/image_ttl_group_policy/update_spec.rb'
- './spec/graphql/mutations/design_management/delete_spec.rb'
- './spec/graphql/mutations/design_management/move_spec.rb'
- './spec/graphql/mutations/design_management/upload_spec.rb'
- './spec/graphql/mutations/discussions/toggle_resolve_spec.rb'
- './spec/graphql/mutations/environments/canary_ingress/update_spec.rb'
- './spec/graphql/mutations/groups/update_spec.rb'
- './spec/graphql/mutations/issues/create_spec.rb'
- './spec/graphql/mutations/issues/move_spec.rb'
- './spec/graphql/mutations/issues/set_assignees_spec.rb'
- './spec/graphql/mutations/issues/set_confidential_spec.rb'
- './spec/graphql/mutations/issues/set_due_date_spec.rb'
- './spec/graphql/mutations/issues/set_escalation_status_spec.rb'
- './spec/graphql/mutations/issues/set_locked_spec.rb'
- './spec/graphql/mutations/issues/set_severity_spec.rb'
- './spec/graphql/mutations/issues/set_subscription_spec.rb'
- './spec/graphql/mutations/issues/update_spec.rb'
- './spec/graphql/mutations/labels/create_spec.rb'
- './spec/graphql/mutations/merge_requests/accept_spec.rb'
- './spec/graphql/mutations/merge_requests/create_spec.rb'
- './spec/graphql/mutations/merge_requests/set_assignees_spec.rb'
- './spec/graphql/mutations/merge_requests/set_draft_spec.rb'
- './spec/graphql/mutations/merge_requests/set_labels_spec.rb'
- './spec/graphql/mutations/merge_requests/set_locked_spec.rb'
- './spec/graphql/mutations/merge_requests/set_milestone_spec.rb'
- './spec/graphql/mutations/merge_requests/set_reviewers_spec.rb'
- './spec/graphql/mutations/merge_requests/set_subscription_spec.rb'
- './spec/graphql/mutations/merge_requests/update_spec.rb'
- './spec/graphql/mutations/namespace/package_settings/update_spec.rb'
- './spec/graphql/mutations/notes/reposition_image_diff_note_spec.rb'
- './spec/graphql/mutations/pages/mark_onboarding_complete_spec.rb'
- './spec/graphql/mutations/release_asset_links/create_spec.rb'
- './spec/graphql/mutations/release_asset_links/delete_spec.rb'
- './spec/graphql/mutations/release_asset_links/update_spec.rb'
- './spec/graphql/mutations/releases/create_spec.rb'
- './spec/graphql/mutations/releases/delete_spec.rb'
- './spec/graphql/mutations/releases/update_spec.rb'
- './spec/graphql/mutations/security/ci_configuration/base_security_analyzer_spec.rb'
- './spec/graphql/mutations/security/ci_configuration/configure_sast_iac_spec.rb'
- './spec/graphql/mutations/security/ci_configuration/configure_sast_spec.rb'
- './spec/graphql/mutations/security/ci_configuration/configure_secret_detection_spec.rb'
- './spec/graphql/mutations/terraform/state/delete_spec.rb'
- './spec/graphql/mutations/terraform/state/lock_spec.rb'
- './spec/graphql/mutations/terraform/state/unlock_spec.rb'
- './spec/graphql/mutations/timelogs/delete_spec.rb'
- './spec/graphql/mutations/todos/create_spec.rb'
- './spec/graphql/mutations/todos/mark_all_done_spec.rb'
- './spec/graphql/mutations/todos/mark_done_spec.rb'
- './spec/graphql/mutations/todos/restore_many_spec.rb'
- './spec/graphql/mutations/todos/restore_spec.rb'
- './spec/graphql/mutations/user_callouts/create_spec.rb'
- './spec/graphql/resolvers/admin/analytics/usage_trends/measurements_resolver_spec.rb'
- './spec/graphql/resolvers/alert_management/alert_resolver_spec.rb'
- './spec/graphql/resolvers/alert_management/alert_status_counts_resolver_spec.rb'
- './spec/graphql/resolvers/alert_management/http_integrations_resolver_spec.rb'
- './spec/graphql/resolvers/alert_management/integrations_resolver_spec.rb'
- './spec/graphql/resolvers/base_resolver_spec.rb'
- './spec/graphql/resolvers/blobs_resolver_spec.rb'
- './spec/graphql/resolvers/board_list_issues_resolver_spec.rb'
- './spec/graphql/resolvers/board_list_resolver_spec.rb'
- './spec/graphql/resolvers/board_lists_resolver_spec.rb'
- './spec/graphql/resolvers/board_resolver_spec.rb'
- './spec/graphql/resolvers/boards_resolver_spec.rb'
- './spec/graphql/resolvers/ci/config_resolver_spec.rb'
- './spec/graphql/resolvers/ci/jobs_resolver_spec.rb'
- './spec/graphql/resolvers/ci/job_token_scope_resolver_spec.rb'
- './spec/graphql/resolvers/ci/pipeline_analytics_resolver_spec.rb'
- './spec/graphql/resolvers/ci/project_pipeline_counts_resolver_spec.rb'
- './spec/graphql/resolvers/ci/project_pipeline_resolver_spec.rb'
- './spec/graphql/resolvers/ci/project_pipelines_resolver_spec.rb'
- './spec/graphql/resolvers/ci/runner_jobs_resolver_spec.rb'
- './spec/graphql/resolvers/ci/runner_platforms_resolver_spec.rb'
- './spec/graphql/resolvers/ci/runner_setup_resolver_spec.rb'
- './spec/graphql/resolvers/ci/template_resolver_spec.rb'
- './spec/graphql/resolvers/ci/test_report_summary_resolver_spec.rb'
- './spec/graphql/resolvers/ci/test_suite_resolver_spec.rb'
- './spec/graphql/resolvers/clusters/agent_activity_events_resolver_spec.rb'
- './spec/graphql/resolvers/clusters/agents_resolver_spec.rb'
- './spec/graphql/resolvers/clusters/agent_tokens_resolver_spec.rb'
- './spec/graphql/resolvers/commit_pipelines_resolver_spec.rb'
- './spec/graphql/resolvers/concerns/caching_array_resolver_spec.rb'
- './spec/graphql/resolvers/concerns/looks_ahead_spec.rb'
- './spec/graphql/resolvers/concerns/resolves_groups_spec.rb'
- './spec/graphql/resolvers/concerns/resolves_ids_spec.rb'
- './spec/graphql/resolvers/concerns/resolves_pipelines_spec.rb'
- './spec/graphql/resolvers/concerns/resolves_project_spec.rb'
- './spec/graphql/resolvers/container_repositories_resolver_spec.rb'
- './spec/graphql/resolvers/container_repository_tags_resolver_spec.rb'
- './spec/graphql/resolvers/crm/contacts_resolver_spec.rb'
- './spec/graphql/resolvers/crm/contact_state_counts_resolver_spec.rb'
- './spec/graphql/resolvers/crm/organizations_resolver_spec.rb'
- './spec/graphql/resolvers/crm/organization_state_counts_resolver_spec.rb'
- './spec/graphql/resolvers/design_management/design_at_version_resolver_spec.rb'
- './spec/graphql/resolvers/design_management/design_resolver_spec.rb'
- './spec/graphql/resolvers/design_management/designs_resolver_spec.rb'
- './spec/graphql/resolvers/design_management/version/design_at_version_resolver_spec.rb'
- './spec/graphql/resolvers/design_management/version/designs_at_version_resolver_spec.rb'
- './spec/graphql/resolvers/design_management/version_in_collection_resolver_spec.rb'
- './spec/graphql/resolvers/design_management/version_resolver_spec.rb'
- './spec/graphql/resolvers/design_management/versions_resolver_spec.rb'
- './spec/graphql/resolvers/echo_resolver_spec.rb'
- './spec/graphql/resolvers/environments_resolver_spec.rb'
- './spec/graphql/resolvers/error_tracking/sentry_detailed_error_resolver_spec.rb'
- './spec/graphql/resolvers/error_tracking/sentry_error_collection_resolver_spec.rb'
- './spec/graphql/resolvers/error_tracking/sentry_errors_resolver_spec.rb'
- './spec/graphql/resolvers/group_issues_resolver_spec.rb'
- './spec/graphql/resolvers/group_labels_resolver_spec.rb'
- './spec/graphql/resolvers/group_members/notification_email_resolver_spec.rb'
- './spec/graphql/resolvers/group_members_resolver_spec.rb'
- './spec/graphql/resolvers/group_milestones_resolver_spec.rb'
- './spec/graphql/resolvers/group_packages_resolver_spec.rb'
- './spec/graphql/resolvers/group_resolver_spec.rb'
- './spec/graphql/resolvers/groups_resolver_spec.rb'
- './spec/graphql/resolvers/issue_status_counts_resolver_spec.rb'
- './spec/graphql/resolvers/kas/agent_configurations_resolver_spec.rb'
- './spec/graphql/resolvers/kas/agent_connections_resolver_spec.rb'
- './spec/graphql/resolvers/labels_resolver_spec.rb'
- './spec/graphql/resolvers/last_commit_resolver_spec.rb'
- './spec/graphql/resolvers/merge_request_pipelines_resolver_spec.rb'
- './spec/graphql/resolvers/merge_requests_count_resolver_spec.rb'
- './spec/graphql/resolvers/merge_requests_resolver_spec.rb'
- './spec/graphql/resolvers/metadata_resolver_spec.rb'
- './spec/graphql/resolvers/namespace_projects_resolver_spec.rb'
- './spec/graphql/resolvers/package_details_resolver_spec.rb'
- './spec/graphql/resolvers/package_pipelines_resolver_spec.rb'
- './spec/graphql/resolvers/packages_base_resolver_spec.rb'
- './spec/graphql/resolvers/paginated_tree_resolver_spec.rb'
- './spec/graphql/resolvers/project_jobs_resolver_spec.rb'
- './spec/graphql/resolvers/project_members_resolver_spec.rb'
- './spec/graphql/resolvers/project_merge_requests_resolver_spec.rb'
- './spec/graphql/resolvers/project_milestones_resolver_spec.rb'
- './spec/graphql/resolvers/project_packages_resolver_spec.rb'
- './spec/graphql/resolvers/project_resolver_spec.rb'
- './spec/graphql/resolvers/projects/fork_targets_resolver_spec.rb'
- './spec/graphql/resolvers/projects/grafana_integration_resolver_spec.rb'
- './spec/graphql/resolvers/projects/jira_projects_resolver_spec.rb'
- './spec/graphql/resolvers/projects_resolver_spec.rb'
- './spec/graphql/resolvers/projects/services_resolver_spec.rb'
- './spec/graphql/resolvers/projects/snippets_resolver_spec.rb'
- './spec/graphql/resolvers/recent_boards_resolver_spec.rb'
- './spec/graphql/resolvers/release_milestones_resolver_spec.rb'
- './spec/graphql/resolvers/release_resolver_spec.rb'
- './spec/graphql/resolvers/releases_resolver_spec.rb'
- './spec/graphql/resolvers/repository_branch_names_resolver_spec.rb'
- './spec/graphql/resolvers/snippets/blobs_resolver_spec.rb'
- './spec/graphql/resolvers/snippets_resolver_spec.rb'
- './spec/graphql/resolvers/terraform/states_resolver_spec.rb'
- './spec/graphql/resolvers/timelog_resolver_spec.rb'
- './spec/graphql/resolvers/todos_resolver_spec.rb'
- './spec/graphql/resolvers/topics_resolver_spec.rb'
- './spec/graphql/resolvers/tree_resolver_spec.rb'
- './spec/graphql/resolvers/user_discussions_count_resolver_spec.rb'
- './spec/graphql/resolvers/user_notes_count_resolver_spec.rb'
- './spec/graphql/resolvers/user_resolver_spec.rb'
- './spec/graphql/resolvers/users/group_count_resolver_spec.rb'
- './spec/graphql/resolvers/users/groups_resolver_spec.rb'
- './spec/graphql/resolvers/users/participants_resolver_spec.rb'
- './spec/graphql/resolvers/users_resolver_spec.rb'
- './spec/graphql/resolvers/users/snippets_resolver_spec.rb'
- './spec/graphql/resolvers/work_item_resolver_spec.rb'
- './spec/graphql/resolvers/work_items_resolver_spec.rb'
- './spec/graphql/resolvers/work_items/types_resolver_spec.rb'
- './spec/graphql/subscriptions/issuable_updated_spec.rb'
- './spec/helpers/access_tokens_helper_spec.rb'
- './spec/helpers/admin/application_settings/settings_helper_spec.rb'
- './spec/helpers/admin/background_migrations_helper_spec.rb'
- './spec/helpers/admin/deploy_key_helper_spec.rb'
- './spec/helpers/admin/identities_helper_spec.rb'
- './spec/helpers/admin/user_actions_helper_spec.rb'
- './spec/helpers/appearances_helper_spec.rb'
- './spec/helpers/application_helper_spec.rb'
- './spec/helpers/auth_helper_spec.rb'
- './spec/helpers/auto_devops_helper_spec.rb'
- './spec/helpers/avatars_helper_spec.rb'
- './spec/helpers/award_emoji_helper_spec.rb'
- './spec/helpers/badges_helper_spec.rb'
- './spec/helpers/bizible_helper_spec.rb'
- './spec/helpers/blame_helper_spec.rb'
- './spec/helpers/blob_helper_spec.rb'
- './spec/helpers/boards_helper_spec.rb'
- './spec/helpers/branches_helper_spec.rb'
- './spec/helpers/breadcrumbs_helper_spec.rb'
- './spec/helpers/button_helper_spec.rb'
- './spec/helpers/calendar_helper_spec.rb'
- './spec/helpers/ci/pipeline_editor_helper_spec.rb'
- './spec/helpers/ci/pipelines_helper_spec.rb'
- './spec/helpers/ci/secure_files_helper_spec.rb'
- './spec/helpers/ci/status_helper_spec.rb'
- './spec/helpers/ci/triggers_helper_spec.rb'
- './spec/helpers/clusters_helper_spec.rb'
- './spec/helpers/colors_helper_spec.rb'
- './spec/helpers/commits_helper_spec.rb'
- './spec/helpers/components_helper_spec.rb'
- './spec/helpers/container_expiration_policies_helper_spec.rb'
- './spec/helpers/container_registry/container_registry_helper_spec.rb'
- './spec/helpers/cookies_helper_spec.rb'
- './spec/helpers/dashboard_helper_spec.rb'
- './spec/helpers/deploy_tokens_helper_spec.rb'
- './spec/helpers/dev_ops_report_helper_spec.rb'
- './spec/helpers/diff_helper_spec.rb'
- './spec/helpers/dropdowns_helper_spec.rb'
- './spec/helpers/emails_helper_spec.rb'
- './spec/helpers/emoji_helper_spec.rb'
- './spec/helpers/enable_search_settings_helper_spec.rb'
- './spec/helpers/environment_helper_spec.rb'
- './spec/helpers/environments_helper_spec.rb'
- './spec/helpers/explore_helper_spec.rb'
- './spec/helpers/export_helper_spec.rb'
- './spec/helpers/external_link_helper_spec.rb'
- './spec/helpers/feature_flags_helper_spec.rb'
- './spec/helpers/form_helper_spec.rb'
- './spec/helpers/git_helper_spec.rb'
- './spec/helpers/gitlab_routing_helper_spec.rb'
- './spec/helpers/gitlab_script_tag_helper_spec.rb'
- './spec/helpers/graph_helper_spec.rb'
- './spec/helpers/groups/group_members_helper_spec.rb'
- './spec/helpers/groups_helper_spec.rb'
- './spec/helpers/hooks_helper_spec.rb'
- './spec/helpers/icons_helper_spec.rb'
- './spec/helpers/import_helper_spec.rb'
- './spec/helpers/instance_configuration_helper_spec.rb'
- './spec/helpers/integrations_helper_spec.rb'
- './spec/helpers/issuables_description_templates_helper_spec.rb'
- './spec/helpers/issuables_helper_spec.rb'
- './spec/helpers/issues_helper_spec.rb'
- './spec/helpers/jira_connect_helper_spec.rb'
- './spec/helpers/keyset_helper_spec.rb'
- './spec/helpers/labels_helper_spec.rb'
- './spec/helpers/lazy_image_tag_helper_spec.rb'
- './spec/helpers/listbox_helper_spec.rb'
- './spec/helpers/markup_helper_spec.rb'
- './spec/helpers/members_helper_spec.rb'
- './spec/helpers/merge_requests_helper_spec.rb'
- './spec/helpers/namespaces_helper_spec.rb'
- './spec/helpers/nav/new_dropdown_helper_spec.rb'
- './spec/helpers/notes_helper_spec.rb'
- './spec/helpers/notifications_helper_spec.rb'
- './spec/helpers/notify_helper_spec.rb'
- './spec/helpers/numbers_helper_spec.rb'
- './spec/helpers/one_trust_helper_spec.rb'
- './spec/helpers/operations_helper_spec.rb'
- './spec/helpers/packages_helper_spec.rb'
- './spec/helpers/page_layout_helper_spec.rb'
- './spec/helpers/pagination_helper_spec.rb'
- './spec/helpers/preferences_helper_spec.rb'
- './spec/helpers/profiles_helper_spec.rb'
- './spec/helpers/projects/alert_management_helper_spec.rb'
- './spec/helpers/projects/cluster_agents_helper_spec.rb'
- './spec/helpers/projects/error_tracking_helper_spec.rb'
- './spec/helpers/projects_helper_spec.rb'
- './spec/helpers/projects/pipeline_helper_spec.rb'
- './spec/helpers/projects/project_members_helper_spec.rb'
- './spec/helpers/projects/security/configuration_helper_spec.rb'
- './spec/helpers/projects/terraform_helper_spec.rb'
- './spec/helpers/recaptcha_helper_spec.rb'
- './spec/helpers/registrations_helper_spec.rb'
- './spec/helpers/releases_helper_spec.rb'
- './spec/helpers/routing/pseudonymization_helper_spec.rb'
- './spec/helpers/rss_helper_spec.rb'
- './spec/helpers/sessions_helper_spec.rb'
- './spec/helpers/sidebars_helper_spec.rb'
- './spec/helpers/snippets_helper_spec.rb'
- './spec/helpers/sorting_helper_spec.rb'
- './spec/helpers/sourcegraph_helper_spec.rb'
- './spec/helpers/ssh_keys_helper_spec.rb'
- './spec/helpers/startupjs_helper_spec.rb'
- './spec/helpers/stat_anchors_helper_spec.rb'
- './spec/helpers/storage_helper_spec.rb'
- './spec/helpers/submodule_helper_spec.rb'
- './spec/helpers/subscribable_banner_helper_spec.rb'
- './spec/helpers/tab_helper_spec.rb'
- './spec/helpers/terms_helper_spec.rb'
- './spec/helpers/timeboxes_helper_spec.rb'
- './spec/helpers/time_helper_spec.rb'
- './spec/helpers/time_zone_helper_spec.rb'
- './spec/helpers/tracking_helper_spec.rb'
- './spec/helpers/tree_helper_spec.rb'
- './spec/helpers/users/callouts_helper_spec.rb'
- './spec/helpers/users/group_callouts_helper_spec.rb'
- './spec/helpers/users_helper_spec.rb'
- './spec/helpers/version_check_helper_spec.rb'
- './spec/helpers/visibility_level_helper_spec.rb'
- './spec/helpers/web_hooks/web_hooks_helper_spec.rb'
- './spec/helpers/web_ide_button_helper_spec.rb'
- './spec/helpers/webpack_helper_spec.rb'
- './spec/helpers/whats_new_helper_spec.rb'
- './spec/helpers/wiki_helper_spec.rb'
- './spec/helpers/wiki_page_version_helper_spec.rb'
- './spec/helpers/x509_helper_spec.rb'
- './spec/lib/api/api_spec.rb'
- './spec/lib/api/base_spec.rb'
- './spec/lib/api/ci/helpers/runner_helpers_spec.rb'
- './spec/lib/api/ci/helpers/runner_spec.rb'
- './spec/lib/api/entities/application_setting_spec.rb'
- './spec/lib/api/entities/basic_project_details_spec.rb'
- './spec/lib/api/entities/branch_spec.rb'
- './spec/lib/api/entities/bulk_imports/entity_failure_spec.rb'
- './spec/lib/api/entities/bulk_imports/entity_spec.rb'
- './spec/lib/api/entities/bulk_imports/export_status_spec.rb'
- './spec/lib/api/entities/bulk_import_spec.rb'
- './spec/lib/api/entities/changelog_spec.rb'
- './spec/lib/api/entities/ci/job_artifact_file_spec.rb'
- './spec/lib/api/entities/ci/job_request/dependency_spec.rb'
- './spec/lib/api/entities/ci/job_request/image_spec.rb'
- './spec/lib/api/entities/ci/job_request/port_spec.rb'
- './spec/lib/api/entities/ci/job_request/service_spec.rb'
- './spec/lib/api/entities/ci/pipeline_spec.rb'
- './spec/lib/api/entities/clusters/agent_spec.rb'
- './spec/lib/api/entities/deploy_key_spec.rb'
- './spec/lib/api/entities/deploy_keys_project_spec.rb'
- './spec/lib/api/entities/deployment_extended_spec.rb'
- './spec/lib/api/entities/design_management/design_spec.rb'
- './spec/lib/api/entities/group_detail_spec.rb'
- './spec/lib/api/entities/merge_request_approvals_spec.rb'
- './spec/lib/api/entities/merge_request_basic_spec.rb'
- './spec/lib/api/entities/merge_request_changes_spec.rb'
- './spec/lib/api/entities/nuget/dependency_group_spec.rb'
- './spec/lib/api/entities/nuget/dependency_spec.rb'
- './spec/lib/api/entities/nuget/metadatum_spec.rb'
- './spec/lib/api/entities/nuget/package_metadata_catalog_entry_spec.rb'
- './spec/lib/api/entities/nuget/search_result_spec.rb'
- './spec/lib/api/entities/package_spec.rb'
- './spec/lib/api/entities/personal_access_token_spec.rb'
- './spec/lib/api/entities/plan_limit_spec.rb'
- './spec/lib/api/entities/project_import_failed_relation_spec.rb'
- './spec/lib/api/entities/project_import_status_spec.rb'
- './spec/lib/api/entities/project_spec.rb'
- './spec/lib/api/entities/projects/repository_storage_move_spec.rb'
- './spec/lib/api/entities/projects/topic_spec.rb'
- './spec/lib/api/entities/public_group_details_spec.rb'
- './spec/lib/api/entities/release_spec.rb'
- './spec/lib/api/entities/snippet_spec.rb'
- './spec/lib/api/entities/snippets/repository_storage_move_spec.rb'
- './spec/lib/api/entities/ssh_key_spec.rb'
- './spec/lib/api/entities/user_spec.rb'
- './spec/lib/api/entities/wiki_page_spec.rb'
- './spec/lib/api/every_api_endpoint_spec.rb'
- './spec/lib/api/helpers/authentication_spec.rb'
- './spec/lib/api/helpers/caching_spec.rb'
- './spec/lib/api/helpers/common_helpers_spec.rb'
- './spec/lib/api/helpers/graphql_helpers_spec.rb'
- './spec/lib/api/helpers/label_helpers_spec.rb'
- './spec/lib/api/helpers/merge_requests_helpers_spec.rb'
- './spec/lib/api/helpers/packages/dependency_proxy_helpers_spec.rb'
- './spec/lib/api/helpers/packages_helpers_spec.rb'
- './spec/lib/api/helpers/packages_manager_clients_helpers_spec.rb'
- './spec/lib/api/helpers/pagination_spec.rb'
- './spec/lib/api/helpers/pagination_strategies_spec.rb'
- './spec/lib/api/helpers/project_stats_refresh_conflicts_helpers_spec.rb'
- './spec/lib/api/helpers/rate_limiter_spec.rb'
- './spec/lib/api/helpers/related_resources_helpers_spec.rb'
- './spec/lib/api/helpers_spec.rb'
- './spec/lib/api/helpers/variables_helpers_spec.rb'
- './spec/lib/api/helpers/version_spec.rb'
- './spec/lib/api/support/git_access_actor_spec.rb'
- './spec/lib/api/validations/validators/absence_spec.rb'
- './spec/lib/api/validations/validators/array_none_any_spec.rb'
- './spec/lib/api/validations/validators/email_or_email_list_spec.rb'
- './spec/lib/api/validations/validators/file_path_spec.rb'
- './spec/lib/api/validations/validators/git_ref_spec.rb'
- './spec/lib/api/validations/validators/git_sha_spec.rb'
- './spec/lib/api/validations/validators/integer_none_any_spec.rb'
- './spec/lib/api/validations/validators/integer_or_custom_value_spec.rb'
- './spec/lib/api/validations/validators/limit_spec.rb'
- './spec/lib/api/validations/validators/project_portable_spec.rb'
- './spec/lib/api/validations/validators/untrusted_regexp_spec.rb'
- './spec/lib/atlassian/jira_connect/client_spec.rb'
- './spec/lib/atlassian/jira_connect/jwt/asymmetric_spec.rb'
- './spec/lib/atlassian/jira_connect/jwt/symmetric_spec.rb'
- './spec/lib/atlassian/jira_connect/serializers/author_entity_spec.rb'
- './spec/lib/atlassian/jira_connect/serializers/base_entity_spec.rb'
- './spec/lib/atlassian/jira_connect/serializers/branch_entity_spec.rb'
- './spec/lib/atlassian/jira_connect/serializers/build_entity_spec.rb'
- './spec/lib/atlassian/jira_connect/serializers/feature_flag_entity_spec.rb'
- './spec/lib/atlassian/jira_connect/serializers/pull_request_entity_spec.rb'
- './spec/lib/atlassian/jira_connect/serializers/repository_entity_spec.rb'
- './spec/lib/atlassian/jira_connect_spec.rb'
- './spec/lib/atlassian/jira_issue_key_extractor_spec.rb'
- './spec/lib/backup/database_backup_error_spec.rb'
- './spec/lib/backup/file_backup_error_spec.rb'
- './spec/lib/backup/gitaly_backup_spec.rb'
- './spec/lib/backup/manager_spec.rb'
- './spec/lib/banzai/color_parser_spec.rb'
- './spec/lib/banzai/commit_renderer_spec.rb'
- './spec/lib/banzai/cross_project_reference_spec.rb'
- './spec/lib/banzai/filter/absolute_link_filter_spec.rb'
- './spec/lib/banzai/filter_array_spec.rb'
- './spec/lib/banzai/filter/ascii_doc_post_processing_filter_spec.rb'
- './spec/lib/banzai/filter/ascii_doc_sanitization_filter_spec.rb'
- './spec/lib/banzai/filter/asset_proxy_filter_spec.rb'
- './spec/lib/banzai/filter/audio_link_filter_spec.rb'
- './spec/lib/banzai/filter/broadcast_message_placeholders_filter_spec.rb'
- './spec/lib/banzai/filter/broadcast_message_sanitization_filter_spec.rb'
- './spec/lib/banzai/filter/color_filter_spec.rb'
- './spec/lib/banzai/filter/commit_trailers_filter_spec.rb'
- './spec/lib/banzai/filter/custom_emoji_filter_spec.rb'
- './spec/lib/banzai/filter/emoji_filter_spec.rb'
- './spec/lib/banzai/filter/external_link_filter_spec.rb'
- './spec/lib/banzai/filter/footnote_filter_spec.rb'
- './spec/lib/banzai/filter/front_matter_filter_spec.rb'
- './spec/lib/banzai/filter/gollum_tags_filter_spec.rb'
- './spec/lib/banzai/filter/html_entity_filter_spec.rb'
- './spec/lib/banzai/filter/image_lazy_load_filter_spec.rb'
- './spec/lib/banzai/filter/image_link_filter_spec.rb'
- './spec/lib/banzai/filter/inline_diff_filter_spec.rb'
- './spec/lib/banzai/filter/issuable_reference_expansion_filter_spec.rb'
- './spec/lib/banzai/filter/jira_import/adf_to_commonmark_filter_spec.rb'
- './spec/lib/banzai/filter/kroki_filter_spec.rb'
- './spec/lib/banzai/filter/markdown_filter_spec.rb'
- './spec/lib/banzai/filter/math_filter_spec.rb'
- './spec/lib/banzai/filter/mermaid_filter_spec.rb'
- './spec/lib/banzai/filter/normalize_source_filter_spec.rb'
- './spec/lib/banzai/filter/plantuml_filter_spec.rb'
- './spec/lib/banzai/filter/reference_redactor_filter_spec.rb'
- './spec/lib/banzai/filter/references/abstract_reference_filter_spec.rb'
- './spec/lib/banzai/filter/references/alert_reference_filter_spec.rb'
- './spec/lib/banzai/filter/references/commit_range_reference_filter_spec.rb'
- './spec/lib/banzai/filter/references/commit_reference_filter_spec.rb'
- './spec/lib/banzai/filter/references/design_reference_filter_spec.rb'
- './spec/lib/banzai/filter/references/external_issue_reference_filter_spec.rb'
- './spec/lib/banzai/filter/references/feature_flag_reference_filter_spec.rb'
- './spec/lib/banzai/filter/references/issue_reference_filter_spec.rb'
- './spec/lib/banzai/filter/references/label_reference_filter_spec.rb'
- './spec/lib/banzai/filter/references/merge_request_reference_filter_spec.rb'
- './spec/lib/banzai/filter/references/milestone_reference_filter_spec.rb'
- './spec/lib/banzai/filter/references/project_reference_filter_spec.rb'
- './spec/lib/banzai/filter/references/reference_cache_spec.rb'
- './spec/lib/banzai/filter/references/reference_filter_spec.rb'
- './spec/lib/banzai/filter/references/snippet_reference_filter_spec.rb'
- './spec/lib/banzai/filter/references/user_reference_filter_spec.rb'
- './spec/lib/banzai/filter/repository_link_filter_spec.rb'
- './spec/lib/banzai/filter/sanitization_filter_spec.rb'
- './spec/lib/banzai/filter/spaced_link_filter_spec.rb'
- './spec/lib/banzai/filter/suggestion_filter_spec.rb'
- './spec/lib/banzai/filter/syntax_highlight_filter_spec.rb'
- './spec/lib/banzai/filter/table_of_contents_tag_filter_spec.rb'
- './spec/lib/banzai/filter/task_list_filter_spec.rb'
- './spec/lib/banzai/filter/truncate_source_filter_spec.rb'
- './spec/lib/banzai/filter/upload_link_filter_spec.rb'
- './spec/lib/banzai/filter/video_link_filter_spec.rb'
- './spec/lib/banzai/filter/wiki_link_filter_spec.rb'
- './spec/lib/banzai/issuable_extractor_spec.rb'
- './spec/lib/banzai/object_renderer_spec.rb'
- './spec/lib/banzai/pipeline/broadcast_message_pipeline_spec.rb'
- './spec/lib/banzai/pipeline/description_pipeline_spec.rb'
- './spec/lib/banzai/pipeline/email_pipeline_spec.rb'
- './spec/lib/banzai/pipeline/emoji_pipeline_spec.rb'
- './spec/lib/banzai/pipeline/full_pipeline_spec.rb'
- './spec/lib/banzai/pipeline/gfm_pipeline_spec.rb'
- './spec/lib/banzai/pipeline/jira_import/adf_commonmark_pipeline_spec.rb'
- './spec/lib/banzai/pipeline/plain_markdown_pipeline_spec.rb'
- './spec/lib/banzai/pipeline/post_process_pipeline_spec.rb'
- './spec/lib/banzai/pipeline/pre_process_pipeline_spec.rb'
- './spec/lib/banzai/pipeline_spec.rb'
- './spec/lib/banzai/pipeline/wiki_pipeline_spec.rb'
- './spec/lib/banzai/querying_spec.rb'
- './spec/lib/banzai/reference_parser/alert_parser_spec.rb'
- './spec/lib/banzai/reference_parser/base_parser_spec.rb'
- './spec/lib/banzai/reference_parser/commit_parser_spec.rb'
- './spec/lib/banzai/reference_parser/commit_range_parser_spec.rb'
- './spec/lib/banzai/reference_parser/design_parser_spec.rb'
- './spec/lib/banzai/reference_parser/external_issue_parser_spec.rb'
- './spec/lib/banzai/reference_parser/feature_flag_parser_spec.rb'
- './spec/lib/banzai/reference_parser/issue_parser_spec.rb'
- './spec/lib/banzai/reference_parser/label_parser_spec.rb'
- './spec/lib/banzai/reference_parser/mentioned_group_parser_spec.rb'
- './spec/lib/banzai/reference_parser/mentioned_project_parser_spec.rb'
- './spec/lib/banzai/reference_parser/mentioned_user_parser_spec.rb'
- './spec/lib/banzai/reference_parser/merge_request_parser_spec.rb'
- './spec/lib/banzai/reference_parser/milestone_parser_spec.rb'
- './spec/lib/banzai/reference_parser/project_parser_spec.rb'
- './spec/lib/banzai/reference_parser/snippet_parser_spec.rb'
- './spec/lib/banzai/reference_parser/user_parser_spec.rb'
- './spec/lib/banzai/reference_redactor_spec.rb'
- './spec/lib/banzai/render_context_spec.rb'
- './spec/lib/banzai/renderer_spec.rb'
- './spec/lib/bulk_imports/clients/graphql_spec.rb'
- './spec/lib/bulk_imports/clients/http_spec.rb'
- './spec/lib/bulk_imports/common/extractors/graphql_extractor_spec.rb'
- './spec/lib/bulk_imports/common/extractors/json_extractor_spec.rb'
- './spec/lib/bulk_imports/common/extractors/ndjson_extractor_spec.rb'
- './spec/lib/bulk_imports/common/extractors/rest_extractor_spec.rb'
- './spec/lib/bulk_imports/common/graphql/get_members_query_spec.rb'
- './spec/lib/bulk_imports/common/pipelines/badges_pipeline_spec.rb'
- './spec/lib/bulk_imports/common/pipelines/boards_pipeline_spec.rb'
- './spec/lib/bulk_imports/common/pipelines/entity_finisher_spec.rb'
- './spec/lib/bulk_imports/common/pipelines/labels_pipeline_spec.rb'
- './spec/lib/bulk_imports/common/pipelines/lfs_objects_pipeline_spec.rb'
- './spec/lib/bulk_imports/common/pipelines/members_pipeline_spec.rb'
- './spec/lib/bulk_imports/common/pipelines/milestones_pipeline_spec.rb'
- './spec/lib/bulk_imports/common/pipelines/uploads_pipeline_spec.rb'
- './spec/lib/bulk_imports/common/pipelines/wiki_pipeline_spec.rb'
- './spec/lib/bulk_imports/common/rest/get_badges_query_spec.rb'
- './spec/lib/bulk_imports/common/transformers/prohibited_attributes_transformer_spec.rb'
- './spec/lib/bulk_imports/groups/extractors/subgroups_extractor_spec.rb'
- './spec/lib/bulk_imports/groups/graphql/get_group_query_spec.rb'
- './spec/lib/bulk_imports/groups/graphql/get_projects_query_spec.rb'
- './spec/lib/bulk_imports/groups/loaders/group_loader_spec.rb'
- './spec/lib/bulk_imports/groups/pipelines/group_attributes_pipeline_spec.rb'
- './spec/lib/bulk_imports/groups/pipelines/group_pipeline_spec.rb'
- './spec/lib/bulk_imports/groups/pipelines/namespace_settings_pipeline_spec.rb'
- './spec/lib/bulk_imports/groups/pipelines/project_entities_pipeline_spec.rb'
- './spec/lib/bulk_imports/groups/pipelines/subgroup_entities_pipeline_spec.rb'
- './spec/lib/bulk_imports/groups/stage_spec.rb'
- './spec/lib/bulk_imports/groups/transformers/group_attributes_transformer_spec.rb'
- './spec/lib/bulk_imports/groups/transformers/subgroup_to_entity_transformer_spec.rb'
- './spec/lib/bulk_imports/ndjson_pipeline_spec.rb'
- './spec/lib/bulk_imports/network_error_spec.rb'
- './spec/lib/bulk_imports/pipeline/context_spec.rb'
- './spec/lib/bulk_imports/pipeline/extracted_data_spec.rb'
- './spec/lib/bulk_imports/pipeline/runner_spec.rb'
- './spec/lib/bulk_imports/pipeline_spec.rb'
- './spec/lib/bulk_imports/projects/graphql/get_project_query_spec.rb'
- './spec/lib/bulk_imports/projects/graphql/get_repository_query_spec.rb'
- './spec/lib/bulk_imports/projects/graphql/get_snippet_repository_query_spec.rb'
- './spec/lib/bulk_imports/projects/pipelines/auto_devops_pipeline_spec.rb'
- './spec/lib/bulk_imports/projects/pipelines/ci_pipelines_pipeline_spec.rb'
- './spec/lib/bulk_imports/projects/pipelines/container_expiration_policy_pipeline_spec.rb'
- './spec/lib/bulk_imports/projects/pipelines/design_bundle_pipeline_spec.rb'
- './spec/lib/bulk_imports/projects/pipelines/external_pull_requests_pipeline_spec.rb'
- './spec/lib/bulk_imports/projects/pipelines/issues_pipeline_spec.rb'
- './spec/lib/bulk_imports/projects/pipelines/merge_requests_pipeline_spec.rb'
- './spec/lib/bulk_imports/projects/pipelines/pipeline_schedules_pipeline_spec.rb'
- './spec/lib/bulk_imports/projects/pipelines/project_attributes_pipeline_spec.rb'
- './spec/lib/bulk_imports/projects/pipelines/project_feature_pipeline_spec.rb'
- './spec/lib/bulk_imports/projects/pipelines/project_pipeline_spec.rb'
- './spec/lib/bulk_imports/projects/pipelines/protected_branches_pipeline_spec.rb'
- './spec/lib/bulk_imports/projects/pipelines/releases_pipeline_spec.rb'
- './spec/lib/bulk_imports/projects/pipelines/repository_bundle_pipeline_spec.rb'
- './spec/lib/bulk_imports/projects/pipelines/repository_pipeline_spec.rb'
- './spec/lib/bulk_imports/projects/pipelines/service_desk_setting_pipeline_spec.rb'
- './spec/lib/bulk_imports/projects/pipelines/snippets_pipeline_spec.rb'
- './spec/lib/bulk_imports/projects/pipelines/snippets_repository_pipeline_spec.rb'
- './spec/lib/bulk_imports/projects/stage_spec.rb'
- './spec/lib/bulk_imports/projects/transformers/project_attributes_transformer_spec.rb'
- './spec/lib/bulk_imports/retry_pipeline_error_spec.rb'
- './spec/lib/bulk_imports/users_mapper_spec.rb'
- './spec/lib/constraints/admin_constrainer_spec.rb'
- './spec/lib/constraints/group_url_constrainer_spec.rb'
- './spec/lib/constraints/jira_encoded_url_constrainer_spec.rb'
- './spec/lib/constraints/project_url_constrainer_spec.rb'
- './spec/lib/constraints/user_url_constrainer_spec.rb'
- './spec/lib/container_registry/blob_spec.rb'
- './spec/lib/container_registry/client_spec.rb'
- './spec/lib/container_registry/gitlab_api_client_spec.rb'
- './spec/lib/container_registry/path_spec.rb'
- './spec/lib/container_registry/registry_spec.rb'
- './spec/lib/declarative_enum_spec.rb'
- './spec/lib/error_tracking/stacktrace_builder_spec.rb'
- './spec/lib/event_filter_spec.rb'
- './spec/lib/expand_variables_spec.rb'
- './spec/lib/extracts_path_spec.rb'
- './spec/lib/feature/definition_spec.rb'
- './spec/lib/feature/gitaly_spec.rb'
- './spec/lib/feature_spec.rb'
- './spec/lib/file_size_validator_spec.rb'
- './spec/lib/forever_spec.rb'
- './spec/lib/generators/gitlab/usage_metric_definition_generator_spec.rb'
- './spec/lib/generators/gitlab/usage_metric_definition/redis_hll_generator_spec.rb'
- './spec/lib/generators/gitlab/usage_metric_generator_spec.rb'
- './spec/lib/generators/model/model_generator_spec.rb'
- './spec/lib/gitaly/server_spec.rb'
- './spec/lib/gitlab/access/branch_protection_spec.rb'
- './spec/lib/gitlab/action_cable/request_store_callbacks_spec.rb'
- './spec/lib/gitlab/alert_management/alert_status_counts_spec.rb'
- './spec/lib/gitlab/alert_management/fingerprint_spec.rb'
- './spec/lib/gitlab/alert_management/payload/base_spec.rb'
- './spec/lib/gitlab/alert_management/payload/generic_spec.rb'
- './spec/lib/gitlab/alert_management/payload/prometheus_spec.rb'
- './spec/lib/gitlab/alert_management/payload_spec.rb'
- './spec/lib/gitlab/allowable_spec.rb'
- './spec/lib/gitlab/anonymous_session_spec.rb'
- './spec/lib/gitlab/api_authentication/builder_spec.rb'
- './spec/lib/gitlab/api_authentication/sent_through_builder_spec.rb'
- './spec/lib/gitlab/api_authentication/token_locator_spec.rb'
- './spec/lib/gitlab/api_authentication/token_resolver_spec.rb'
- './spec/lib/gitlab/api_authentication/token_type_builder_spec.rb'
- './spec/lib/gitlab/app_json_logger_spec.rb'
- './spec/lib/gitlab/application_context_spec.rb'
- './spec/lib/gitlab/application_rate_limiter/base_strategy_spec.rb'
- './spec/lib/gitlab/application_rate_limiter/increment_per_actioned_resource_spec.rb'
- './spec/lib/gitlab/application_rate_limiter/increment_per_action_spec.rb'
- './spec/lib/gitlab/application_rate_limiter_spec.rb'
- './spec/lib/gitlab/app_logger_spec.rb'
- './spec/lib/gitlab/app_text_logger_spec.rb'
- './spec/lib/gitlab/asciidoc/html5_converter_spec.rb'
- './spec/lib/gitlab/asciidoc/include_processor_spec.rb'
- './spec/lib/gitlab/asciidoc_spec.rb'
- './spec/lib/gitlab/asset_proxy_spec.rb'
- './spec/lib/gitlab/audit/auditor_spec.rb'
- './spec/lib/gitlab/audit/ci_runner_token_author_spec.rb'
- './spec/lib/gitlab/audit/deploy_key_author_spec.rb'
- './spec/lib/gitlab/audit/deploy_token_author_spec.rb'
- './spec/lib/gitlab/audit/null_author_spec.rb'
- './spec/lib/gitlab/audit/null_target_spec.rb'
- './spec/lib/gitlab/audit/target_spec.rb'
- './spec/lib/gitlab/audit/unauthenticated_author_spec.rb'
- './spec/lib/gitlab/auth/activity_spec.rb'
- './spec/lib/gitlab/auth/atlassian/auth_hash_spec.rb'
- './spec/lib/gitlab/auth/atlassian/identity_linker_spec.rb'
- './spec/lib/gitlab/auth/atlassian/user_spec.rb'
- './spec/lib/gitlab/auth/auth_finders_spec.rb'
- './spec/lib/gitlab/auth/blocked_user_tracker_spec.rb'
- './spec/lib/gitlab/auth/crowd/authentication_spec.rb'
- './spec/lib/gitlab/auth/current_user_mode_spec.rb'
- './spec/lib/gitlab/auth/ip_rate_limiter_spec.rb'
- './spec/lib/gitlab/auth/key_status_checker_spec.rb'
- './spec/lib/gitlab/auth/ldap/access_spec.rb'
- './spec/lib/gitlab/auth/ldap/adapter_spec.rb'
- './spec/lib/gitlab/auth/ldap/authentication_spec.rb'
- './spec/lib/gitlab/auth/ldap/auth_hash_spec.rb'
- './spec/lib/gitlab/auth/ldap/config_spec.rb'
- './spec/lib/gitlab/auth/ldap/dn_spec.rb'
- './spec/lib/gitlab/auth/ldap/person_spec.rb'
- './spec/lib/gitlab/auth/ldap/user_spec.rb'
- './spec/lib/gitlab/auth/o_auth/auth_hash_spec.rb'
- './spec/lib/gitlab/auth/o_auth/identity_linker_spec.rb'
- './spec/lib/gitlab/auth/o_auth/provider_spec.rb'
- './spec/lib/gitlab/auth/o_auth/user_spec.rb'
- './spec/lib/gitlab/authorized_keys_spec.rb'
- './spec/lib/gitlab/auth/otp/strategies/devise_spec.rb'
- './spec/lib/gitlab/auth/otp/strategies/forti_authenticator/manual_otp_spec.rb'
- './spec/lib/gitlab/auth/otp/strategies/forti_authenticator/push_otp_spec.rb'
- './spec/lib/gitlab/auth/otp/strategies/forti_token_cloud_spec.rb'
- './spec/lib/gitlab/auth/request_authenticator_spec.rb'
- './spec/lib/gitlab/auth/result_spec.rb'
- './spec/lib/gitlab/auth/saml/auth_hash_spec.rb'
- './spec/lib/gitlab/auth/saml/config_spec.rb'
- './spec/lib/gitlab/auth/saml/identity_linker_spec.rb'
- './spec/lib/gitlab/auth/saml/origin_validator_spec.rb'
- './spec/lib/gitlab/auth/saml/user_spec.rb'
- './spec/lib/gitlab/auth_spec.rb'
- './spec/lib/gitlab/auth/two_factor_auth_verifier_spec.rb'
- './spec/lib/gitlab/auth/unique_ips_limiter_spec.rb'
- './spec/lib/gitlab/auth/user_access_denied_reason_spec.rb'
- './spec/lib/gitlab/avatar_cache_spec.rb'
- './spec/lib/gitlab/background_migration/backfill_namespace_id_of_vulnerability_reads_spec.rb'
- './spec/lib/gitlab/background_migration/backfill_note_discussion_id_spec.rb'
- './spec/lib/gitlab/background_migration/backfill_project_feature_package_registry_access_level_spec.rb'
- './spec/lib/gitlab/background_migration/backfill_project_repositories_spec.rb'
- './spec/lib/gitlab/background_migration/backfill_vulnerability_reads_cluster_agent_spec.rb'
- './spec/lib/gitlab/background_migration/base_job_spec.rb'
- './spec/lib/gitlab/background_migration/batched_migration_job_spec.rb'
- './spec/lib/gitlab/background_migration/batching_strategies/backfill_project_statistics_with_container_registry_size_batching_strategy_spec.rb'
- './spec/lib/gitlab/background_migration/batching_strategies/base_strategy_spec.rb'
- './spec/lib/gitlab/background_migration/batching_strategies/dismissed_vulnerabilities_strategy_spec.rb'
- './spec/lib/gitlab/background_migration/batching_strategies/loose_index_scan_batching_strategy_spec.rb'
- './spec/lib/gitlab/background_migration/batching_strategies/primary_key_batching_strategy_spec.rb'
- './spec/lib/gitlab/background_migration/disable_legacy_open_source_license_for_inactive_public_projects_spec.rb'
- './spec/lib/gitlab/background_migration/disable_legacy_open_source_license_for_one_member_no_repo_projects_spec.rb'
- './spec/lib/gitlab/background_migration/job_coordinator_spec.rb'
- './spec/lib/gitlab/background_migration/mailers/unconfirm_mailer_spec.rb'
- './spec/lib/gitlab/background_migration/set_correct_vulnerability_state_spec.rb'
- './spec/lib/gitlab/background_migration/set_legacy_open_source_license_available_for_non_public_projects_spec.rb'
- './spec/lib/gitlab/background_migration_spec.rb'
- './spec/lib/gitlab/background_migration/update_jira_tracker_data_deployment_type_based_on_url_spec.rb'
- './spec/lib/gitlab/background_task_spec.rb'
- './spec/lib/gitlab/backtrace_cleaner_spec.rb'
- './spec/lib/gitlab/batch_worker_context_spec.rb'
- './spec/lib/gitlab/bitbucket_import/project_creator_spec.rb'
- './spec/lib/gitlab/bitbucket_import/wiki_formatter_spec.rb'
- './spec/lib/gitlab/blame_spec.rb'
- './spec/lib/gitlab/blob_helper_spec.rb'
- './spec/lib/gitlab/branch_push_merge_commit_analyzer_spec.rb'
- './spec/lib/gitlab/build_access_spec.rb'
- './spec/lib/gitlab/bullet/exclusions_spec.rb'
- './spec/lib/gitlab/bullet_spec.rb'
- './spec/lib/gitlab/cache/ci/project_pipeline_status_spec.rb'
- './spec/lib/gitlab/cache/helpers_spec.rb'
- './spec/lib/gitlab/cache/import/caching_spec.rb'
- './spec/lib/gitlab/cache/request_cache_spec.rb'
- './spec/lib/gitlab/cache_spec.rb'
- './spec/lib/gitlab/changelog/committer_spec.rb'
- './spec/lib/gitlab/changelog/config_spec.rb'
- './spec/lib/gitlab/changelog/generator_spec.rb'
- './spec/lib/gitlab/changelog/release_spec.rb'
- './spec/lib/gitlab/changes_list_spec.rb'
- './spec/lib/gitlab/chat/command_spec.rb'
- './spec/lib/gitlab/chat_name_token_spec.rb'
- './spec/lib/gitlab/chat/output_spec.rb'
- './spec/lib/gitlab/chat/responder/base_spec.rb'
- './spec/lib/gitlab/chat/responder/mattermost_spec.rb'
- './spec/lib/gitlab/chat/responder/slack_spec.rb'
- './spec/lib/gitlab/chat/responder_spec.rb'
- './spec/lib/gitlab/checks/branch_check_spec.rb'
- './spec/lib/gitlab/checks/changes_access_spec.rb'
- './spec/lib/gitlab/checks/container_moved_spec.rb'
- './spec/lib/gitlab/checks/diff_check_spec.rb'
- './spec/lib/gitlab/checks/force_push_spec.rb'
- './spec/lib/gitlab/checks/lfs_check_spec.rb'
- './spec/lib/gitlab/checks/lfs_integrity_spec.rb'
- './spec/lib/gitlab/checks/matching_merge_request_spec.rb'
- './spec/lib/gitlab/checks/project_created_spec.rb'
- './spec/lib/gitlab/checks/push_check_spec.rb'
- './spec/lib/gitlab/checks/push_file_count_check_spec.rb'
- './spec/lib/gitlab/checks/single_change_access_spec.rb'
- './spec/lib/gitlab/checks/snippet_check_spec.rb'
- './spec/lib/gitlab/checks/tag_check_spec.rb'
- './spec/lib/gitlab/checks/timed_logger_spec.rb'
- './spec/lib/gitlab/ci_access_spec.rb'
- './spec/lib/gitlab/ci/ansi2html_spec.rb'
- './spec/lib/gitlab/ci/ansi2json/line_spec.rb'
- './spec/lib/gitlab/ci/ansi2json/parser_spec.rb'
- './spec/lib/gitlab/ci/ansi2json/result_spec.rb'
- './spec/lib/gitlab/ci/ansi2json_spec.rb'
- './spec/lib/gitlab/ci/ansi2json/style_spec.rb'
- './spec/lib/gitlab/ci/artifact_file_reader_spec.rb'
- './spec/lib/gitlab/ci/artifacts/logger_spec.rb'
- './spec/lib/gitlab/ci/artifacts/metrics_spec.rb'
- './spec/lib/gitlab/ci/badge/coverage/metadata_spec.rb'
- './spec/lib/gitlab/ci/badge/coverage/report_spec.rb'
- './spec/lib/gitlab/ci/badge/coverage/template_spec.rb'
- './spec/lib/gitlab/ci/badge/pipeline/metadata_spec.rb'
- './spec/lib/gitlab/ci/badge/pipeline/status_spec.rb'
- './spec/lib/gitlab/ci/badge/pipeline/template_spec.rb'
- './spec/lib/gitlab/ci/badge/release/latest_release_spec.rb'
- './spec/lib/gitlab/ci/badge/release/metadata_spec.rb'
- './spec/lib/gitlab/ci/badge/release/template_spec.rb'
- './spec/lib/gitlab/ci/build/artifacts/adapters/gzip_stream_spec.rb'
- './spec/lib/gitlab/ci/build/artifacts/adapters/raw_stream_spec.rb'
- './spec/lib/gitlab/ci/build/artifacts/metadata/entry_spec.rb'
- './spec/lib/gitlab/ci/build/artifacts/metadata_spec.rb'
- './spec/lib/gitlab/ci/build/artifacts/path_spec.rb'
- './spec/lib/gitlab/ci/build/auto_retry_spec.rb'
- './spec/lib/gitlab/ci/build/cache_spec.rb'
- './spec/lib/gitlab/ci/build/context/build_spec.rb'
- './spec/lib/gitlab/ci/build/context/global_spec.rb'
- './spec/lib/gitlab/ci/build/credentials/factory_spec.rb'
- './spec/lib/gitlab/ci/build/credentials/registry/dependency_proxy_spec.rb'
- './spec/lib/gitlab/ci/build/credentials/registry/gitlab_registry_spec.rb'
- './spec/lib/gitlab/ci/build/duration_parser_spec.rb'
- './spec/lib/gitlab/ci/build/image_spec.rb'
- './spec/lib/gitlab/ci/build/policy/changes_spec.rb'
- './spec/lib/gitlab/ci/build/policy/kubernetes_spec.rb'
- './spec/lib/gitlab/ci/build/policy/refs_spec.rb'
- './spec/lib/gitlab/ci/build/policy_spec.rb'
- './spec/lib/gitlab/ci/build/policy/variables_spec.rb'
- './spec/lib/gitlab/ci/build/port_spec.rb'
- './spec/lib/gitlab/ci/build/prerequisite/factory_spec.rb'
- './spec/lib/gitlab/ci/build/prerequisite/kubernetes_namespace_spec.rb'
- './spec/lib/gitlab/ci/build/releaser_spec.rb'
- './spec/lib/gitlab/ci/build/rules/rule/clause/changes_spec.rb'
- './spec/lib/gitlab/ci/build/rules/rule/clause/exists_spec.rb'
- './spec/lib/gitlab/ci/build/rules/rule/clause/if_spec.rb'
- './spec/lib/gitlab/ci/build/rules/rule/clause_spec.rb'
- './spec/lib/gitlab/ci/build/rules/rule_spec.rb'
- './spec/lib/gitlab/ci/build/rules_spec.rb'
- './spec/lib/gitlab/ci/build/status/reason_spec.rb'
- './spec/lib/gitlab/ci/build/step_spec.rb'
- './spec/lib/gitlab/ci/charts_spec.rb'
- './spec/lib/gitlab/ci/config/entry/allow_failure_spec.rb'
- './spec/lib/gitlab/ci/config/entry/artifacts_spec.rb'
- './spec/lib/gitlab/ci/config/entry/bridge_spec.rb'
- './spec/lib/gitlab/ci/config/entry/cache_spec.rb'
- './spec/lib/gitlab/ci/config/entry/caches_spec.rb'
- './spec/lib/gitlab/ci/config/entry/commands_spec.rb'
- './spec/lib/gitlab/ci/config/entry/coverage_spec.rb'
- './spec/lib/gitlab/ci/config/entry/default_spec.rb'
- './spec/lib/gitlab/ci/config/entry/environment_spec.rb'
- './spec/lib/gitlab/ci/config/entry/files_spec.rb'
- './spec/lib/gitlab/ci/config/entry/hidden_spec.rb'
- './spec/lib/gitlab/ci/config/entry/imageable_spec.rb'
- './spec/lib/gitlab/ci/config/entry/image_spec.rb'
- './spec/lib/gitlab/ci/config/entry/include/rules/rule_spec.rb'
- './spec/lib/gitlab/ci/config/entry/include/rules_spec.rb'
- './spec/lib/gitlab/ci/config/entry/include_spec.rb'
- './spec/lib/gitlab/ci/config/entry/inherit/default_spec.rb'
- './spec/lib/gitlab/ci/config/entry/inherit/variables_spec.rb'
- './spec/lib/gitlab/ci/config/entry/job_spec.rb'
- './spec/lib/gitlab/ci/config/entry/jobs_spec.rb'
- './spec/lib/gitlab/ci/config/entry/key_spec.rb'
- './spec/lib/gitlab/ci/config/entry/kubernetes_spec.rb'
- './spec/lib/gitlab/ci/config/entry/need_spec.rb'
- './spec/lib/gitlab/ci/config/entry/needs_spec.rb'
- './spec/lib/gitlab/ci/config/entry/paths_spec.rb'
- './spec/lib/gitlab/ci/config/entry/policy_spec.rb'
- './spec/lib/gitlab/ci/config/entry/port_spec.rb'
- './spec/lib/gitlab/ci/config/entry/ports_spec.rb'
- './spec/lib/gitlab/ci/config/entry/prefix_spec.rb'
- './spec/lib/gitlab/ci/config/entry/processable_spec.rb'
- './spec/lib/gitlab/ci/config/entry/product/matrix_spec.rb'
- './spec/lib/gitlab/ci/config/entry/product/parallel_spec.rb'
- './spec/lib/gitlab/ci/config/entry/product/variables_spec.rb'
- './spec/lib/gitlab/ci/config/entry/pull_policy_spec.rb'
- './spec/lib/gitlab/ci/config/entry/release/assets/link_spec.rb'
- './spec/lib/gitlab/ci/config/entry/release/assets/links_spec.rb'
- './spec/lib/gitlab/ci/config/entry/release/assets_spec.rb'
- './spec/lib/gitlab/ci/config/entry/release_spec.rb'
- './spec/lib/gitlab/ci/config/entry/reports/coverage_report_spec.rb'
- './spec/lib/gitlab/ci/config/entry/reports_spec.rb'
- './spec/lib/gitlab/ci/config/entry/retry_spec.rb'
- './spec/lib/gitlab/ci/config/entry/root_spec.rb'
- './spec/lib/gitlab/ci/config/entry/rules/rule/changes_spec.rb'
- './spec/lib/gitlab/ci/config/entry/rules/rule_spec.rb'
- './spec/lib/gitlab/ci/config/entry/rules_spec.rb'
- './spec/lib/gitlab/ci/config/entry/service_spec.rb'
- './spec/lib/gitlab/ci/config/entry/services_spec.rb'
- './spec/lib/gitlab/ci/config/entry/stage_spec.rb'
- './spec/lib/gitlab/ci/config/entry/stages_spec.rb'
- './spec/lib/gitlab/ci/config/entry/tags_spec.rb'
- './spec/lib/gitlab/ci/config/entry/trigger/forward_spec.rb'
- './spec/lib/gitlab/ci/config/entry/trigger_spec.rb'
- './spec/lib/gitlab/ci/config/entry/variables_spec.rb'
- './spec/lib/gitlab/ci/config/entry/workflow_spec.rb'
- './spec/lib/gitlab/ci/config/extendable/entry_spec.rb'
- './spec/lib/gitlab/ci/config/extendable_spec.rb'
- './spec/lib/gitlab/ci/config/external/context_spec.rb'
- './spec/lib/gitlab/ci/config/external/file/artifact_spec.rb'
- './spec/lib/gitlab/ci/config/external/file/base_spec.rb'
- './spec/lib/gitlab/ci/config/external/file/local_spec.rb'
- './spec/lib/gitlab/ci/config/external/file/project_spec.rb'
- './spec/lib/gitlab/ci/config/external/file/remote_spec.rb'
- './spec/lib/gitlab/ci/config/external/file/template_spec.rb'
- './spec/lib/gitlab/ci/config/external/mapper_spec.rb'
- './spec/lib/gitlab/ci/config/external/processor_spec.rb'
- './spec/lib/gitlab/ci/config/external/rules_spec.rb'
- './spec/lib/gitlab/ci/config/normalizer/factory_spec.rb'
- './spec/lib/gitlab/ci/config/normalizer/matrix_strategy_spec.rb'
- './spec/lib/gitlab/ci/config/normalizer/number_strategy_spec.rb'
- './spec/lib/gitlab/ci/config/normalizer_spec.rb'
- './spec/lib/gitlab/ci/config_spec.rb'
- './spec/lib/gitlab/ci/config/yaml/tags/reference_spec.rb'
- './spec/lib/gitlab/ci/config/yaml/tags/resolver_spec.rb'
- './spec/lib/gitlab/ci/cron_parser_spec.rb'
- './spec/lib/gitlab/ci/jwt_spec.rb'
- './spec/lib/gitlab/ci/jwt_v2_spec.rb'
- './spec/lib/gitlab/ci/lint_spec.rb'
- './spec/lib/gitlab/ci/mask_secret_spec.rb'
- './spec/lib/gitlab/ci/matching/build_matcher_spec.rb'
- './spec/lib/gitlab/ci/matching/runner_matcher_spec.rb'
- './spec/lib/gitlab/ci/parsers/accessibility/pa11y_spec.rb'
- './spec/lib/gitlab/ci/parsers/codequality/code_climate_spec.rb'
- './spec/lib/gitlab/ci/parsers/coverage/cobertura_spec.rb'
- './spec/lib/gitlab/ci/parsers/instrumentation_spec.rb'
- './spec/lib/gitlab/ci/parsers/sbom/cyclonedx_properties_spec.rb'
- './spec/lib/gitlab/ci/parsers/sbom/cyclonedx_spec.rb'
- './spec/lib/gitlab/ci/parsers/sbom/source/dependency_scanning_spec.rb'
- './spec/lib/gitlab/ci/parsers/sbom/validators/cyclonedx_schema_validator_spec.rb'
- './spec/lib/gitlab/ci/parsers/security/common_spec.rb'
- './spec/lib/gitlab/ci/parsers/security/sast_spec.rb'
- './spec/lib/gitlab/ci/parsers/security/secret_detection_spec.rb'
- './spec/lib/gitlab/ci/parsers_spec.rb'
- './spec/lib/gitlab/ci/parsers/terraform/tfplan_spec.rb'
- './spec/lib/gitlab/ci/parsers/test/junit_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/build/associations_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/build_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/cancel_pending_pipelines_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/command_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/config/content_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/create_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/ensure_environments_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/ensure_resource_groups_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/evaluate_workflow_rules_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/limit/deployments_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/limit/rate_limit_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/pipeline/process_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/populate_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/remove_unwanted_chat_jobs_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/seed_block_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/seed_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/sequence_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/skip_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/template_usage_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/validate/abilities_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/validate/external_spec.rb'
- './spec/lib/gitlab/ci/pipeline/chain/validate/repository_spec.rb'
- './spec/lib/gitlab/ci/pipeline/duration_spec.rb'
- './spec/lib/gitlab/ci/pipeline/expression/lexeme/and_spec.rb'
- './spec/lib/gitlab/ci/pipeline/expression/lexeme/equals_spec.rb'
- './spec/lib/gitlab/ci/pipeline/expression/lexeme/matches_spec.rb'
- './spec/lib/gitlab/ci/pipeline/expression/lexeme/not_equals_spec.rb'
- './spec/lib/gitlab/ci/pipeline/expression/lexeme/not_matches_spec.rb'
- './spec/lib/gitlab/ci/pipeline/expression/lexeme/null_spec.rb'
- './spec/lib/gitlab/ci/pipeline/expression/lexeme/or_spec.rb'
- './spec/lib/gitlab/ci/pipeline/expression/lexeme/pattern_spec.rb'
- './spec/lib/gitlab/ci/pipeline/expression/lexeme/string_spec.rb'
- './spec/lib/gitlab/ci/pipeline/expression/lexeme/variable_spec.rb'
- './spec/lib/gitlab/ci/pipeline/expression/lexer_spec.rb'
- './spec/lib/gitlab/ci/pipeline/expression/parser_spec.rb'
- './spec/lib/gitlab/ci/pipeline/expression/statement_spec.rb'
- './spec/lib/gitlab/ci/pipeline/expression/token_spec.rb'
- './spec/lib/gitlab/ci/pipeline/logger_spec.rb'
- './spec/lib/gitlab/ci/pipeline/metrics_spec.rb'
- './spec/lib/gitlab/ci/pipeline_object_hierarchy_spec.rb'
- './spec/lib/gitlab/ci/pipeline/preloader_spec.rb'
- './spec/lib/gitlab/ci/pipeline/quota/deployments_spec.rb'
- './spec/lib/gitlab/ci/pipeline/seed/build/cache_spec.rb'
- './spec/lib/gitlab/ci/pipeline/seed/build_spec.rb'
- './spec/lib/gitlab/ci/pipeline/seed/pipeline_spec.rb'
- './spec/lib/gitlab/ci/pipeline/seed/processable/resource_group_spec.rb'
- './spec/lib/gitlab/ci/pipeline/seed/stage_spec.rb'
- './spec/lib/gitlab/ci/reports/accessibility_reports_comparer_spec.rb'
- './spec/lib/gitlab/ci/reports/accessibility_reports_spec.rb'
- './spec/lib/gitlab/ci/reports/codequality_mr_diff_spec.rb'
- './spec/lib/gitlab/ci/reports/codequality_reports_comparer_spec.rb'
- './spec/lib/gitlab/ci/reports/codequality_reports_spec.rb'
- './spec/lib/gitlab/ci/reports/coverage_report_generator_spec.rb'
- './spec/lib/gitlab/ci/reports/coverage_report_spec.rb'
- './spec/lib/gitlab/ci/reports/reports_comparer_spec.rb'
- './spec/lib/gitlab/ci/reports/sbom/component_spec.rb'
- './spec/lib/gitlab/ci/reports/sbom/report_spec.rb'
- './spec/lib/gitlab/ci/reports/sbom/reports_spec.rb'
- './spec/lib/gitlab/ci/reports/sbom/source_spec.rb'
- './spec/lib/gitlab/ci/reports/security/aggregated_report_spec.rb'
- './spec/lib/gitlab/ci/reports/security/finding_key_spec.rb'
- './spec/lib/gitlab/ci/reports/security/finding_signature_spec.rb'
- './spec/lib/gitlab/ci/reports/security/flag_spec.rb'
- './spec/lib/gitlab/ci/reports/security/identifier_spec.rb'
- './spec/lib/gitlab/ci/reports/security/link_spec.rb'
- './spec/lib/gitlab/ci/reports/security/locations/sast_spec.rb'
- './spec/lib/gitlab/ci/reports/security/locations/secret_detection_spec.rb'
- './spec/lib/gitlab/ci/reports/security/report_spec.rb'
- './spec/lib/gitlab/ci/reports/security/reports_spec.rb'
- './spec/lib/gitlab/ci/reports/security/scanned_resource_spec.rb'
- './spec/lib/gitlab/ci/reports/security/scanner_spec.rb'
- './spec/lib/gitlab/ci/reports/security/scan_spec.rb'
- './spec/lib/gitlab/ci/reports/terraform_reports_spec.rb'
- './spec/lib/gitlab/ci/reports/test_case_spec.rb'
- './spec/lib/gitlab/ci/reports/test_failure_history_spec.rb'
- './spec/lib/gitlab/ci/reports/test_reports_comparer_spec.rb'
- './spec/lib/gitlab/ci/reports/test_report_spec.rb'
- './spec/lib/gitlab/ci/reports/test_report_summary_spec.rb'
- './spec/lib/gitlab/ci/reports/test_suite_comparer_spec.rb'
- './spec/lib/gitlab/ci/reports/test_suite_spec.rb'
- './spec/lib/gitlab/ci/reports/test_suite_summary_spec.rb'
- './spec/lib/gitlab/ci/runner/backoff_spec.rb'
- './spec/lib/gitlab/ci/runner_instructions_spec.rb'
- './spec/lib/gitlab/ci/runner/metrics_spec.rb'
- './spec/lib/gitlab/ci/runner_releases_spec.rb'
- './spec/lib/gitlab/ci/runner_upgrade_check_spec.rb'
- './spec/lib/gitlab/ci/status/bridge/common_spec.rb'
- './spec/lib/gitlab/ci/status/bridge/factory_spec.rb'
- './spec/lib/gitlab/ci/status/bridge/waiting_for_resource_spec.rb'
- './spec/lib/gitlab/ci/status/build/action_spec.rb'
- './spec/lib/gitlab/ci/status/build/cancelable_spec.rb'
- './spec/lib/gitlab/ci/status/build/canceled_spec.rb'
- './spec/lib/gitlab/ci/status/build/common_spec.rb'
- './spec/lib/gitlab/ci/status/build/created_spec.rb'
- './spec/lib/gitlab/ci/status/build/erased_spec.rb'
- './spec/lib/gitlab/ci/status/build/factory_spec.rb'
- './spec/lib/gitlab/ci/status/build/failed_allowed_spec.rb'
- './spec/lib/gitlab/ci/status/build/failed_spec.rb'
- './spec/lib/gitlab/ci/status/build/failed_unmet_prerequisites_spec.rb'
- './spec/lib/gitlab/ci/status/build/manual_spec.rb'
- './spec/lib/gitlab/ci/status/build/pending_spec.rb'
- './spec/lib/gitlab/ci/status/build/play_spec.rb'
- './spec/lib/gitlab/ci/status/build/preparing_spec.rb'
- './spec/lib/gitlab/ci/status/build/retried_spec.rb'
- './spec/lib/gitlab/ci/status/build/retryable_spec.rb'
- './spec/lib/gitlab/ci/status/build/scheduled_spec.rb'
- './spec/lib/gitlab/ci/status/build/skipped_spec.rb'
- './spec/lib/gitlab/ci/status/build/stop_spec.rb'
- './spec/lib/gitlab/ci/status/build/unschedule_spec.rb'
- './spec/lib/gitlab/ci/status/build/waiting_for_resource_spec.rb'
- './spec/lib/gitlab/ci/status/canceled_spec.rb'
- './spec/lib/gitlab/ci/status/composite_spec.rb'
- './spec/lib/gitlab/ci/status/core_spec.rb'
- './spec/lib/gitlab/ci/status/created_spec.rb'
- './spec/lib/gitlab/ci/status/extended_spec.rb'
- './spec/lib/gitlab/ci/status/external/common_spec.rb'
- './spec/lib/gitlab/ci/status/external/factory_spec.rb'
- './spec/lib/gitlab/ci/status/factory_spec.rb'
- './spec/lib/gitlab/ci/status/failed_spec.rb'
- './spec/lib/gitlab/ci/status/group/common_spec.rb'
- './spec/lib/gitlab/ci/status/group/factory_spec.rb'
- './spec/lib/gitlab/ci/status/manual_spec.rb'
- './spec/lib/gitlab/ci/status/pending_spec.rb'
- './spec/lib/gitlab/ci/status/pipeline/blocked_spec.rb'
- './spec/lib/gitlab/ci/status/pipeline/common_spec.rb'
- './spec/lib/gitlab/ci/status/pipeline/delayed_spec.rb'
- './spec/lib/gitlab/ci/status/pipeline/factory_spec.rb'
- './spec/lib/gitlab/ci/status/preparing_spec.rb'
- './spec/lib/gitlab/ci/status/processable/waiting_for_resource_spec.rb'
- './spec/lib/gitlab/ci/status/running_spec.rb'
- './spec/lib/gitlab/ci/status/scheduled_spec.rb'
- './spec/lib/gitlab/ci/status/skipped_spec.rb'
- './spec/lib/gitlab/ci/status/stage/common_spec.rb'
- './spec/lib/gitlab/ci/status/stage/factory_spec.rb'
- './spec/lib/gitlab/ci/status/stage/play_manual_spec.rb'
- './spec/lib/gitlab/ci/status/success_spec.rb'
- './spec/lib/gitlab/ci/status/success_warning_spec.rb'
- './spec/lib/gitlab/ci/status/waiting_for_resource_spec.rb'
- './spec/lib/gitlab/ci/tags/bulk_insert_spec.rb'
- './spec/lib/gitlab/ci/templates/auto_devops_gitlab_ci_yaml_spec.rb'
- './spec/lib/gitlab/ci/templates/AWS/deploy_ecs_gitlab_ci_yaml_spec.rb'
- './spec/lib/gitlab/ci/templates/flutter_gitlab_ci_yaml_spec.rb'
- './spec/lib/gitlab/ci/templates/Jobs/build_gitlab_ci_yaml_spec.rb'
- './spec/lib/gitlab/ci/templates/Jobs/code_quality_gitlab_ci_yaml_spec.rb'
- './spec/lib/gitlab/ci/templates/Jobs/deploy_gitlab_ci_yaml_spec.rb'
- './spec/lib/gitlab/ci/templates/Jobs/sast_iac_gitlab_ci_yaml_spec.rb'
- './spec/lib/gitlab/ci/templates/Jobs/sast_iac_latest_gitlab_ci_yaml_spec.rb'
- './spec/lib/gitlab/ci/templates/Jobs/test_gitlab_ci_yaml_spec.rb'
- './spec/lib/gitlab/ci/templates/kaniko_gitlab_ci_yaml_spec.rb'
- './spec/lib/gitlab/ci/templates/MATLAB_spec.rb'
- './spec/lib/gitlab/ci/templates/npm_spec.rb'
- './spec/lib/gitlab/ci/templates/templates_spec.rb'
- './spec/lib/gitlab/ci/templates/themekit_gitlab_ci_yaml_spec.rb'
- './spec/lib/gitlab/ci/templates/Verify/load_performance_testing_gitlab_ci_yaml_spec.rb'
- './spec/lib/gitlab/ci/trace/archive_spec.rb'
- './spec/lib/gitlab/ci/trace/backoff_spec.rb'
- './spec/lib/gitlab/ci/trace/checksum_spec.rb'
- './spec/lib/gitlab/ci/trace/chunked_io_spec.rb'
- './spec/lib/gitlab/ci/trace/metrics_spec.rb'
- './spec/lib/gitlab/ci/trace/remote_checksum_spec.rb'
- './spec/lib/gitlab/ci/trace/section_parser_spec.rb'
- './spec/lib/gitlab/ci/trace_spec.rb'
- './spec/lib/gitlab/ci/trace/stream_spec.rb'
- './spec/lib/gitlab/ci/variables/builder/group_spec.rb'
- './spec/lib/gitlab/ci/variables/builder/instance_spec.rb'
- './spec/lib/gitlab/ci/variables/builder/project_spec.rb'
- './spec/lib/gitlab/ci/variables/builder_spec.rb'
- './spec/lib/gitlab/ci/variables/collection/item_spec.rb'
- './spec/lib/gitlab/ci/variables/collection/sort_spec.rb'
- './spec/lib/gitlab/ci/variables/collection_spec.rb'
- './spec/lib/gitlab/ci/variables/helpers_spec.rb'
- './spec/lib/gitlab/ci/yaml_processor/dag_spec.rb'
- './spec/lib/gitlab/ci/yaml_processor/result_spec.rb'
- './spec/lib/gitlab/ci/yaml_processor_spec.rb'
- './spec/lib/gitlab/class_attributes_spec.rb'
- './spec/lib/gitlab/cleanup/orphan_job_artifact_files_batch_spec.rb'
- './spec/lib/gitlab/cleanup/orphan_job_artifact_files_spec.rb'
- './spec/lib/gitlab/cleanup/orphan_lfs_file_references_spec.rb'
- './spec/lib/gitlab/cleanup/project_uploads_spec.rb'
- './spec/lib/gitlab/cleanup/remote_uploads_spec.rb'
- './spec/lib/gitlab/closing_issue_extractor_spec.rb'
- './spec/lib/gitlab/cluster/lifecycle_events_spec.rb'
- './spec/lib/gitlab/cluster/mixins/puma_cluster_spec.rb'
- './spec/lib/gitlab/cluster/rack_timeout_observer_spec.rb'
- './spec/lib/gitlab/code_navigation_path_spec.rb'
- './spec/lib/gitlab/color_schemes_spec.rb'
- './spec/lib/gitlab/color_spec.rb'
- './spec/lib/gitlab/composer/version_index_spec.rb'
- './spec/lib/gitlab/conan_token_spec.rb'
- './spec/lib/gitlab/config_checker/external_database_checker_spec.rb'
- './spec/lib/gitlab/config/entry/attributable_spec.rb'
- './spec/lib/gitlab/config/entry/boolean_spec.rb'
- './spec/lib/gitlab/config/entry/composable_array_spec.rb'
- './spec/lib/gitlab/config/entry/composable_hash_spec.rb'
- './spec/lib/gitlab/config/entry/configurable_spec.rb'
- './spec/lib/gitlab/config/entry/factory_spec.rb'
- './spec/lib/gitlab/config/entry/simplifiable_spec.rb'
- './spec/lib/gitlab/config/entry/undefined_spec.rb'
- './spec/lib/gitlab/config/entry/unspecified_spec.rb'
- './spec/lib/gitlab/config/entry/validatable_spec.rb'
- './spec/lib/gitlab/config/entry/validators/nested_array_helpers_spec.rb'
- './spec/lib/gitlab/config/entry/validator_spec.rb'
- './spec/lib/gitlab/config/entry/validators_spec.rb'
- './spec/lib/gitlab/config/loader/yaml_spec.rb'
- './spec/lib/gitlab/conflict/file_collection_spec.rb'
- './spec/lib/gitlab/conflict/file_spec.rb'
- './spec/lib/gitlab/console_spec.rb'
- './spec/lib/gitlab/consul/internal_spec.rb'
- './spec/lib/gitlab/container_repository/tags/cache_spec.rb'
- './spec/lib/gitlab/content_security_policy/config_loader_spec.rb'
- './spec/lib/gitlab/contributions_calendar_spec.rb'
- './spec/lib/gitlab/cross_project_access/check_collection_spec.rb'
- './spec/lib/gitlab/cross_project_access/check_info_spec.rb'
- './spec/lib/gitlab/cross_project_access/class_methods_spec.rb'
- './spec/lib/gitlab/cross_project_access_spec.rb'
- './spec/lib/gitlab/crypto_helper_spec.rb'
- './spec/lib/gitlab/current_settings_spec.rb'
- './spec/lib/gitlab/cycle_analytics/permissions_spec.rb'
- './spec/lib/gitlab/cycle_analytics/stage_summary_spec.rb'
- './spec/lib/gitlab/cycle_analytics/summary/value_spec.rb'
- './spec/lib/gitlab/cycle_analytics/updater_spec.rb'
- './spec/lib/gitlab/daemon_spec.rb'
- './spec/lib/gitlab/database/async_indexes/index_creator_spec.rb'
- './spec/lib/gitlab/database/async_indexes/index_destructor_spec.rb'
- './spec/lib/gitlab/database/async_indexes/migration_helpers_spec.rb'
- './spec/lib/gitlab/database/async_indexes/postgres_async_index_spec.rb'
- './spec/lib/gitlab/database/async_indexes_spec.rb'
- './spec/lib/gitlab/database/background_migration/batched_job_spec.rb'
- './spec/lib/gitlab/database/background_migration/batched_job_transition_log_spec.rb'
- './spec/lib/gitlab/database/background_migration/batched_migration_runner_spec.rb'
- './spec/lib/gitlab/database/background_migration/batched_migration_spec.rb'
- './spec/lib/gitlab/database/background_migration/batched_migration_wrapper_spec.rb'
- './spec/lib/gitlab/database/background_migration/batch_metrics_spec.rb'
- './spec/lib/gitlab/database/background_migration/batch_optimizer_spec.rb'
- './spec/lib/gitlab/database/background_migration/prometheus_metrics_spec.rb'
- './spec/lib/gitlab/database/batch_count_spec.rb'
- './spec/lib/gitlab/database/bulk_update_spec.rb'
- './spec/lib/gitlab/database/connection_timer_spec.rb'
- './spec/lib/gitlab/database/consistency_checker_spec.rb'
- './spec/lib/gitlab/database/consistency_spec.rb'
- './spec/lib/gitlab/database/count/exact_count_strategy_spec.rb'
- './spec/lib/gitlab/database/count/reltuples_count_strategy_spec.rb'
- './spec/lib/gitlab/database/count_spec.rb'
- './spec/lib/gitlab/database/count/tablesample_count_strategy_spec.rb'
- './spec/lib/gitlab/database/dynamic_model_helpers_spec.rb'
- './spec/lib/gitlab/database/each_database_spec.rb'
- './spec/lib/gitlab/database/gitlab_schema_spec.rb'
- './spec/lib/gitlab/database/grant_spec.rb'
- './spec/lib/gitlab/database_importers/work_items/base_type_importer_spec.rb'
- './spec/lib/gitlab/database/load_balancing/action_cable_callbacks_spec.rb'
- './spec/lib/gitlab/database/load_balancing/configuration_spec.rb'
- './spec/lib/gitlab/database/load_balancing/connection_proxy_spec.rb'
- './spec/lib/gitlab/database/load_balancing/host_list_spec.rb'
- './spec/lib/gitlab/database/load_balancing/host_spec.rb'
- './spec/lib/gitlab/database/load_balancing/load_balancer_spec.rb'
- './spec/lib/gitlab/database/load_balancing/primary_host_spec.rb'
- './spec/lib/gitlab/database/load_balancing/rack_middleware_spec.rb'
- './spec/lib/gitlab/database/load_balancing/resolver_spec.rb'
- './spec/lib/gitlab/database/load_balancing/service_discovery_spec.rb'
- './spec/lib/gitlab/database/load_balancing/session_spec.rb'
- './spec/lib/gitlab/database/load_balancing/setup_spec.rb'
- './spec/lib/gitlab/database/load_balancing/sidekiq_client_middleware_spec.rb'
- './spec/lib/gitlab/database/load_balancing/sidekiq_server_middleware_spec.rb'
- './spec/lib/gitlab/database/load_balancing_spec.rb'
- './spec/lib/gitlab/database/load_balancing/srv_resolver_spec.rb'
- './spec/lib/gitlab/database/load_balancing/sticking_spec.rb'
- './spec/lib/gitlab/database/load_balancing/transaction_leaking_spec.rb'
- './spec/lib/gitlab/database/lock_writes_manager_spec.rb'
- './spec/lib/gitlab/database/loose_foreign_keys_spec.rb'
- './spec/lib/gitlab/database/migration_helpers/announce_database_spec.rb'
- './spec/lib/gitlab/database/migration_helpers/cascading_namespace_settings_spec.rb'
- './spec/lib/gitlab/database/migration_helpers/loose_foreign_key_helpers_spec.rb'
- './spec/lib/gitlab/database/migration_helpers/restrict_gitlab_schema_spec.rb'
- './spec/lib/gitlab/database/migration_helpers_spec.rb'
- './spec/lib/gitlab/database/migration_helpers/v2_spec.rb'
- './spec/lib/gitlab/database/migrations/base_background_runner_spec.rb'
- './spec/lib/gitlab/database/migrations/batched_background_migration_helpers_spec.rb'
- './spec/lib/gitlab/database/migrations/instrumentation_spec.rb'
- './spec/lib/gitlab/database/migrations/lock_retry_mixin_spec.rb'
- './spec/lib/gitlab/database/migrations/observers/query_details_spec.rb'
- './spec/lib/gitlab/database/migrations/observers/query_log_spec.rb'
- './spec/lib/gitlab/database/migrations/observers/query_statistics_spec.rb'
- './spec/lib/gitlab/database/migrations/observers/total_database_size_change_spec.rb'
- './spec/lib/gitlab/database/migrations/observers/transaction_duration_spec.rb'
- './spec/lib/gitlab/database/migration_spec.rb'
- './spec/lib/gitlab/database/migrations/reestablished_connection_stack_spec.rb'
- './spec/lib/gitlab/database/migrations/runner_spec.rb'
- './spec/lib/gitlab/database/migrations/test_batched_background_runner_spec.rb'
- './spec/lib/gitlab/database/no_cross_db_foreign_keys_spec.rb'
- './spec/lib/gitlab/database/partitioning/detached_partition_dropper_spec.rb'
- './spec/lib/gitlab/database/partitioning_migration_helpers/foreign_key_helpers_spec.rb'
- './spec/lib/gitlab/database/partitioning_migration_helpers/index_helpers_spec.rb'
- './spec/lib/gitlab/database/partitioning_migration_helpers/table_management_helpers_spec.rb'
- './spec/lib/gitlab/database/partitioning/monthly_strategy_spec.rb'
- './spec/lib/gitlab/database/partitioning/partition_manager_spec.rb'
- './spec/lib/gitlab/database/partitioning/partition_monitoring_spec.rb'
- './spec/lib/gitlab/database/partitioning/replace_table_spec.rb'
- './spec/lib/gitlab/database/partitioning/single_numeric_list_partition_spec.rb'
- './spec/lib/gitlab/database/partitioning/sliding_list_strategy_spec.rb'
- './spec/lib/gitlab/database/partitioning_spec.rb'
- './spec/lib/gitlab/database/partitioning/time_partition_spec.rb'
- './spec/lib/gitlab/database/pg_class_spec.rb'
- './spec/lib/gitlab/database/postgres_autovacuum_activity_spec.rb'
- './spec/lib/gitlab/database/postgres_foreign_key_spec.rb'
- './spec/lib/gitlab/database/postgres_hll/batch_distinct_counter_spec.rb'
- './spec/lib/gitlab/database/postgres_hll/buckets_spec.rb'
- './spec/lib/gitlab/database/postgres_index_bloat_estimate_spec.rb'
- './spec/lib/gitlab/database/postgres_index_spec.rb'
- './spec/lib/gitlab/database/postgres_partitioned_table_spec.rb'
- './spec/lib/gitlab/database/postgres_partition_spec.rb'
- './spec/lib/gitlab/database/postgresql_adapter/dump_schema_versions_mixin_spec.rb'
- './spec/lib/gitlab/database/postgresql_adapter/force_disconnectable_mixin_spec.rb'
- './spec/lib/gitlab/database/postgresql_adapter/type_map_cache_spec.rb'
- './spec/lib/gitlab/database/postgresql_database_tasks/load_schema_versions_mixin_spec.rb'
- './spec/lib/gitlab/database/query_analyzers/gitlab_schemas_metrics_spec.rb'
- './spec/lib/gitlab/database/query_analyzers/gitlab_schemas_validate_connection_spec.rb'
- './spec/lib/gitlab/database/query_analyzer_spec.rb'
- './spec/lib/gitlab/database/query_analyzers/prevent_cross_database_modification_spec.rb'
- './spec/lib/gitlab/database/query_analyzers/restrict_allowed_schemas_spec.rb'
- './spec/lib/gitlab/database/reflection_spec.rb'
- './spec/lib/gitlab/database/reindexing/coordinator_spec.rb'
- './spec/lib/gitlab/database/reindexing/grafana_notifier_spec.rb'
- './spec/lib/gitlab/database/reindexing/index_selection_spec.rb'
- './spec/lib/gitlab/database/reindexing/reindex_action_spec.rb'
- './spec/lib/gitlab/database/reindexing/reindex_concurrently_spec.rb'
- './spec/lib/gitlab/database/reindexing_spec.rb'
- './spec/lib/gitlab/database/schema_cache_with_renamed_table_spec.rb'
- './spec/lib/gitlab/database/schema_cleaner_spec.rb'
- './spec/lib/gitlab/database/schema_migrations/context_spec.rb'
- './spec/lib/gitlab/database/schema_migrations/migrations_spec.rb'
- './spec/lib/gitlab/database/sha_attribute_spec.rb'
- './spec/lib/gitlab/database/shared_model_spec.rb'
- './spec/lib/gitlab/database/similarity_score_spec.rb'
- './spec/lib/gitlab/database_spec.rb'
- './spec/lib/gitlab/database/transaction/context_spec.rb'
- './spec/lib/gitlab/database/transaction/observer_spec.rb'
- './spec/lib/gitlab/database/type/color_spec.rb'
- './spec/lib/gitlab/database/type/json_pg_safe_spec.rb'
- './spec/lib/gitlab/database/with_lock_retries_outside_transaction_spec.rb'
- './spec/lib/gitlab/database/with_lock_retries_spec.rb'
- './spec/lib/gitlab/data_builder/alert_spec.rb'
- './spec/lib/gitlab/data_builder/archive_trace_spec.rb'
- './spec/lib/gitlab/data_builder/build_spec.rb'
- './spec/lib/gitlab/data_builder/deployment_spec.rb'
- './spec/lib/gitlab/data_builder/feature_flag_spec.rb'
- './spec/lib/gitlab/data_builder/issuable_spec.rb'
- './spec/lib/gitlab/data_builder/note_spec.rb'
- './spec/lib/gitlab/data_builder/pipeline_spec.rb'
- './spec/lib/gitlab/data_builder/push_spec.rb'
- './spec/lib/gitlab/data_builder/wiki_page_spec.rb'
- './spec/lib/gitlab/default_branch_spec.rb'
- './spec/lib/gitlab/dependency_linker/base_linker_spec.rb'
- './spec/lib/gitlab/dependency_linker/cargo_toml_linker_spec.rb'
- './spec/lib/gitlab/dependency_linker/cartfile_linker_spec.rb'
- './spec/lib/gitlab/dependency_linker/composer_json_linker_spec.rb'
- './spec/lib/gitlab/dependency_linker/gemfile_linker_spec.rb'
- './spec/lib/gitlab/dependency_linker/gemspec_linker_spec.rb'
- './spec/lib/gitlab/dependency_linker/godeps_json_linker_spec.rb'
- './spec/lib/gitlab/dependency_linker/go_mod_linker_spec.rb'
- './spec/lib/gitlab/dependency_linker/go_sum_linker_spec.rb'
- './spec/lib/gitlab/dependency_linker/package_json_linker_spec.rb'
- './spec/lib/gitlab/dependency_linker/parser/gemfile_spec.rb'
- './spec/lib/gitlab/dependency_linker/podfile_linker_spec.rb'
- './spec/lib/gitlab/dependency_linker/podspec_json_linker_spec.rb'
- './spec/lib/gitlab/dependency_linker/podspec_linker_spec.rb'
- './spec/lib/gitlab/dependency_linker/requirements_txt_linker_spec.rb'
- './spec/lib/gitlab/dependency_linker_spec.rb'
- './spec/lib/gitlab/deploy_key_access_spec.rb'
- './spec/lib/gitlab/diff/char_diff_spec.rb'
- './spec/lib/gitlab/diff/diff_refs_spec.rb'
- './spec/lib/gitlab/diff/file_collection/base_spec.rb'
- './spec/lib/gitlab/diff/file_collection/commit_spec.rb'
- './spec/lib/gitlab/diff/file_collection/compare_spec.rb'
- './spec/lib/gitlab/diff/file_collection/merge_request_diff_base_spec.rb'
- './spec/lib/gitlab/diff/file_collection/merge_request_diff_batch_spec.rb'
- './spec/lib/gitlab/diff/file_collection/merge_request_diff_spec.rb'
- './spec/lib/gitlab/diff/file_collection_sorter_spec.rb'
- './spec/lib/gitlab/diff/file_spec.rb'
- './spec/lib/gitlab/diff/formatters/image_formatter_spec.rb'
- './spec/lib/gitlab/diff/formatters/text_formatter_spec.rb'
- './spec/lib/gitlab/diff/highlight_cache_spec.rb'
- './spec/lib/gitlab/diff/highlight_spec.rb'
- './spec/lib/gitlab/diff/inline_diff_markdown_marker_spec.rb'
- './spec/lib/gitlab/diff/inline_diff_marker_spec.rb'
- './spec/lib/gitlab/diff/inline_diff_spec.rb'
- './spec/lib/gitlab/diff/line_mapper_spec.rb'
- './spec/lib/gitlab/diff/line_spec.rb'
- './spec/lib/gitlab/diff/lines_unfolder_spec.rb'
- './spec/lib/gitlab/diff/pair_selector_spec.rb'
- './spec/lib/gitlab/diff/parallel_diff_spec.rb'
- './spec/lib/gitlab/diff/parser_spec.rb'
- './spec/lib/gitlab/diff/position_collection_spec.rb'
- './spec/lib/gitlab/diff/position_spec.rb'
- './spec/lib/gitlab/diff/position_tracer/image_strategy_spec.rb'
- './spec/lib/gitlab/diff/position_tracer/line_strategy_spec.rb'
- './spec/lib/gitlab/diff/position_tracer_spec.rb'
- './spec/lib/gitlab/diff/rendered/notebook/diff_file_helper_spec.rb'
- './spec/lib/gitlab/diff/rendered/notebook/diff_file_spec.rb'
- './spec/lib/gitlab/diff/stats_cache_spec.rb'
- './spec/lib/gitlab/diff/suggestion_diff_spec.rb'
- './spec/lib/gitlab/diff/suggestions_parser_spec.rb'
- './spec/lib/gitlab/diff/suggestion_spec.rb'
- './spec/lib/gitlab/discussions_diff/file_collection_spec.rb'
- './spec/lib/gitlab/discussions_diff/highlight_cache_spec.rb'
- './spec/lib/gitlab/doctor/secrets_spec.rb'
- './spec/lib/gitlab_edition_spec.rb'
- './spec/lib/gitlab/email/attachment_uploader_spec.rb'
- './spec/lib/gitlab/email/failure_handler_spec.rb'
- './spec/lib/gitlab/email/handler/create_issue_handler_spec.rb'
- './spec/lib/gitlab/email/handler/create_merge_request_handler_spec.rb'
- './spec/lib/gitlab/email/handler/create_note_on_issuable_handler_spec.rb'
- './spec/lib/gitlab/email/handler/service_desk_handler_spec.rb'
- './spec/lib/gitlab/email/handler_spec.rb'
- './spec/lib/gitlab/email/handler/unsubscribe_handler_spec.rb'
- './spec/lib/gitlab/email/hook/additional_headers_interceptor_spec.rb'
- './spec/lib/gitlab/email/hook/delivery_metrics_observer_spec.rb'
- './spec/lib/gitlab/email/hook/disable_email_interceptor_spec.rb'
- './spec/lib/gitlab/email/hook/smime_signature_interceptor_spec.rb'
- './spec/lib/gitlab/email/message/repository_push_spec.rb'
- './spec/lib/gitlab/email/receiver_spec.rb'
- './spec/lib/gitlab/email/reply_parser_spec.rb'
- './spec/lib/gitlab/email/service_desk_receiver_spec.rb'
- './spec/lib/gitlab/email/smime/signer_spec.rb'
- './spec/lib/gitlab/emoji_spec.rb'
- './spec/lib/gitlab/encoding_helper_spec.rb'
- './spec/lib/gitlab/encrypted_configuration_spec.rb'
- './spec/lib/gitlab/endpoint_attributes_spec.rb'
- './spec/lib/gitlab/error_tracking/context_payload_generator_spec.rb'
- './spec/lib/gitlab/error_tracking/error_repository/open_api_strategy_spec.rb'
- './spec/lib/gitlab/error_tracking/log_formatter_spec.rb'
- './spec/lib/gitlab/error_tracking/logger_spec.rb'
- './spec/lib/gitlab/error_tracking/processor/context_payload_processor_spec.rb'
- './spec/lib/gitlab/error_tracking/processor/grpc_error_processor_spec.rb'
- './spec/lib/gitlab/error_tracking/processor/sanitize_error_message_processor_spec.rb'
- './spec/lib/gitlab/error_tracking/processor/sanitizer_processor_spec.rb'
- './spec/lib/gitlab/error_tracking/processor/sidekiq_processor_spec.rb'
- './spec/lib/gitlab/error_tracking/stack_trace_highlight_decorator_spec.rb'
- './spec/lib/gitlab/etag_caching/middleware_spec.rb'
- './spec/lib/gitlab/etag_caching/router/graphql_spec.rb'
- './spec/lib/gitlab/etag_caching/router/rails_spec.rb'
- './spec/lib/gitlab/etag_caching/router_spec.rb'
- './spec/lib/gitlab/etag_caching/store_spec.rb'
- './spec/lib/gitlab/event_store/store_spec.rb'
- './spec/lib/gitlab/exception_log_formatter_spec.rb'
- './spec/lib/gitlab/exceptions_app_spec.rb'
- './spec/lib/gitlab/exclusive_lease_helpers/sleeping_lock_spec.rb'
- './spec/lib/gitlab/exclusive_lease_helpers_spec.rb'
- './spec/lib/gitlab/exclusive_lease_spec.rb'
- './spec/lib/gitlab/experiment_feature_rollout_spec.rb'
- './spec/lib/gitlab/external_authorization/access_spec.rb'
- './spec/lib/gitlab/external_authorization/cache_spec.rb'
- './spec/lib/gitlab/external_authorization/client_spec.rb'
- './spec/lib/gitlab/external_authorization/logger_spec.rb'
- './spec/lib/gitlab/external_authorization/response_spec.rb'
- './spec/lib/gitlab/external_authorization_spec.rb'
- './spec/lib/gitlab/fake_application_settings_spec.rb'
- './spec/lib/gitlab/faraday/error_callback_spec.rb'
- './spec/lib/gitlab/favicon_spec.rb'
- './spec/lib/gitlab/feature_categories_spec.rb'
- './spec/lib/gitlab/file_detector_spec.rb'
- './spec/lib/gitlab/file_finder_spec.rb'
- './spec/lib/gitlab/file_hook_spec.rb'
- './spec/lib/gitlab/file_markdown_link_builder_spec.rb'
- './spec/lib/gitlab/file_type_detection_spec.rb'
- './spec/lib/gitlab/fips_spec.rb'
- './spec/lib/gitlab/fogbugz_import/client_spec.rb'
- './spec/lib/gitlab/fogbugz_import/importer_spec.rb'
- './spec/lib/gitlab/fogbugz_import/project_creator_spec.rb'
- './spec/lib/gitlab/form_builders/gitlab_ui_form_builder_spec.rb'
- './spec/lib/gitlab/gfm/reference_rewriter_spec.rb'
- './spec/lib/gitlab/gfm/uploads_rewriter_spec.rb'
- './spec/lib/gitlab/git_access_design_spec.rb'
- './spec/lib/gitlab/git_access_project_spec.rb'
- './spec/lib/gitlab/git_access_snippet_spec.rb'
- './spec/lib/gitlab/git_access_spec.rb'
- './spec/lib/gitlab/git_access_wiki_spec.rb'
- './spec/lib/gitlab/gitaly_client/blob_service_spec.rb'
- './spec/lib/gitlab/gitaly_client/blobs_stitcher_spec.rb'
- './spec/lib/gitlab/gitaly_client/call_spec.rb'
- './spec/lib/gitlab/gitaly_client/cleanup_service_spec.rb'
- './spec/lib/gitlab/gitaly_client/commit_service_spec.rb'
- './spec/lib/gitlab/gitaly_client/conflict_files_stitcher_spec.rb'
- './spec/lib/gitlab/gitaly_client/conflicts_service_spec.rb'
- './spec/lib/gitlab/gitaly_client/diff_spec.rb'
- './spec/lib/gitlab/gitaly_client/diff_stitcher_spec.rb'
- './spec/lib/gitlab/gitaly_client/health_check_service_spec.rb'
- './spec/lib/gitlab/gitaly_client/object_pool_service_spec.rb'
- './spec/lib/gitlab/gitaly_client/operation_service_spec.rb'
- './spec/lib/gitlab/gitaly_client/praefect_info_service_spec.rb'
- './spec/lib/gitlab/gitaly_client/ref_service_spec.rb'
- './spec/lib/gitlab/gitaly_client/remote_service_spec.rb'
- './spec/lib/gitlab/gitaly_client/repository_service_spec.rb'
- './spec/lib/gitlab/gitaly_client_spec.rb'
- './spec/lib/gitlab/gitaly_client/storage_settings_spec.rb'
- './spec/lib/gitlab/gitaly_client/util_spec.rb'
- './spec/lib/gitlab/git/attributes_at_ref_parser_spec.rb'
- './spec/lib/gitlab/git/attributes_parser_spec.rb'
- './spec/lib/gitlab/git/base_error_spec.rb'
- './spec/lib/gitlab/git/blame_spec.rb'
- './spec/lib/gitlab/git/blob_spec.rb'
- './spec/lib/gitlab/git/branch_spec.rb'
- './spec/lib/gitlab/git/bundle_file_spec.rb'
- './spec/lib/gitlab/git/changed_path_spec.rb'
- './spec/lib/gitlab/git/changes_spec.rb'
- './spec/lib/gitlab/git/commit_spec.rb'
- './spec/lib/gitlab/git/commit_stats_spec.rb'
- './spec/lib/gitlab/git/compare_spec.rb'
- './spec/lib/gitlab/git/conflict/file_spec.rb'
- './spec/lib/gitlab/git/conflict/parser_spec.rb'
- './spec/lib/gitlab/git/conflict/resolver_spec.rb'
- './spec/lib/gitlab/git/diff_collection_spec.rb'
- './spec/lib/gitlab/git/diff_spec.rb'
- './spec/lib/gitlab/git/diff_stats_collection_spec.rb'
- './spec/lib/gitlab/git/gitmodules_parser_spec.rb'
- './spec/lib/gitlab/git/hook_env_spec.rb'
- './spec/lib/gitlab/github_import/bulk_importing_spec.rb'
- './spec/lib/gitlab/github_import/client_spec.rb'
- './spec/lib/gitlab/github_import/importer/diff_note_importer_spec.rb'
- './spec/lib/gitlab/github_import/importer/diff_notes_importer_spec.rb'
- './spec/lib/gitlab/github_import/importer/events/base_importer_spec.rb'
- './spec/lib/gitlab/github_import/importer/events/changed_assignee_spec.rb'
- './spec/lib/gitlab/github_import/importer/events/changed_label_spec.rb'
- './spec/lib/gitlab/github_import/importer/events/changed_milestone_spec.rb'
- './spec/lib/gitlab/github_import/importer/events/closed_spec.rb'
- './spec/lib/gitlab/github_import/importer/events/cross_referenced_spec.rb'
- './spec/lib/gitlab/github_import/importer/events/renamed_spec.rb'
- './spec/lib/gitlab/github_import/importer/events/reopened_spec.rb'
- './spec/lib/gitlab/github_import/importer/issue_and_label_links_importer_spec.rb'
- './spec/lib/gitlab/github_import/importer/issue_importer_spec.rb'
- './spec/lib/gitlab/github_import/importer/issues_importer_spec.rb'
- './spec/lib/gitlab/github_import/importer/label_links_importer_spec.rb'
- './spec/lib/gitlab/github_import/importer/labels_importer_spec.rb'
- './spec/lib/gitlab/github_import/importer/lfs_object_importer_spec.rb'
- './spec/lib/gitlab/github_import/importer/lfs_objects_importer_spec.rb'
- './spec/lib/gitlab/github_import/importer/milestones_importer_spec.rb'
- './spec/lib/gitlab/github_import/importer/note_importer_spec.rb'
- './spec/lib/gitlab/github_import/importer/notes_importer_spec.rb'
- './spec/lib/gitlab/github_import/importer/pull_request_importer_spec.rb'
- './spec/lib/gitlab/github_import/importer/pull_requests_importer_spec.rb'
- './spec/lib/gitlab/github_import/importer/releases_importer_spec.rb'
- './spec/lib/gitlab/github_import/importer/repository_importer_spec.rb'
- './spec/lib/gitlab/github_import/importer/single_endpoint_diff_notes_importer_spec.rb'
- './spec/lib/gitlab/github_import/importer/single_endpoint_issue_events_importer_spec.rb'
- './spec/lib/gitlab/github_import/issuable_finder_spec.rb'
- './spec/lib/gitlab/github_import/label_finder_spec.rb'
- './spec/lib/gitlab/github_import/logger_spec.rb'
- './spec/lib/gitlab/github_import/markdown_text_spec.rb'
- './spec/lib/gitlab/github_import/milestone_finder_spec.rb'
- './spec/lib/gitlab/github_import/object_counter_spec.rb'
- './spec/lib/gitlab/github_import/parallel_importer_spec.rb'
- './spec/lib/gitlab/github_import/parallel_scheduling_spec.rb'
- './spec/lib/gitlab/github_import/representation/diff_note_spec.rb'
- './spec/lib/gitlab/github_import/representation/diff_notes/suggestion_formatter_spec.rb'
- './spec/lib/gitlab/github_import/representation/expose_attribute_spec.rb'
- './spec/lib/gitlab/github_import/representation/issue_event_spec.rb'
- './spec/lib/gitlab/github_import/representation/issue_spec.rb'
- './spec/lib/gitlab/github_import/representation/lfs_object_spec.rb'
- './spec/lib/gitlab/github_import/representation/note_spec.rb'
- './spec/lib/gitlab/github_import/representation/pull_request_review_spec.rb'
- './spec/lib/gitlab/github_import/representation/pull_request_spec.rb'
- './spec/lib/gitlab/github_import/representation_spec.rb'
- './spec/lib/gitlab/github_import/representation/to_hash_spec.rb'
- './spec/lib/gitlab/github_import/representation/user_spec.rb'
- './spec/lib/gitlab/github_import/single_endpoint_notes_importing_spec.rb'
- './spec/lib/gitlab/github_import_spec.rb'
- './spec/lib/gitlab/github_import/user_finder_spec.rb'
- './spec/lib/gitlab/git/keep_around_spec.rb'
- './spec/lib/gitlab/git/lfs_changes_spec.rb'
- './spec/lib/gitlab/git/lfs_pointer_file_spec.rb'
- './spec/lib/gitlab/git/merge_base_spec.rb'
- './spec/lib/gitlab/git/object_pool_spec.rb'
- './spec/lib/gitlab/git/patches/collection_spec.rb'
- './spec/lib/gitlab/git/patches/commit_patches_spec.rb'
- './spec/lib/gitlab/git/patches/patch_spec.rb'
- './spec/lib/gitlab/git_post_receive_spec.rb'
- './spec/lib/gitlab/git/pre_receive_error_spec.rb'
- './spec/lib/gitlab/git/push_spec.rb'
- './spec/lib/gitlab/git/raw_diff_change_spec.rb'
- './spec/lib/gitlab/git_ref_validator_spec.rb'
- './spec/lib/gitlab/git/remote_mirror_spec.rb'
- './spec/lib/gitlab/git/repository_cleaner_spec.rb'
- './spec/lib/gitlab/git/repository_spec.rb'
- './spec/lib/gitlab/git_spec.rb'
- './spec/lib/gitlab/git/tag_spec.rb'
- './spec/lib/gitlab/git/tree_spec.rb'
- './spec/lib/gitlab/git/user_spec.rb'
- './spec/lib/gitlab/git/util_spec.rb'
- './spec/lib/gitlab/git/wiki_page_version_spec.rb'
- './spec/lib/gitlab/git/wraps_gitaly_errors_spec.rb'
- './spec/lib/gitlab/global_id/deprecations_spec.rb'
- './spec/lib/gitlab/global_id_spec.rb'
- './spec/lib/gitlab/gl_repository_spec.rb'
- './spec/lib/gitlab/gon_helper_spec.rb'
- './spec/lib/gitlab/gpg/commit_spec.rb'
- './spec/lib/gitlab/gpg/invalid_gpg_signature_updater_spec.rb'
- './spec/lib/gitlab/gpg_spec.rb'
- './spec/lib/gitlab/grape_logging/formatters/lograge_with_timestamp_spec.rb'
- './spec/lib/gitlab/grape_logging/loggers/cloudflare_logger_spec.rb'
- './spec/lib/gitlab/grape_logging/loggers/exception_logger_spec.rb'
- './spec/lib/gitlab/grape_logging/loggers/perf_logger_spec.rb'
- './spec/lib/gitlab/grape_logging/loggers/queue_duration_logger_spec.rb'
- './spec/lib/gitlab/grape_logging/loggers/token_logger_spec.rb'
- './spec/lib/gitlab/grape_logging/loggers/urgency_logger_spec.rb'
- './spec/lib/gitlab/graphs/commits_spec.rb'
- './spec/lib/gitlab/group_search_results_spec.rb'
- './spec/lib/gitlab/harbor/client_spec.rb'
- './spec/lib/gitlab/harbor/query_spec.rb'
- './spec/lib/gitlab/hashed_path_spec.rb'
- './spec/lib/gitlab/health_checks/db_check_spec.rb'
- './spec/lib/gitlab/health_checks/gitaly_check_spec.rb'
- './spec/lib/gitlab/health_checks/master_check_spec.rb'
- './spec/lib/gitlab/health_checks/middleware_spec.rb'
- './spec/lib/gitlab/health_checks/probes/collection_spec.rb'
- './spec/lib/gitlab/health_checks/puma_check_spec.rb'
- './spec/lib/gitlab/health_checks/server_spec.rb'
- './spec/lib/gitlab/highlight_spec.rb'
- './spec/lib/gitlab/hook_data/base_builder_spec.rb'
- './spec/lib/gitlab/hook_data/group_builder_spec.rb'
- './spec/lib/gitlab/hook_data/group_member_builder_spec.rb'
- './spec/lib/gitlab/hook_data/issue_builder_spec.rb'
- './spec/lib/gitlab/hook_data/key_builder_spec.rb'
- './spec/lib/gitlab/hook_data/merge_request_builder_spec.rb'
- './spec/lib/gitlab/hook_data/project_builder_spec.rb'
- './spec/lib/gitlab/hook_data/project_member_builder_spec.rb'
- './spec/lib/gitlab/hook_data/release_builder_spec.rb'
- './spec/lib/gitlab/hook_data/subgroup_builder_spec.rb'
- './spec/lib/gitlab/hook_data/user_builder_spec.rb'
- './spec/lib/gitlab/hotlinking_detector_spec.rb'
- './spec/lib/gitlab/http_io_spec.rb'
- './spec/lib/gitlab/http_spec.rb'
- './spec/lib/gitlab/i18n/metadata_entry_spec.rb'
- './spec/lib/gitlab/i18n/po_linter_spec.rb'
- './spec/lib/gitlab/i18n_spec.rb'
- './spec/lib/gitlab/i18n/translation_entry_spec.rb'
- './spec/lib/gitlab/identifier_spec.rb'
- './spec/lib/gitlab/import/database_helpers_spec.rb'
- './spec/lib/gitlab/import/page_counter_spec.rb'
- './spec/lib/gitlab/import_export/after_export_strategy_builder_spec.rb'
- './spec/lib/gitlab/import_export/attribute_cleaner_spec.rb'
- './spec/lib/gitlab/import_export/attribute_configuration_spec.rb'
- './spec/lib/gitlab/import_export/attributes_finder_spec.rb'
- './spec/lib/gitlab/import_export/attributes_permitter_spec.rb'
- './spec/lib/gitlab/import_export/avatar_restorer_spec.rb'
- './spec/lib/gitlab/import_export/avatar_saver_spec.rb'
- './spec/lib/gitlab/import_export/base/object_builder_spec.rb'
- './spec/lib/gitlab/import_export/base/relation_factory_spec.rb'
- './spec/lib/gitlab/import_export/base/relation_object_saver_spec.rb'
- './spec/lib/gitlab/import_export/command_line_util_spec.rb'
- './spec/lib/gitlab/import_export/config_spec.rb'
- './spec/lib/gitlab/import_export/decompressed_archive_size_validator_spec.rb'
- './spec/lib/gitlab/import_export/design_repo_restorer_spec.rb'
- './spec/lib/gitlab/import_export/design_repo_saver_spec.rb'
- './spec/lib/gitlab/import_export/duration_measuring_spec.rb'
- './spec/lib/gitlab/import_export/error_spec.rb'
- './spec/lib/gitlab/import_export/fast_hash_serializer_spec.rb'
- './spec/lib/gitlab/import_export/group/object_builder_spec.rb'
- './spec/lib/gitlab/import_export/group/relation_factory_spec.rb'
- './spec/lib/gitlab/import_export/group/relation_tree_restorer_spec.rb'
- './spec/lib/gitlab/import_export/group/tree_restorer_spec.rb'
- './spec/lib/gitlab/import_export/group/tree_saver_spec.rb'
- './spec/lib/gitlab/import_export/hash_util_spec.rb'
- './spec/lib/gitlab/import_export/importer_spec.rb'
- './spec/lib/gitlab/import_export/import_export_spec.rb'
- './spec/lib/gitlab/import_export/import_failure_service_spec.rb'
- './spec/lib/gitlab/import_export/import_test_coverage_spec.rb'
- './spec/lib/gitlab/import_export/json/ndjson_reader_spec.rb'
- './spec/lib/gitlab/import_export/json/ndjson_writer_spec.rb'
- './spec/lib/gitlab/import_export/json/streaming_serializer_spec.rb'
- './spec/lib/gitlab/import_export/lfs_restorer_spec.rb'
- './spec/lib/gitlab/import_export/lfs_saver_spec.rb'
- './spec/lib/gitlab/import_export/log_util_spec.rb'
- './spec/lib/gitlab/import_export/merge_request_parser_spec.rb'
- './spec/lib/gitlab/import_export/model_configuration_spec.rb'
- './spec/lib/gitlab/import_export/project/export_task_spec.rb'
- './spec/lib/gitlab/import_export/project/import_task_spec.rb'
- './spec/lib/gitlab/import_export/project/object_builder_spec.rb'
- './spec/lib/gitlab/import_export/project/relation_factory_spec.rb'
- './spec/lib/gitlab/import_export/project/relation_saver_spec.rb'
- './spec/lib/gitlab/import_export/project/relation_tree_restorer_spec.rb'
- './spec/lib/gitlab/import_export/project/sample/date_calculator_spec.rb'
- './spec/lib/gitlab/import_export/project/sample/relation_factory_spec.rb'
- './spec/lib/gitlab/import_export/project/sample/relation_tree_restorer_spec.rb'
- './spec/lib/gitlab/import_export/project/tree_restorer_spec.rb'
- './spec/lib/gitlab/import_export/project/tree_saver_spec.rb'
- './spec/lib/gitlab/import_export/reader_spec.rb'
- './spec/lib/gitlab/import_export/references_configuration_spec.rb'
- './spec/lib/gitlab/import_export/remote_stream_upload_spec.rb'
- './spec/lib/gitlab/import_export/repo_restorer_spec.rb'
- './spec/lib/gitlab/import_export/repo_saver_spec.rb'
- './spec/lib/gitlab/import_export/saver_spec.rb'
- './spec/lib/gitlab/import_export/shared_spec.rb'
- './spec/lib/gitlab/import_export/snippet_repo_restorer_spec.rb'
- './spec/lib/gitlab/import_export/snippet_repo_saver_spec.rb'
- './spec/lib/gitlab/import_export/snippets_repo_restorer_spec.rb'
- './spec/lib/gitlab/import_export/snippets_repo_saver_spec.rb'
- './spec/lib/gitlab/import_export/uploads_manager_spec.rb'
- './spec/lib/gitlab/import_export/uploads_restorer_spec.rb'
- './spec/lib/gitlab/import_export/uploads_saver_spec.rb'
- './spec/lib/gitlab/import_export/version_checker_spec.rb'
- './spec/lib/gitlab/import_export/wiki_repo_saver_spec.rb'
- './spec/lib/gitlab/import_formatter_spec.rb'
- './spec/lib/gitlab/import/import_failure_service_spec.rb'
- './spec/lib/gitlab/import/merge_request_creator_spec.rb'
- './spec/lib/gitlab/import/merge_request_helpers_spec.rb'
- './spec/lib/gitlab/import/metrics_spec.rb'
- './spec/lib/gitlab/import/set_async_jid_spec.rb'
- './spec/lib/gitlab/import_sources_spec.rb'
- './spec/lib/gitlab/dormant_projects_deletion_warning_tracker_spec.rb'
- './spec/lib/gitlab/insecure_key_fingerprint_spec.rb'
- './spec/lib/gitlab/instrumentation_helper_spec.rb'
- './spec/lib/gitlab/instrumentation/rate_limiting_gates_spec.rb'
- './spec/lib/gitlab/instrumentation/redis_base_spec.rb'
- './spec/lib/gitlab/instrumentation/redis_cluster_validator_spec.rb'
- './spec/lib/gitlab/instrumentation/redis_spec.rb'
- './spec/lib/gitlab/internal_post_receive/response_spec.rb'
- './spec/lib/gitlab/issuable/clone/attributes_rewriter_spec.rb'
- './spec/lib/gitlab/issuable/clone/copy_resource_events_service_spec.rb'
- './spec/lib/gitlab/issuable_metadata_spec.rb'
- './spec/lib/gitlab/issuables_count_for_state_spec.rb'
- './spec/lib/gitlab/issuable_sorter_spec.rb'
- './spec/lib/gitlab/issues/rebalancing/state_spec.rb'
- './spec/lib/gitlab/jira/dvcs_spec.rb'
- './spec/lib/gitlab/jira_import/base_importer_spec.rb'
- './spec/lib/gitlab/jira_import/handle_labels_service_spec.rb'
- './spec/lib/gitlab/jira_import/issue_serializer_spec.rb'
- './spec/lib/gitlab/jira_import/issues_importer_spec.rb'
- './spec/lib/gitlab/jira_import/labels_importer_spec.rb'
- './spec/lib/gitlab/jira_import/metadata_collector_spec.rb'
- './spec/lib/gitlab/jira_import_spec.rb'
- './spec/lib/gitlab/job_waiter_spec.rb'
- './spec/lib/gitlab/json_logger_spec.rb'
- './spec/lib/gitlab/json_spec.rb'
- './spec/lib/gitlab/jwt_authenticatable_spec.rb'
- './spec/lib/gitlab/jwt_token_spec.rb'
- './spec/lib/gitlab/kas/client_spec.rb'
- './spec/lib/gitlab/kas_spec.rb'
- './spec/lib/gitlab/kroki_spec.rb'
- './spec/lib/gitlab/kubernetes/cluster_role_binding_spec.rb'
- './spec/lib/gitlab/kubernetes/config_maps/aws_node_auth_spec.rb'
- './spec/lib/gitlab/kubernetes/config_map_spec.rb'
- './spec/lib/gitlab/kubernetes/default_namespace_spec.rb'
- './spec/lib/gitlab/kubernetes/deployment_spec.rb'
- './spec/lib/gitlab/kubernetes/generic_secret_spec.rb'
- './spec/lib/gitlab/kubernetes/ingress_spec.rb'
- './spec/lib/gitlab/kubernetes/kube_client_spec.rb'
- './spec/lib/gitlab/kubernetes/kubeconfig/entry/cluster_spec.rb'
- './spec/lib/gitlab/kubernetes/kubeconfig/entry/context_spec.rb'
- './spec/lib/gitlab/kubernetes/kubeconfig/entry/user_spec.rb'
- './spec/lib/gitlab/kubernetes/kubeconfig/template_spec.rb'
- './spec/lib/gitlab/kubernetes/kubectl_cmd_spec.rb'
- './spec/lib/gitlab/kubernetes/namespace_spec.rb'
- './spec/lib/gitlab/kubernetes/node_spec.rb'
- './spec/lib/gitlab/kubernetes/pod_cmd_spec.rb'
- './spec/lib/gitlab/kubernetes/role_binding_spec.rb'
- './spec/lib/gitlab/kubernetes/role_spec.rb'
- './spec/lib/gitlab/kubernetes/rollout_instances_spec.rb'
- './spec/lib/gitlab/kubernetes/rollout_status_spec.rb'
- './spec/lib/gitlab/kubernetes/service_account_spec.rb'
- './spec/lib/gitlab/kubernetes/service_account_token_spec.rb'
- './spec/lib/gitlab/kubernetes_spec.rb'
- './spec/lib/gitlab/kubernetes/tls_secret_spec.rb'
- './spec/lib/gitlab/language_data_spec.rb'
- './spec/lib/gitlab/language_detection_spec.rb'
- './spec/lib/gitlab/lazy_spec.rb'
- './spec/lib/gitlab/legacy_github_import/branch_formatter_spec.rb'
- './spec/lib/gitlab/legacy_github_import/client_spec.rb'
- './spec/lib/gitlab/legacy_github_import/comment_formatter_spec.rb'
- './spec/lib/gitlab/legacy_github_import/importer_spec.rb'
- './spec/lib/gitlab/legacy_github_import/issuable_formatter_spec.rb'
- './spec/lib/gitlab/legacy_github_import/issue_formatter_spec.rb'
- './spec/lib/gitlab/legacy_github_import/label_formatter_spec.rb'
- './spec/lib/gitlab/legacy_github_import/milestone_formatter_spec.rb'
- './spec/lib/gitlab/legacy_github_import/project_creator_spec.rb'
- './spec/lib/gitlab/legacy_github_import/pull_request_formatter_spec.rb'
- './spec/lib/gitlab/legacy_github_import/release_formatter_spec.rb'
- './spec/lib/gitlab/legacy_github_import/user_formatter_spec.rb'
- './spec/lib/gitlab/legacy_github_import/wiki_formatter_spec.rb'
- './spec/lib/gitlab/lets_encrypt/challenge_spec.rb'
- './spec/lib/gitlab/lets_encrypt/client_spec.rb'
- './spec/lib/gitlab/lets_encrypt/order_spec.rb'
- './spec/lib/gitlab/lets_encrypt_spec.rb'
- './spec/lib/gitlab/lfs/client_spec.rb'
- './spec/lib/gitlab/lfs_token_spec.rb'
- './spec/lib/gitlab/local_and_remote_storage_migration/artifact_migrater_spec.rb'
- './spec/lib/gitlab/local_and_remote_storage_migration/pages_deployment_migrater_spec.rb'
- './spec/lib/gitlab/logger_spec.rb'
- './spec/lib/gitlab/logging/cloudflare_helper_spec.rb'
- './spec/lib/gitlab/lograge/custom_options_spec.rb'
- './spec/lib/gitlab/log_timestamp_formatter_spec.rb'
- './spec/lib/gitlab/loop_helpers_spec.rb'
- './spec/lib/gitlab/mailgun/webhook_processors/failure_logger_spec.rb'
- './spec/lib/gitlab/mailgun/webhook_processors/member_invites_spec.rb'
- './spec/lib/gitlab/mail_room/authenticator_spec.rb'
- './spec/lib/gitlab/mail_room/mail_room_spec.rb'
- './spec/lib/gitlab/manifest_import/manifest_spec.rb'
- './spec/lib/gitlab/manifest_import/metadata_spec.rb'
- './spec/lib/gitlab/manifest_import/project_creator_spec.rb'
- './spec/lib/gitlab/markdown_cache/active_record/extension_spec.rb'
- './spec/lib/gitlab/markdown_cache/field_data_spec.rb'
- './spec/lib/gitlab/markdown_cache/redis/extension_spec.rb'
- './spec/lib/gitlab/markdown_cache/redis/store_spec.rb'
- './spec/lib/gitlab/marker_range_spec.rb'
- './spec/lib/gitlab/markup_helper_spec.rb'
- './spec/lib/gitlab/memory/instrumentation_spec.rb'
- './spec/lib/gitlab/memory/jemalloc_spec.rb'
- './spec/lib/gitlab/memory/reports_daemon_spec.rb'
- './spec/lib/gitlab/memory/reports/jemalloc_stats_spec.rb'
- './spec/lib/gitlab/memory/watchdog_spec.rb'
- './spec/lib/gitlab/merge_requests/mergeability/check_result_spec.rb'
- './spec/lib/gitlab/merge_requests/mergeability/redis_interface_spec.rb'
- './spec/lib/gitlab/merge_requests/mergeability/results_store_spec.rb'
- './spec/lib/gitlab/metrics/background_transaction_spec.rb'
- './spec/lib/gitlab/metrics/boot_time_tracker_spec.rb'
- './spec/lib/gitlab/metrics/delta_spec.rb'
- './spec/lib/gitlab/metrics/elasticsearch_rack_middleware_spec.rb'
- './spec/lib/gitlab/metrics/exporter/base_exporter_spec.rb'
- './spec/lib/gitlab/metrics/exporter/gc_request_middleware_spec.rb'
- './spec/lib/gitlab/metrics/exporter/metrics_middleware_spec.rb'
- './spec/lib/gitlab/metrics/memory_spec.rb'
- './spec/lib/gitlab/metrics/method_call_spec.rb'
- './spec/lib/gitlab/metrics/methods_spec.rb'
- './spec/lib/gitlab/metrics/rack_middleware_spec.rb'
- './spec/lib/gitlab/metrics/rails_slis_spec.rb'
- './spec/lib/gitlab/metrics/requests_rack_middleware_spec.rb'
- './spec/lib/gitlab/metrics/samplers/action_cable_sampler_spec.rb'
- './spec/lib/gitlab/metrics/samplers/database_sampler_spec.rb'
- './spec/lib/gitlab/metrics/samplers/puma_sampler_spec.rb'
- './spec/lib/gitlab/metrics/samplers/ruby_sampler_spec.rb'
- './spec/lib/gitlab/metrics/samplers/threads_sampler_spec.rb'
- './spec/lib/gitlab/metrics/sli_spec.rb'
- './spec/lib/gitlab/metrics_spec.rb'
- './spec/lib/gitlab/metrics/subscribers/action_cable_spec.rb'
- './spec/lib/gitlab/metrics/subscribers/action_view_spec.rb'
- './spec/lib/gitlab/metrics/subscribers/active_record_spec.rb'
- './spec/lib/gitlab/metrics/subscribers/external_http_spec.rb'
- './spec/lib/gitlab/metrics/subscribers/load_balancing_spec.rb'
- './spec/lib/gitlab/metrics/subscribers/rack_attack_spec.rb'
- './spec/lib/gitlab/metrics/subscribers/rails_cache_spec.rb'
- './spec/lib/gitlab/metrics/transaction_spec.rb'
- './spec/lib/gitlab/metrics/web_transaction_spec.rb'
- './spec/lib/gitlab/middleware/basic_health_check_spec.rb'
- './spec/lib/gitlab/middleware/compressed_json_spec.rb'
- './spec/lib/gitlab/middleware/go_spec.rb'
- './spec/lib/gitlab/middleware/handle_ip_spoof_attack_error_spec.rb'
- './spec/lib/gitlab/middleware/handle_malformed_strings_spec.rb'
- './spec/lib/gitlab/middleware/memory_report_spec.rb'
- './spec/lib/gitlab/middleware/multipart/handler_spec.rb'
- './spec/lib/gitlab/middleware/multipart_spec.rb'
- './spec/lib/gitlab/middleware/query_analyzer_spec.rb'
- './spec/lib/gitlab/middleware/rack_multipart_tempfile_factory_spec.rb'
- './spec/lib/gitlab/middleware/rails_queue_duration_spec.rb'
- './spec/lib/gitlab/middleware/read_only_spec.rb'
- './spec/lib/gitlab/middleware/release_env_spec.rb'
- './spec/lib/gitlab/middleware/request_context_spec.rb'
- './spec/lib/gitlab/middleware/same_site_cookies_spec.rb'
- './spec/lib/gitlab/middleware/sidekiq_web_static_spec.rb'
- './spec/lib/gitlab/middleware/speedscope_spec.rb'
- './spec/lib/gitlab/middleware/webhook_recursion_detection_spec.rb'
- './spec/lib/gitlab/monitor/demo_projects_spec.rb'
- './spec/lib/gitlab/multi_collection_paginator_spec.rb'
- './spec/lib/gitlab/multi_destination_logger_spec.rb'
- './spec/lib/gitlab/namespaced_session_store_spec.rb'
- './spec/lib/gitlab/nav/top_nav_menu_item_spec.rb'
- './spec/lib/gitlab/no_cache_headers_spec.rb'
- './spec/lib/gitlab/noteable_metadata_spec.rb'
- './spec/lib/gitlab/object_hierarchy_spec.rb'
- './spec/lib/gitlab/omniauth_initializer_spec.rb'
- './spec/lib/gitlab/optimistic_locking_spec.rb'
- './spec/lib/gitlab/other_markup_spec.rb'
- './spec/lib/gitlab/otp_key_rotator_spec.rb'
- './spec/lib/gitlab/pages/settings_spec.rb'
- './spec/lib/gitlab/pages_spec.rb'
- './spec/lib/gitlab/pagination/cursor_based_keyset_spec.rb'
- './spec/lib/gitlab/pagination/gitaly_keyset_pager_spec.rb'
- './spec/lib/gitlab/pagination/keyset/column_order_definition_spec.rb'
- './spec/lib/gitlab/pagination/keyset/cursor_based_request_context_spec.rb'
- './spec/lib/gitlab/pagination/keyset/cursor_pager_spec.rb'
- './spec/lib/gitlab/pagination/keyset/in_operator_optimization/array_scope_columns_spec.rb'
- './spec/lib/gitlab/pagination/keyset/in_operator_optimization/column_data_spec.rb'
- './spec/lib/gitlab/pagination/keyset/in_operator_optimization/order_by_column_data_spec.rb'
- './spec/lib/gitlab/pagination/keyset/in_operator_optimization/order_by_columns_spec.rb'
- './spec/lib/gitlab/pagination/keyset/in_operator_optimization/query_builder_spec.rb'
- './spec/lib/gitlab/pagination/keyset/in_operator_optimization/strategies/order_values_loader_strategy_spec.rb'
- './spec/lib/gitlab/pagination/keyset/in_operator_optimization/strategies/record_loader_strategy_spec.rb'
- './spec/lib/gitlab/pagination/keyset/iterator_spec.rb'
- './spec/lib/gitlab/pagination/keyset/order_spec.rb'
- './spec/lib/gitlab/pagination/keyset/pager_spec.rb'
- './spec/lib/gitlab/pagination/keyset/page_spec.rb'
- './spec/lib/gitlab/pagination/keyset/paginator_spec.rb'
- './spec/lib/gitlab/pagination/keyset/request_context_spec.rb'
- './spec/lib/gitlab/pagination/keyset/simple_order_builder_spec.rb'
- './spec/lib/gitlab/pagination/keyset_spec.rb'
- './spec/lib/gitlab/pagination/offset_header_builder_spec.rb'
- './spec/lib/gitlab/pagination/offset_header_builder_with_controller_spec.rb'
- './spec/lib/gitlab/pagination/offset_pagination_spec.rb'
- './spec/lib/gitlab/patch/database_config_spec.rb'
- './spec/lib/gitlab/patch/draw_route_spec.rb'
- './spec/lib/gitlab/patch/prependable_spec.rb'
- './spec/lib/gitlab/path_regex_spec.rb'
- './spec/lib/gitlab/performance_bar/redis_adapter_when_peek_enabled_spec.rb'
- './spec/lib/gitlab/performance_bar_spec.rb'
- './spec/lib/gitlab/performance_bar/stats_spec.rb'
- './spec/lib/gitlab/performance_bar/with_top_level_warnings_spec.rb'
- './spec/lib/gitlab/pipeline_scope_counts_spec.rb'
- './spec/lib/gitlab/polling_interval_spec.rb'
- './spec/lib/gitlab/popen/runner_spec.rb'
- './spec/lib/gitlab/private_commit_email_spec.rb'
- './spec/lib/gitlab/process_management_spec.rb'
- './spec/lib/gitlab/process_memory_cache/helper_spec.rb'
- './spec/lib/gitlab/process_supervisor_spec.rb'
- './spec/lib/gitlab/profiler_spec.rb'
- './spec/lib/gitlab/project_authorizations_spec.rb'
- './spec/lib/gitlab/project_search_results_spec.rb'
- './spec/lib/gitlab/project_stats_refresh_conflicts_logger_spec.rb'
- './spec/lib/gitlab/project_template_spec.rb'
- './spec/lib/gitlab/project_transfer_spec.rb'
- './spec/lib/gitlab/prometheus/adapter_spec.rb'
- './spec/lib/gitlab/prometheus_client_spec.rb'
- './spec/lib/gitlab/prometheus/internal_spec.rb'
- './spec/lib/gitlab/protocol_access_spec.rb'
- './spec/lib/gitlab/puma_logging/json_formatter_spec.rb'
- './spec/lib/gitlab/push_options_spec.rb'
- './spec/lib/gitlab/query_limiting/active_support_subscriber_spec.rb'
- './spec/lib/gitlab/query_limiting/middleware_spec.rb'
- './spec/lib/gitlab/query_limiting_spec.rb'
- './spec/lib/gitlab/query_limiting/transaction_spec.rb'
- './spec/lib/gitlab/quick_actions/command_definition_spec.rb'
- './spec/lib/gitlab/quick_actions/dsl_spec.rb'
- './spec/lib/gitlab/quick_actions/extractor_spec.rb'
- './spec/lib/gitlab/quick_actions/spend_time_and_date_separator_spec.rb'
- './spec/lib/gitlab/quick_actions/substitution_definition_spec.rb'
- './spec/lib/gitlab/quick_actions/users_extractor_spec.rb'
- './spec/lib/gitlab/rack_attack/request_spec.rb'
- './spec/lib/gitlab/rack_attack_spec.rb'
- './spec/lib/gitlab/rack_attack/user_allowlist_spec.rb'
- './spec/lib/gitlab/reactive_cache_set_cache_spec.rb'
- './spec/lib/gitlab/redis/boolean_spec.rb'
- './spec/lib/gitlab/redis/cache_spec.rb'
- './spec/lib/gitlab/redis/hll_spec.rb'
- './spec/lib/gitlab/redis/multi_store_spec.rb'
- './spec/lib/gitlab/redis/queues_spec.rb'
- './spec/lib/gitlab/redis/rate_limiting_spec.rb'
- './spec/lib/gitlab/redis/sessions_spec.rb'
- './spec/lib/gitlab/redis/shared_state_spec.rb'
- './spec/lib/gitlab/redis/trace_chunks_spec.rb'
- './spec/lib/gitlab/redis/wrapper_spec.rb'
- './spec/lib/gitlab/reference_counter_spec.rb'
- './spec/lib/gitlab/reference_extractor_spec.rb'
- './spec/lib/gitlab/regex_requires_app_spec.rb'
- './spec/lib/gitlab/regex_spec.rb'
- './spec/lib/gitlab/relative_positioning/item_context_spec.rb'
- './spec/lib/gitlab/relative_positioning/mover_spec.rb'
- './spec/lib/gitlab/relative_positioning/range_spec.rb'
- './spec/lib/gitlab/render_timeout_spec.rb'
- './spec/lib/gitlab/repo_path_spec.rb'
- './spec/lib/gitlab/repository_archive_rate_limiter_spec.rb'
- './spec/lib/gitlab/repository_cache_adapter_spec.rb'
- './spec/lib/gitlab/repository_cache/preloader_spec.rb'
- './spec/lib/gitlab/repository_cache_spec.rb'
- './spec/lib/gitlab/repository_hash_cache_spec.rb'
- './spec/lib/gitlab/repository_set_cache_spec.rb'
- './spec/lib/gitlab/repository_size_checker_spec.rb'
- './spec/lib/gitlab/repository_size_error_message_spec.rb'
- './spec/lib/gitlab/repository_url_builder_spec.rb'
- './spec/lib/gitlab/request_context_spec.rb'
- './spec/lib/gitlab/request_endpoints_spec.rb'
- './spec/lib/gitlab/request_forgery_protection_spec.rb'
- './spec/lib/gitlab/robots_txt/parser_spec.rb'
- './spec/lib/gitlab/route_map_spec.rb'
- './spec/lib/gitlab/routing_spec.rb'
- './spec/lib/gitlab/runtime_spec.rb'
- './spec/lib/gitlab/saas_spec.rb'
- './spec/lib/gitlab/safe_request_loader_spec.rb'
- './spec/lib/gitlab/safe_request_purger_spec.rb'
- './spec/lib/gitlab/sample_data_template_spec.rb'
- './spec/lib/gitlab/sanitizers/exception_message_spec.rb'
- './spec/lib/gitlab/sanitizers/exif_spec.rb'
- './spec/lib/gitlab/sanitizers/svg_spec.rb'
- './spec/lib/gitlab/search/abuse_validators/no_abusive_coercion_from_string_validator_spec.rb'
- './spec/lib/gitlab/search/abuse_validators/no_abusive_term_length_validator_spec.rb'
- './spec/lib/gitlab/search_context/builder_spec.rb'
- './spec/lib/gitlab/search_context/controller_concern_spec.rb'
- './spec/lib/gitlab/search/found_blob_spec.rb'
- './spec/lib/gitlab/search/found_wiki_page_spec.rb'
- './spec/lib/gitlab/search/query_spec.rb'
- './spec/lib/gitlab/search/recent_issues_spec.rb'
- './spec/lib/gitlab/search/recent_merge_requests_spec.rb'
- './spec/lib/gitlab/search_results_spec.rb'
- './spec/lib/gitlab/search/sort_options_spec.rb'
- './spec/lib/gitlab/security/scan_configuration_spec.rb'
- './spec/lib/gitlab/seeders/ci/daily_build_group_report_result_spec.rb'
- './spec/lib/gitlab/seeder_spec.rb'
- './spec/lib/gitlab/serializer/ci/variables_spec.rb'
- './spec/lib/gitlab/serializer/pagination_spec.rb'
- './spec/lib/gitlab/session_spec.rb'
- './spec/lib/gitlab/setup_helper/praefect_spec.rb'
- './spec/lib/gitlab/setup_helper/workhorse_spec.rb'
- './spec/lib/gitlab/shard_health_cache_spec.rb'
- './spec/lib/gitlab/shell_spec.rb'
- './spec/lib/gitlab/sidekiq_config/cli_methods_spec.rb'
- './spec/lib/gitlab/sidekiq_config_spec.rb'
- './spec/lib/gitlab/sidekiq_config/worker_matcher_spec.rb'
- './spec/lib/gitlab/sidekiq_config/worker_router_spec.rb'
- './spec/lib/gitlab/sidekiq_config/worker_spec.rb'
- './spec/lib/gitlab/sidekiq_daemon/monitor_spec.rb'
- './spec/lib/gitlab/sidekiq_death_handler_spec.rb'
- './spec/lib/gitlab/sidekiq_logging/deduplication_logger_spec.rb'
- './spec/lib/gitlab/sidekiq_logging/json_formatter_spec.rb'
- './spec/lib/gitlab/sidekiq_logging/structured_logger_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/admin_mode/client_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/admin_mode/server_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/client_metrics_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/duplicate_jobs/client_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/duplicate_jobs/duplicate_job_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/duplicate_jobs/server_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/duplicate_jobs/strategies/none_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/duplicate_jobs/strategies_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/duplicate_jobs/strategies/until_executed_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/duplicate_jobs/strategies/until_executing_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/extra_done_log_metadata_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/instrumentation_logger_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/monitor_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/query_analyzer_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/server_metrics_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/size_limiter/client_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/size_limiter/compressor_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/size_limiter/exceed_limit_error_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/size_limiter/server_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/size_limiter/validator_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/worker_context/client_spec.rb'
- './spec/lib/gitlab/sidekiq_middleware/worker_context/server_spec.rb'
- './spec/lib/gitlab/sidekiq_migrate_jobs_spec.rb'
- './spec/lib/gitlab/sidekiq_queue_spec.rb'
- './spec/lib/gitlab/sidekiq_signals_spec.rb'
- './spec/lib/gitlab/sidekiq_status/client_middleware_spec.rb'
- './spec/lib/gitlab/sidekiq_status/server_middleware_spec.rb'
- './spec/lib/gitlab/sidekiq_status_spec.rb'
- './spec/lib/gitlab/sidekiq_versioning/middleware_spec.rb'
- './spec/lib/gitlab/sidekiq_versioning_spec.rb'
- './spec/lib/gitlab/sidekiq_versioning/worker_spec.rb'
- './spec/lib/gitlab/slash_commands/application_help_spec.rb'
- './spec/lib/gitlab/slash_commands/command_spec.rb'
- './spec/lib/gitlab/slash_commands/deploy_spec.rb'
- './spec/lib/gitlab/slash_commands/issue_close_spec.rb'
- './spec/lib/gitlab/slash_commands/issue_comment_spec.rb'
- './spec/lib/gitlab/slash_commands/issue_move_spec.rb'
- './spec/lib/gitlab/slash_commands/issue_new_spec.rb'
- './spec/lib/gitlab/slash_commands/issue_search_spec.rb'
- './spec/lib/gitlab/slash_commands/issue_show_spec.rb'
- './spec/lib/gitlab/slash_commands/presenters/access_spec.rb'
- './spec/lib/gitlab/slash_commands/presenters/deploy_spec.rb'
- './spec/lib/gitlab/slash_commands/presenters/error_spec.rb'
- './spec/lib/gitlab/slash_commands/presenters/issue_close_spec.rb'
- './spec/lib/gitlab/slash_commands/presenters/issue_comment_spec.rb'
- './spec/lib/gitlab/slash_commands/presenters/issue_move_spec.rb'
- './spec/lib/gitlab/slash_commands/presenters/issue_new_spec.rb'
- './spec/lib/gitlab/slash_commands/presenters/issue_search_spec.rb'
- './spec/lib/gitlab/slash_commands/presenters/issue_show_spec.rb'
- './spec/lib/gitlab/slash_commands/presenters/run_spec.rb'
- './spec/lib/gitlab/slash_commands/run_spec.rb'
- './spec/lib/gitlab/snippet_search_results_spec.rb'
- './spec/lib/gitlab/spamcheck/client_spec.rb'
- './spec/lib/gitlab_spec.rb'
- './spec/lib/gitlab/sql/cte_spec.rb'
- './spec/lib/gitlab/sql/except_spec.rb'
- './spec/lib/gitlab/sql/glob_spec.rb'
- './spec/lib/gitlab/sql/intersect_spec.rb'
- './spec/lib/gitlab/sql/pattern_spec.rb'
- './spec/lib/gitlab/sql/recursive_cte_spec.rb'
- './spec/lib/gitlab/sql/union_spec.rb'
- './spec/lib/gitlab/ssh/commit_spec.rb'
- './spec/lib/gitlab/ssh_public_key_spec.rb'
- './spec/lib/gitlab/ssh/signature_spec.rb'
- './spec/lib/gitlab/string_placeholder_replacer_spec.rb'
- './spec/lib/gitlab/string_range_marker_spec.rb'
- './spec/lib/gitlab/string_regex_marker_spec.rb'
- './spec/lib/gitlab/submodule_links_spec.rb'
- './spec/lib/gitlab/subscription_portal_spec.rb'
- './spec/lib/gitlab/suggestions/commit_message_spec.rb'
- './spec/lib/gitlab/suggestions/file_suggestion_spec.rb'
- './spec/lib/gitlab/suggestions/suggestion_set_spec.rb'
- './spec/lib/gitlab/tab_width_spec.rb'
- './spec/lib/gitlab/tcp_checker_spec.rb'
- './spec/lib/gitlab/template/finders/global_template_finder_spec.rb'
- './spec/lib/gitlab/template/finders/repo_template_finders_spec.rb'
- './spec/lib/gitlab/template/gitignore_template_spec.rb'
- './spec/lib/gitlab/template/gitlab_ci_yml_template_spec.rb'
- './spec/lib/gitlab/template/issue_template_spec.rb'
- './spec/lib/gitlab/template/merge_request_template_spec.rb'
- './spec/lib/gitlab/template_parser/ast_spec.rb'
- './spec/lib/gitlab/template_parser/parser_spec.rb'
- './spec/lib/gitlab/terraform_registry_token_spec.rb'
- './spec/lib/gitlab/terraform/state_migration_helper_spec.rb'
- './spec/lib/gitlab/themes_spec.rb'
- './spec/lib/gitlab/throttle_spec.rb'
- './spec/lib/gitlab/time_tracking_formatter_spec.rb'
- './spec/lib/gitlab/tracking/destinations/snowplow_micro_spec.rb'
- './spec/lib/gitlab/tracking/destinations/snowplow_spec.rb'
- './spec/lib/gitlab/tracking/event_definition_spec.rb'
- './spec/lib/gitlab/tracking_spec.rb'
- './spec/lib/gitlab/tracking/standard_context_spec.rb'
- './spec/lib/gitlab/tree_summary_spec.rb'
- './spec/lib/gitlab/unicode_spec.rb'
- './spec/lib/gitlab/untrusted_regexp/ruby_syntax_spec.rb'
- './spec/lib/gitlab/untrusted_regexp_spec.rb'
- './spec/lib/gitlab/uploads_transfer_spec.rb'
- './spec/lib/gitlab/url_blockers/domain_allowlist_entry_spec.rb'
- './spec/lib/gitlab/url_blockers/ip_allowlist_entry_spec.rb'
- './spec/lib/gitlab/url_builder_spec.rb'
- './spec/lib/gitlab/url_sanitizer_spec.rb'
- './spec/lib/gitlab/usage_data_counters/base_counter_spec.rb'
- './spec/lib/gitlab/usage_data_counters/ci_template_unique_counter_spec.rb'
- './spec/lib/gitlab/usage_data_counters/code_review_events_spec.rb'
- './spec/lib/gitlab/usage_data_counters/gitlab_cli_activity_unique_counter_spec.rb'
- './spec/lib/gitlab/usage_data_counters/hll_redis_counter_spec.rb'
- './spec/lib/gitlab/usage_data_counters/ipynb_diff_activity_counter_spec.rb'
- './spec/lib/gitlab/usage_data_counters/issue_activity_unique_counter_spec.rb'
- './spec/lib/gitlab/usage_data_counters/jetbrains_plugin_activity_unique_counter_spec.rb'
- './spec/lib/gitlab/usage_data_counters/merge_request_activity_unique_counter_spec.rb'
- './spec/lib/gitlab/usage_data_counters/merge_request_widget_extension_counter_spec.rb'
- './spec/lib/gitlab/usage_data_counters/quick_action_activity_unique_counter_spec.rb'
- './spec/lib/gitlab/usage_data_counters/redis_counter_spec.rb'
- './spec/lib/gitlab/usage_data_counters_spec.rb'
- './spec/lib/gitlab/usage_data_counters/vscode_extension_activity_unique_counter_spec.rb'
- './spec/lib/gitlab/usage_data_counters/work_item_activity_unique_counter_spec.rb'
- './spec/lib/gitlab/usage_data_metrics_spec.rb'
- './spec/lib/gitlab/usage_data_non_sql_metrics_spec.rb'
- './spec/lib/gitlab/usage_data_queries_spec.rb'
- './spec/lib/gitlab/usage_data_spec.rb'
- './spec/lib/gitlab/usage_data/topology_spec.rb'
- './spec/lib/gitlab/usage/metric_definition_spec.rb'
- './spec/lib/gitlab/usage/metrics/aggregates/sources/postgres_hll_spec.rb'
- './spec/lib/gitlab/usage/metrics/instrumentations/active_user_count_metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/instrumentations/cert_based_clusters_ff_metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/instrumentations/collected_data_categories_metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/instrumentations/count_boards_metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/instrumentations/count_bulk_imports_entities_metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/instrumentations/count_imported_projects_metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/instrumentations/count_imported_projects_total_metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/instrumentations/count_issues_metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/instrumentations/count_users_associating_milestones_to_releases_metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/instrumentations/count_users_creating_issues_metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/instrumentations/database_metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/instrumentations/generic_metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/instrumentations/hostname_metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/instrumentations/jira_imports_total_imported_issues_count_metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/instrumentations/numbers_metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/instrumentations/redis_hll_metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/instrumentations/redis_metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/instrumentations/service_ping_features_metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/instrumentations/snowplow_configured_to_gitlab_collector_metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/instrumentations/snowplow_enabled_metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/instrumentations/uuid_metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/key_path_processor_spec.rb'
- './spec/lib/gitlab/usage/metric_spec.rb'
- './spec/lib/gitlab/usage/metrics/query_spec.rb'
- './spec/lib/gitlab/usage/service_ping/instrumented_payload_spec.rb'
- './spec/lib/gitlab/usage/service_ping/legacy_metric_metadata_decorator_spec.rb'
- './spec/lib/gitlab/usage/service_ping/payload_keys_processor_spec.rb'
- './spec/lib/gitlab/usage/service_ping_report_spec.rb'
- './spec/lib/gitlab/user_access_snippet_spec.rb'
- './spec/lib/gitlab/user_access_spec.rb'
- './spec/lib/gitlab/uuid_spec.rb'
- './spec/lib/gitlab/verify/job_artifacts_spec.rb'
- './spec/lib/gitlab/verify/lfs_objects_spec.rb'
- './spec/lib/gitlab/verify/uploads_spec.rb'
- './spec/lib/gitlab/view/presenter/base_spec.rb'
- './spec/lib/gitlab/view/presenter/delegated_spec.rb'
- './spec/lib/gitlab/view/presenter/factory_spec.rb'
- './spec/lib/gitlab/view/presenter/simple_spec.rb'
- './spec/lib/gitlab/visibility_level_checker_spec.rb'
- './spec/lib/gitlab/visibility_level_spec.rb'
- './spec/lib/gitlab/web_hooks/rate_limiter_spec.rb'
- './spec/lib/gitlab/web_hooks/recursion_detection_spec.rb'
- './spec/lib/web_ide/config/entry/global_spec.rb'
- './spec/lib/web_ide/config/entry/terminal_spec.rb'
- './spec/lib/web_ide/config_spec.rb'
- './spec/lib/gitlab/webpack/file_loader_spec.rb'
- './spec/lib/gitlab/webpack/graphql_known_operations_spec.rb'
- './spec/lib/gitlab/webpack/manifest_spec.rb'
- './spec/lib/gitlab/wiki_file_finder_spec.rb'
- './spec/lib/gitlab/wiki_pages/front_matter_parser_spec.rb'
- './spec/lib/gitlab/word_diff/chunk_collection_spec.rb'
- './spec/lib/gitlab/word_diff/line_processor_spec.rb'
- './spec/lib/gitlab/word_diff/parser_spec.rb'
- './spec/lib/gitlab/word_diff/positions_counter_spec.rb'
- './spec/lib/gitlab/word_diff/segments/chunk_spec.rb'
- './spec/lib/gitlab/word_diff/segments/diff_hunk_spec.rb'
- './spec/lib/gitlab/word_diff/segments/newline_spec.rb'
- './spec/lib/gitlab/workhorse_spec.rb'
- './spec/lib/gitlab/x509/certificate_spec.rb'
- './spec/lib/gitlab/x509/commit_spec.rb'
- './spec/lib/gitlab/x509/signature_spec.rb'
- './spec/lib/gitlab/x509/tag_spec.rb'
- './spec/lib/gitlab/zentao/client_spec.rb'
- './spec/lib/gitlab/zentao/query_spec.rb'
- './spec/lib/gitlab/zoom_link_extractor_spec.rb'
- './spec/lib/google_api/auth_spec.rb'
- './spec/lib/google_api/cloud_platform/client_spec.rb'
- './spec/lib/grafana/client_spec.rb'
- './spec/lib/grafana/validator_spec.rb'
- './spec/lib/initializer_connections_spec.rb'
- './spec/lib/json_web_token/hmac_token_spec.rb'
- './spec/lib/json_web_token/rsa_token_spec.rb'
- './spec/lib/json_web_token/token_spec.rb'
- './spec/lib/kramdown/kramdown_spec.rb'
- './spec/lib/kramdown/parser/atlassian_document_format_spec.rb'
- './spec/lib/marginalia_spec.rb'
- './spec/lib/mattermost/client_spec.rb'
- './spec/lib/mattermost/command_spec.rb'
- './spec/lib/mattermost/session_spec.rb'
- './spec/lib/mattermost/team_spec.rb'
- './spec/lib/microsoft_teams/activity_spec.rb'
- './spec/lib/microsoft_teams/notifier_spec.rb'
- './spec/lib/object_storage/config_spec.rb'
- './spec/lib/object_storage/direct_upload_spec.rb'
- './spec/lib/omni_auth/strategies/jwt_spec.rb'
- './spec/lib/pager_duty/webhook_payload_parser_spec.rb'
- './spec/lib/peek/views/active_record_spec.rb'
- './spec/lib/peek/views/bullet_detailed_spec.rb'
- './spec/lib/peek/views/detailed_view_spec.rb'
- './spec/lib/peek/views/external_http_spec.rb'
- './spec/lib/peek/views/memory_spec.rb'
- './spec/lib/peek/views/redis_detailed_spec.rb'
- './spec/lib/prometheus/cleanup_multiproc_dir_service_spec.rb'
- './spec/lib/prometheus/pid_provider_spec.rb'
- './spec/lib/quality/seeders/issues_spec.rb'
- './spec/lib/release_highlights/validator/entry_spec.rb'
- './spec/lib/release_highlights/validator_spec.rb'
- './spec/lib/rouge/formatters/html_gitlab_spec.rb'
- './spec/lib/safe_zip/entry_spec.rb'
- './spec/lib/safe_zip/extract_params_spec.rb'
- './spec/lib/safe_zip/extract_spec.rb'
- './spec/lib/security/ci_configuration/container_scanning_build_action_spec.rb'
- './spec/lib/security/ci_configuration/sast_build_action_spec.rb'
- './spec/lib/security/ci_configuration/sast_iac_build_action_spec.rb'
- './spec/lib/security/ci_configuration/secret_detection_build_action_spec.rb'
- './spec/lib/security/report_schema_version_matcher_spec.rb'
- './spec/lib/serializers/unsafe_json_spec.rb'
- './spec/lib/service_ping/build_payload_spec.rb'
- './spec/lib/service_ping/devops_report_spec.rb'
- './spec/lib/service_ping/permit_data_categories_spec.rb'
- './spec/lib/service_ping/service_ping_settings_spec.rb'
- './spec/lib/sidebars/concerns/container_with_html_options_spec.rb'
- './spec/lib/sidebars/concerns/link_with_html_options_spec.rb'
- './spec/lib/sidebars/groups/menus/ci_cd_menu_spec.rb'
- './spec/lib/sidebars/groups/menus/group_information_menu_spec.rb'
- './spec/lib/sidebars/groups/menus/issues_menu_spec.rb'
- './spec/lib/sidebars/groups/menus/kubernetes_menu_spec.rb'
- './spec/lib/sidebars/groups/menus/merge_requests_menu_spec.rb'
- './spec/lib/sidebars/groups/menus/packages_registries_menu_spec.rb'
- './spec/lib/sidebars/groups/menus/scope_menu_spec.rb'
- './spec/lib/sidebars/groups/menus/settings_menu_spec.rb'
- './spec/lib/sidebars/menu_item_spec.rb'
- './spec/lib/sidebars/menu_spec.rb'
- './spec/lib/sidebars/panel_spec.rb'
- './spec/lib/sidebars/projects/context_spec.rb'
- './spec/lib/sidebars/projects/menus/ci_cd_menu_spec.rb'
- './spec/lib/sidebars/projects/menus/confluence_menu_spec.rb'
- './spec/lib/sidebars/projects/menus/deployments_menu_spec.rb'
- './spec/lib/sidebars/projects/menus/external_issue_tracker_menu_spec.rb'
- './spec/lib/sidebars/projects/menus/external_wiki_menu_spec.rb'
- './spec/lib/sidebars/projects/menus/hidden_menu_spec.rb'
- './spec/lib/sidebars/projects/menus/infrastructure_menu_spec.rb'
- './spec/lib/sidebars/projects/menus/issues_menu_spec.rb'
- './spec/lib/sidebars/projects/menus/merge_requests_menu_spec.rb'
- './spec/lib/sidebars/projects/menus/monitor_menu_spec.rb'
- './spec/lib/sidebars/projects/menus/packages_registries_menu_spec.rb'
- './spec/lib/sidebars/projects/menus/project_information_menu_spec.rb'
- './spec/lib/sidebars/projects/menus/repository_menu_spec.rb'
- './spec/lib/sidebars/projects/menus/scope_menu_spec.rb'
- './spec/lib/sidebars/projects/menus/security_compliance_menu_spec.rb'
- './spec/lib/sidebars/projects/menus/settings_menu_spec.rb'
- './spec/lib/sidebars/projects/menus/snippets_menu_spec.rb'
- './spec/lib/sidebars/projects/menus/wiki_menu_spec.rb'
- './spec/lib/sidebars/projects/menus/zentao_menu_spec.rb'
- './spec/lib/sidebars/projects/panel_spec.rb'
- './spec/lib/unnested_in_filters/dsl_spec.rb'
- './spec/lib/unnested_in_filters/rewriter_spec.rb'
- './spec/lib/uploaded_file_spec.rb'
- './spec/mailers/abuse_report_mailer_spec.rb'
- './spec/mailers/devise_mailer_spec.rb'
- './spec/mailers/email_rejection_mailer_spec.rb'
- './spec/mailers/emails/admin_notification_spec.rb'
- './spec/mailers/emails/auto_devops_spec.rb'
- './spec/mailers/emails/groups_spec.rb'
- './spec/mailers/emails/issues_spec.rb'
- './spec/mailers/emails/merge_requests_spec.rb'
- './spec/mailers/emails/pages_domains_spec.rb'
- './spec/mailers/emails/pipelines_spec.rb'
- './spec/mailers/emails/profile_spec.rb'
- './spec/mailers/emails/projects_spec.rb'
- './spec/mailers/emails/releases_spec.rb'
- './spec/mailers/emails/service_desk_spec.rb'
- './spec/mailers/notify_spec.rb'
- './spec/mailers/repository_check_mailer_spec.rb'
- './spec/metrics_server/metrics_server_spec.rb'
- './spec/migrations/active_record/schema_spec.rb'
- './spec/models/ability_spec.rb'
- './spec/models/abuse_report_spec.rb'
- './spec/models/active_session_spec.rb'
- './spec/models/alerting/project_alerting_setting_spec.rb'
- './spec/models/alert_management/alert_assignee_spec.rb'
- './spec/models/alert_management/alert_spec.rb'
- './spec/models/alert_management/alert_user_mention_spec.rb'
- './spec/models/alert_management/http_integration_spec.rb'
- './spec/models/alert_management/metric_image_spec.rb'
- './spec/models/analytics/cycle_analytics/aggregation_spec.rb'
- './spec/models/analytics/cycle_analytics/issue_stage_event_spec.rb'
- './spec/models/analytics/cycle_analytics/merge_request_stage_event_spec.rb'
- './spec/models/analytics/cycle_analytics/stage_event_hash_spec.rb'
- './spec/models/analytics/usage_trends/measurement_spec.rb'
- './spec/models/appearance_spec.rb'
- './spec/models/application_record_spec.rb'
- './spec/models/application_setting/term_spec.rb'
- './spec/models/approval_spec.rb'
- './spec/models/atlassian/identity_spec.rb'
- './spec/models/audit_event_spec.rb'
- './spec/models/authentication_event_spec.rb'
- './spec/models/award_emoji_spec.rb'
- './spec/models/aws/role_spec.rb'
- './spec/models/badges/group_badge_spec.rb'
- './spec/models/badge_spec.rb'
- './spec/models/badges/project_badge_spec.rb'
- './spec/models/blob_spec.rb'
- './spec/models/blob_viewer/base_spec.rb'
- './spec/models/blob_viewer/changelog_spec.rb'
- './spec/models/blob_viewer/composer_json_spec.rb'
- './spec/models/blob_viewer/gemspec_spec.rb'
- './spec/models/blob_viewer/gitlab_ci_yml_spec.rb'
- './spec/models/blob_viewer/go_mod_spec.rb'
- './spec/models/blob_viewer/license_spec.rb'
- './spec/models/blob_viewer/markup_spec.rb'
- './spec/models/blob_viewer/package_json_spec.rb'
- './spec/models/blob_viewer/podspec_json_spec.rb'
- './spec/models/blob_viewer/podspec_spec.rb'
- './spec/models/blob_viewer/readme_spec.rb'
- './spec/models/blob_viewer/route_map_spec.rb'
- './spec/models/blob_viewer/server_side_spec.rb'
- './spec/models/board_group_recent_visit_spec.rb'
- './spec/models/board_project_recent_visit_spec.rb'
- './spec/models/board_spec.rb'
- './spec/models/bulk_imports/configuration_spec.rb'
- './spec/models/bulk_imports/entity_spec.rb'
- './spec/models/bulk_imports/export_spec.rb'
- './spec/models/bulk_imports/export_status_spec.rb'
- './spec/models/bulk_imports/export_upload_spec.rb'
- './spec/models/bulk_imports/failure_spec.rb'
- './spec/models/bulk_imports/file_transfer/group_config_spec.rb'
- './spec/models/bulk_imports/file_transfer/project_config_spec.rb'
- './spec/models/bulk_imports/file_transfer_spec.rb'
- './spec/models/bulk_import_spec.rb'
- './spec/models/bulk_imports/tracker_spec.rb'
- './spec/models/chat_name_spec.rb'
- './spec/models/chat_team_spec.rb'
- './spec/models/ci/artifact_blob_spec.rb'
- './spec/models/ci/bridge_spec.rb'
- './spec/models/ci/build_dependencies_spec.rb'
- './spec/models/ci/build_metadata_spec.rb'
- './spec/models/ci/build_need_spec.rb'
- './spec/models/ci/build_pending_state_spec.rb'
- './spec/models/ci/build_report_result_spec.rb'
- './spec/models/ci/build_runner_session_spec.rb'
- './spec/models/ci/build_spec.rb'
- './spec/models/ci/build_trace_chunks/database_spec.rb'
- './spec/models/ci/build_trace_chunks/fog_spec.rb'
- './spec/models/ci/build_trace_chunk_spec.rb'
- './spec/models/ci/build_trace_chunks/redis_spec.rb'
- './spec/models/ci/build_trace_metadata_spec.rb'
- './spec/models/ci/build_trace_spec.rb'
- './spec/models/ci/commit_with_pipeline_spec.rb'
- './spec/models/ci/daily_build_group_report_result_spec.rb'
- './spec/models/ci/deleted_object_spec.rb'
- './spec/models/ci/freeze_period_spec.rb'
- './spec/models/ci/group_spec.rb'
- './spec/models/ci/group_variable_spec.rb'
- './spec/models/ci/instance_variable_spec.rb'
- './spec/models/ci/job_artifact_spec.rb'
- './spec/models/ci/job_token/project_scope_link_spec.rb'
- './spec/models/ci/job_token/scope_spec.rb'
- './spec/models/ci/job_variable_spec.rb'
- './spec/models/ci/namespace_mirror_spec.rb'
- './spec/models/ci/pending_build_spec.rb'
- './spec/models/ci/persistent_ref_spec.rb'
- './spec/models/ci/pipeline_artifact_spec.rb'
- './spec/models/ci/pipeline_message_spec.rb'
- './spec/models/ci/pipeline_schedule_spec.rb'
- './spec/models/ci/pipeline_schedule_variable_spec.rb'
- './spec/models/ci/pipeline_spec.rb'
- './spec/models/ci/pipeline_variable_spec.rb'
- './spec/models/ci/processable_spec.rb'
- './spec/models/ci/project_mirror_spec.rb'
- './spec/models/ci/ref_spec.rb'
- './spec/models/ci/resource_group_spec.rb'
- './spec/models/ci/resource_spec.rb'
- './spec/models/ci/runner_namespace_spec.rb'
- './spec/models/ci/runner_project_spec.rb'
- './spec/models/ci/runner_spec.rb'
- './spec/models/ci/runner_version_spec.rb'
- './spec/models/ci/running_build_spec.rb'
- './spec/models/ci/secure_file_spec.rb'
- './spec/models/ci/sources/pipeline_spec.rb'
- './spec/models/ci/stage_spec.rb'
- './spec/models/ci/trigger_spec.rb'
- './spec/models/ci/unit_test_failure_spec.rb'
- './spec/models/ci/unit_test_spec.rb'
- './spec/models/ci/variable_spec.rb'
- './spec/models/commit_collection_spec.rb'
- './spec/models/commit_range_spec.rb'
- './spec/models/commit_signatures/gpg_signature_spec.rb'
- './spec/models/commit_signatures/ssh_signature_spec.rb'
- './spec/models/commit_signatures/x509_commit_signature_spec.rb'
- './spec/models/commit_spec.rb'
- './spec/models/commit_status_spec.rb'
- './spec/models/compare_spec.rb'
- './spec/models/concerns/access_requestable_spec.rb'
- './spec/models/concerns/after_commit_queue_spec.rb'
- './spec/models/concerns/as_cte_spec.rb'
- './spec/models/concerns/atomic_internal_id_spec.rb'
- './spec/models/concerns/avatarable_spec.rb'
- './spec/models/concerns/awardable_spec.rb'
- './spec/models/concerns/batch_destroy_dependent_associations_spec.rb'
- './spec/models/concerns/batch_nullify_dependent_associations_spec.rb'
- './spec/models/concerns/blob_language_from_git_attributes_spec.rb'
- './spec/models/concerns/blocks_unsafe_serialization_spec.rb'
- './spec/models/concerns/bulk_insertable_associations_spec.rb'
- './spec/models/concerns/bulk_insert_safe_spec.rb'
- './spec/models/concerns/cacheable_attributes_spec.rb'
- './spec/models/concerns/cache_markdown_field_spec.rb'
- './spec/models/concerns/case_sensitivity_spec.rb'
- './spec/models/concerns/checksummable_spec.rb'
- './spec/models/concerns/chronic_duration_attribute_spec.rb'
- './spec/models/concerns/ci/artifactable_spec.rb'
- './spec/models/concerns/ci/bulk_insertable_tags_spec.rb'
- './spec/models/concerns/ci/has_ref_spec.rb'
- './spec/models/concerns/ci/has_status_spec.rb'
- './spec/models/concerns/ci/has_variable_spec.rb'
- './spec/models/concerns/ci/maskable_spec.rb'
- './spec/models/concerns/counter_attribute_spec.rb'
- './spec/models/concerns/cron_schedulable_spec.rb'
- './spec/models/concerns/cross_database_modification_spec.rb'
- './spec/models/concerns/database_reflection_spec.rb'
- './spec/models/concerns/delete_with_limit_spec.rb'
- './spec/models/concerns/deployment_platform_spec.rb'
- './spec/models/concerns/deprecated_assignee_spec.rb'
- './spec/models/concerns/discussion_on_diff_spec.rb'
- './spec/models/concerns/each_batch_spec.rb'
- './spec/models/concerns/editable_spec.rb'
- './spec/models/concerns/expirable_spec.rb'
- './spec/models/concerns/faster_cache_keys_spec.rb'
- './spec/models/concerns/featurable_spec.rb'
- './spec/models/concerns/feature_gate_spec.rb'
- './spec/models/concerns/from_except_spec.rb'
- './spec/models/concerns/from_intersect_spec.rb'
- './spec/models/concerns/from_set_operator_spec.rb'
- './spec/models/concerns/from_union_spec.rb'
- './spec/models/concerns/group_descendant_spec.rb'
- './spec/models/concerns/has_environment_scope_spec.rb'
- './spec/models/concerns/has_user_type_spec.rb'
- './spec/models/concerns/id_in_ordered_spec.rb'
- './spec/models/concerns/ignorable_columns_spec.rb'
- './spec/models/concerns/integrations/enable_ssl_verification_spec.rb'
- './spec/models/concerns/integrations/has_data_fields_spec.rb'
- './spec/models/concerns/integrations/reset_secret_fields_spec.rb'
- './spec/models/concerns/issuable_link_spec.rb'
- './spec/models/concerns/legacy_bulk_insert_spec.rb'
- './spec/models/concerns/limitable_spec.rb'
- './spec/models/concerns/loaded_in_group_list_spec.rb'
- './spec/models/concerns/loose_index_scan_spec.rb'
- './spec/models/concerns/manual_inverse_association_spec.rb'
- './spec/models/concerns/mentionable_spec.rb'
- './spec/models/concerns/milestoneable_spec.rb'
- './spec/models/concerns/milestoneish_spec.rb'
- './spec/models/concerns/noteable_spec.rb'
- './spec/models/concerns/nullify_if_blank_spec.rb'
- './spec/models/concerns/optionally_search_spec.rb'
- './spec/models/concerns/participable_spec.rb'
- './spec/models/concerns/partitioned_table_spec.rb'
- './spec/models/concerns/pg_full_text_searchable_spec.rb'
- './spec/models/concerns/presentable_spec.rb'
- './spec/models/concerns/project_api_compatibility_spec.rb'
- './spec/models/concerns/project_features_compatibility_spec.rb'
- './spec/models/concerns/prometheus_adapter_spec.rb'
- './spec/models/concerns/reactive_caching_spec.rb'
- './spec/models/concerns/redactable_spec.rb'
- './spec/models/concerns/redis_cacheable_spec.rb'
- './spec/models/concerns/require_email_verification_spec.rb'
- './spec/models/concerns/resolvable_discussion_spec.rb'
- './spec/models/concerns/resolvable_note_spec.rb'
- './spec/models/concerns/routable_spec.rb'
- './spec/models/concerns/runners_token_prefixable_spec.rb'
- './spec/models/concerns/safe_url_spec.rb'
- './spec/models/concerns/sanitizable_spec.rb'
- './spec/models/concerns/schedulable_spec.rb'
- './spec/models/concerns/sha_attribute_spec.rb'
- './spec/models/concerns/sortable_spec.rb'
- './spec/models/concerns/spammable_spec.rb'
- './spec/models/concerns/strip_attribute_spec.rb'
- './spec/models/concerns/subscribable_spec.rb'
- './spec/models/concerns/taggable_queries_spec.rb'
- './spec/models/concerns/taskable_spec.rb'
- './spec/models/concerns/token_authenticatable_spec.rb'
- './spec/models/concerns/transactions_spec.rb'
- './spec/models/concerns/triggerable_hooks_spec.rb'
- './spec/models/concerns/usage_statistics_spec.rb'
- './spec/models/concerns/vulnerability_finding_signature_helpers_spec.rb'
- './spec/models/concerns/where_composite_spec.rb'
- './spec/models/concerns/x509_serial_number_attribute_spec.rb'
- './spec/models/container_expiration_policy_spec.rb'
- './spec/models/container_registry/event_spec.rb'
- './spec/models/container_repository_spec.rb'
- './spec/models/context_commits_diff_spec.rb'
- './spec/models/custom_emoji_spec.rb'
- './spec/models/customer_relations/contact_spec.rb'
- './spec/models/customer_relations/contact_state_counts_spec.rb'
- './spec/models/customer_relations/issue_contact_spec.rb'
- './spec/models/customer_relations/organization_spec.rb'
- './spec/models/cycle_analytics/project_level_stage_adapter_spec.rb'
- './spec/models/dependency_proxy/blob_spec.rb'
- './spec/models/dependency_proxy/group_setting_spec.rb'
- './spec/models/dependency_proxy/image_ttl_group_policy_spec.rb'
- './spec/models/dependency_proxy/manifest_spec.rb'
- './spec/models/dependency_proxy/registry_spec.rb'
- './spec/models/deploy_key_spec.rb'
- './spec/models/deploy_keys_project_spec.rb'
- './spec/models/deployment_cluster_spec.rb'
- './spec/models/deployment_merge_request_spec.rb'
- './spec/models/deployment_spec.rb'
- './spec/models/deploy_token_spec.rb'
- './spec/models/description_version_spec.rb'
- './spec/models/design_management/action_spec.rb'
- './spec/models/design_management/design_action_spec.rb'
- './spec/models/design_management/design_at_version_spec.rb'
- './spec/models/design_management/design_collection_spec.rb'
- './spec/models/design_management/design_spec.rb'
- './spec/models/design_management/repository_spec.rb'
- './spec/models/design_management/version_spec.rb'
- './spec/models/design_user_mention_spec.rb'
- './spec/models/dev_ops_report/metric_spec.rb'
- './spec/models/diff_discussion_spec.rb'
- './spec/models/diff_note_position_spec.rb'
- './spec/models/diff_note_spec.rb'
- './spec/models/diff_viewer/base_spec.rb'
- './spec/models/diff_viewer/image_spec.rb'
- './spec/models/diff_viewer/server_side_spec.rb'
- './spec/models/discussion_note_spec.rb'
- './spec/models/discussion_spec.rb'
- './spec/models/draft_note_spec.rb'
- './spec/models/email_spec.rb'
- './spec/models/environment_spec.rb'
- './spec/models/environment_status_spec.rb'
- './spec/models/error_tracking/client_key_spec.rb'
- './spec/models/error_tracking/error_event_spec.rb'
- './spec/models/error_tracking/error_spec.rb'
- './spec/models/error_tracking/project_error_tracking_setting_spec.rb'
- './spec/models/event_spec.rb'
- './spec/models/exported_protected_branch_spec.rb'
- './spec/models/external_issue_spec.rb'
- './spec/models/fork_network_member_spec.rb'
- './spec/models/fork_network_spec.rb'
- './spec/models/generic_commit_status_spec.rb'
- './spec/models/gpg_key_spec.rb'
- './spec/models/gpg_key_subkey_spec.rb'
- './spec/models/grafana_integration_spec.rb'
- './spec/models/group/crm_settings_spec.rb'
- './spec/models/group_custom_attribute_spec.rb'
- './spec/models/group_deploy_keys_group_spec.rb'
- './spec/models/group_deploy_key_spec.rb'
- './spec/models/group_deploy_token_spec.rb'
- './spec/models/group_group_link_spec.rb'
- './spec/models/group_import_state_spec.rb'
- './spec/models/group_label_spec.rb'
- './spec/models/groups/feature_setting_spec.rb'
- './spec/models/group_spec.rb'
- './spec/models/hooks/active_hook_filter_spec.rb'
- './spec/models/hooks/project_hook_spec.rb'
- './spec/models/hooks/service_hook_spec.rb'
- './spec/models/hooks/system_hook_spec.rb'
- './spec/models/hooks/web_hook_log_spec.rb'
- './spec/models/hooks/web_hook_spec.rb'
- './spec/models/identity_spec.rb'
- './spec/models/import_export_upload_spec.rb'
- './spec/models/import_failure_spec.rb'
- './spec/models/instance_configuration_spec.rb'
- './spec/models/integrations/asana_spec.rb'
- './spec/models/integrations/assembla_spec.rb'
- './spec/models/integrations/bamboo_spec.rb'
- './spec/models/integrations/base_chat_notification_spec.rb'
- './spec/models/integrations/base_issue_tracker_spec.rb'
- './spec/models/integrations/bugzilla_spec.rb'
- './spec/models/integrations/buildkite_spec.rb'
- './spec/models/integrations/campfire_spec.rb'
- './spec/models/integrations/chat_message/alert_message_spec.rb'
- './spec/models/integrations/chat_message/base_message_spec.rb'
- './spec/models/integrations/chat_message/deployment_message_spec.rb'
- './spec/models/integrations/chat_message/issue_message_spec.rb'
- './spec/models/integrations/chat_message/merge_message_spec.rb'
- './spec/models/integrations/chat_message/note_message_spec.rb'
- './spec/models/integrations/chat_message/pipeline_message_spec.rb'
- './spec/models/integrations/chat_message/push_message_spec.rb'
- './spec/models/integrations/chat_message/wiki_page_message_spec.rb'
- './spec/models/integrations/confluence_spec.rb'
- './spec/models/integrations/custom_issue_tracker_spec.rb'
- './spec/models/integrations/datadog_spec.rb'
- './spec/models/integrations/discord_spec.rb'
- './spec/models/integrations/drone_ci_spec.rb'
- './spec/models/integrations/emails_on_push_spec.rb'
- './spec/models/integrations/every_integration_spec.rb'
- './spec/models/integrations/ewm_spec.rb'
- './spec/models/integrations/external_wiki_spec.rb'
- './spec/models/integrations/field_spec.rb'
- './spec/models/integrations/hangouts_chat_spec.rb'
- './spec/models/integrations/harbor_spec.rb'
- './spec/models/integrations/irker_spec.rb'
- './spec/models/integrations/issue_tracker_data_spec.rb'
- './spec/models/integrations/jenkins_spec.rb'
- './spec/models/integrations/jira_spec.rb'
- './spec/models/integrations/jira_tracker_data_spec.rb'
- './spec/models/integrations/mattermost_slash_commands_spec.rb'
- './spec/models/integrations/mattermost_spec.rb'
- './spec/models/integrations/microsoft_teams_spec.rb'
- './spec/models/integrations/mock_ci_spec.rb'
- './spec/models/integrations/packagist_spec.rb'
- './spec/models/integration_spec.rb'
- './spec/models/integrations/pipelines_email_spec.rb'
- './spec/models/integrations/pivotaltracker_spec.rb'
- './spec/models/integrations/prometheus_spec.rb'
- './spec/models/integrations/pumble_spec.rb'
- './spec/models/integrations/pushover_spec.rb'
- './spec/models/integrations/redmine_spec.rb'
- './spec/models/integrations/slack_slash_commands_spec.rb'
- './spec/models/integrations/slack_spec.rb'
- './spec/models/integrations/teamcity_spec.rb'
- './spec/models/integrations/unify_circuit_spec.rb'
- './spec/models/integrations/webex_teams_spec.rb'
- './spec/models/integrations/youtrack_spec.rb'
- './spec/models/integrations/zentao_spec.rb'
- './spec/models/integrations/zentao_tracker_data_spec.rb'
- './spec/models/internal_id_spec.rb'
- './spec/models/issuable_severity_spec.rb'
- './spec/models/issue_assignee_spec.rb'
- './spec/models/issue_email_participant_spec.rb'
- './spec/models/issue/email_spec.rb'
- './spec/models/issue_link_spec.rb'
- './spec/models/issue/metrics_spec.rb'
- './spec/models/issues/csv_import_spec.rb'
- './spec/models/issue_spec.rb'
- './spec/models/jira_connect_installation_spec.rb'
- './spec/models/jira_connect_subscription_spec.rb'
- './spec/models/jira_import_state_spec.rb'
- './spec/models/key_spec.rb'
- './spec/models/label_link_spec.rb'
- './spec/models/label_note_spec.rb'
- './spec/models/label_priority_spec.rb'
- './spec/models/label_spec.rb'
- './spec/models/legacy_diff_discussion_spec.rb'
- './spec/models/legacy_diff_note_spec.rb'
- './spec/models/lfs_download_object_spec.rb'
- './spec/models/lfs_file_lock_spec.rb'
- './spec/models/lfs_object_spec.rb'
- './spec/models/lfs_objects_project_spec.rb'
- './spec/models/license_template_spec.rb'
- './spec/models/list_spec.rb'
- './spec/models/list_user_preference_spec.rb'
- './spec/models/loose_foreign_keys/deleted_record_spec.rb'
- './spec/models/loose_foreign_keys/modification_tracker_spec.rb'
- './spec/models/members/group_member_spec.rb'
- './spec/models/members/last_group_owner_assigner_spec.rb'
- './spec/models/member_spec.rb'
- './spec/models/members/project_member_spec.rb'
- './spec/models/merge_request/approval_removal_settings_spec.rb'
- './spec/models/merge_request_assignee_spec.rb'
- './spec/models/merge_request/cleanup_schedule_spec.rb'
- './spec/models/merge_request_context_commit_diff_file_spec.rb'
- './spec/models/merge_request_context_commit_spec.rb'
- './spec/models/merge_request_diff_commit_spec.rb'
- './spec/models/merge_request/diff_commit_user_spec.rb'
- './spec/models/merge_request_diff_file_spec.rb'
- './spec/models/merge_request_diff_spec.rb'
- './spec/models/merge_request/metrics_spec.rb'
- './spec/models/merge_request_reviewer_spec.rb'
- './spec/models/merge_request_spec.rb'
- './spec/models/milestone_note_spec.rb'
- './spec/models/milestone_release_spec.rb'
- './spec/models/milestone_spec.rb'
- './spec/models/ml/candidate_metric_spec.rb'
- './spec/models/ml/candidate_param_spec.rb'
- './spec/models/ml/candidate_spec.rb'
- './spec/models/ml/experiment_spec.rb'
- './spec/models/namespace/admin_note_spec.rb'
- './spec/models/namespace/aggregation_schedule_spec.rb'
- './spec/models/namespace_ci_cd_setting_spec.rb'
- './spec/models/namespace/detail_spec.rb'
- './spec/models/namespace/package_setting_spec.rb'
- './spec/models/namespace/root_storage_statistics_spec.rb'
- './spec/models/namespace_setting_spec.rb'
- './spec/models/namespace_spec.rb'
- './spec/models/namespaces/project_namespace_spec.rb'
- './spec/models/namespace_statistics_spec.rb'
- './spec/models/namespaces/user_namespace_spec.rb'
- './spec/models/namespace/traversal_hierarchy_spec.rb'
- './spec/models/network/graph_spec.rb'
- './spec/models/note_diff_file_spec.rb'
- './spec/models/note_spec.rb'
- './spec/models/notification_recipient_spec.rb'
- './spec/models/notification_setting_spec.rb'
- './spec/models/oauth_access_grant_spec.rb'
- './spec/models/oauth_access_token_spec.rb'
- './spec/models/operations/feature_flags_client_spec.rb'
- './spec/models/operations/feature_flag_spec.rb'
- './spec/models/operations/feature_flags/strategy_spec.rb'
- './spec/models/operations/feature_flags/user_list_spec.rb'
- './spec/models/packages/build_info_spec.rb'
- './spec/models/packages/cleanup/policy_spec.rb'
- './spec/models/packages/composer/metadatum_spec.rb'
- './spec/models/packages/conan/file_metadatum_spec.rb'
- './spec/models/packages/conan/metadatum_spec.rb'
- './spec/models/packages/debian/file_entry_spec.rb'
- './spec/models/packages/debian/file_metadatum_spec.rb'
- './spec/models/packages/debian/group_architecture_spec.rb'
- './spec/models/packages/debian/group_component_file_spec.rb'
- './spec/models/packages/debian/group_component_spec.rb'
- './spec/models/packages/debian/group_distribution_key_spec.rb'
- './spec/models/packages/debian/group_distribution_spec.rb'
- './spec/models/packages/debian/project_architecture_spec.rb'
- './spec/models/packages/debian/project_component_file_spec.rb'
- './spec/models/packages/debian/project_component_spec.rb'
- './spec/models/packages/debian/project_distribution_key_spec.rb'
- './spec/models/packages/debian/project_distribution_spec.rb'
- './spec/models/packages/debian/publication_spec.rb'
- './spec/models/packages/dependency_link_spec.rb'
- './spec/models/packages/dependency_spec.rb'
- './spec/models/packages/go/module_spec.rb'
- './spec/models/packages/go/module_version_spec.rb'
- './spec/models/packages/helm/file_metadatum_spec.rb'
- './spec/models/packages/maven/metadatum_spec.rb'
- './spec/models/packages/npm/metadatum_spec.rb'
- './spec/models/packages/npm_spec.rb'
- './spec/models/packages/nuget/dependency_link_metadatum_spec.rb'
- './spec/models/packages/nuget/metadatum_spec.rb'
- './spec/models/packages/package_file_build_info_spec.rb'
- './spec/models/packages/package_spec.rb'
- './spec/models/packages/pypi/metadatum_spec.rb'
- './spec/models/packages/rubygems/metadatum_spec.rb'
- './spec/models/packages/sem_ver_spec.rb'
- './spec/models/packages/tag_spec.rb'
- './spec/models/pages_deployment_spec.rb'
- './spec/models/pages_domain_acme_order_spec.rb'
- './spec/models/pages_domain_spec.rb'
- './spec/models/pages/lookup_path_spec.rb'
- './spec/models/pages/virtual_domain_spec.rb'
- './spec/models/personal_access_token_spec.rb'
- './spec/models/personal_snippet_spec.rb'
- './spec/models/plan_limits_spec.rb'
- './spec/models/plan_spec.rb'
- './spec/models/pool_repository_spec.rb'
- './spec/models/postgresql/detached_partition_spec.rb'
- './spec/models/postgresql/replication_slot_spec.rb'
- './spec/models/preloaders/commit_status_preloader_spec.rb'
- './spec/models/preloaders/environments/deployment_preloader_spec.rb'
- './spec/models/preloaders/group_policy_preloader_spec.rb'
- './spec/models/preloaders/labels_preloader_spec.rb'
- './spec/models/preloaders/merge_request_diff_preloader_spec.rb'
- './spec/models/preloaders/user_max_access_level_in_groups_preloader_spec.rb'
- './spec/models/preloaders/user_max_access_level_in_projects_preloader_spec.rb'
- './spec/models/programming_language_spec.rb'
- './spec/models/project_authorization_spec.rb'
- './spec/models/project_auto_devops_spec.rb'
- './spec/models/project_ci_cd_setting_spec.rb'
- './spec/models/project_custom_attribute_spec.rb'
- './spec/models/project_daily_statistic_spec.rb'
- './spec/models/project_deploy_token_spec.rb'
- './spec/models/project_export_job_spec.rb'
- './spec/models/project_feature_spec.rb'
- './spec/models/project_group_link_spec.rb'
- './spec/models/project_import_data_spec.rb'
- './spec/models/project_import_state_spec.rb'
- './spec/models/project_label_spec.rb'
- './spec/models/project_repository_spec.rb'
- './spec/models/projects/build_artifacts_size_refresh_spec.rb'
- './spec/models/projects/ci_feature_usage_spec.rb'
- './spec/models/project_setting_spec.rb'
- './spec/models/projects/import_export/relation_export_spec.rb'
- './spec/models/projects/import_export/relation_export_upload_spec.rb'
- './spec/models/project_snippet_spec.rb'
- './spec/models/project_spec.rb'
- './spec/models/projects/project_topic_spec.rb'
- './spec/models/projects/repository_storage_move_spec.rb'
- './spec/models/project_statistics_spec.rb'
- './spec/models/projects/topic_spec.rb'
- './spec/models/projects/triggered_hooks_spec.rb'
- './spec/models/project_team_spec.rb'
- './spec/models/project_wiki_spec.rb'
- './spec/models/protectable_dropdown_spec.rb'
- './spec/models/protected_branch/merge_access_level_spec.rb'
- './spec/models/protected_branch/push_access_level_spec.rb'
- './spec/models/protected_branch_spec.rb'
- './spec/models/protected_tag_spec.rb'
- './spec/models/push_event_payload_spec.rb'
- './spec/models/push_event_spec.rb'
- './spec/models/raw_usage_data_spec.rb'
- './spec/models/redirect_route_spec.rb'
- './spec/models/ref_matcher_spec.rb'
- './spec/models/release_highlight_spec.rb'
- './spec/models/releases/evidence_spec.rb'
- './spec/models/releases/link_spec.rb'
- './spec/models/release_spec.rb'
- './spec/models/releases/source_spec.rb'
- './spec/models/remote_mirror_spec.rb'
- './spec/models/repository_language_spec.rb'
- './spec/models/repository_spec.rb'
- './spec/models/resource_label_event_spec.rb'
- './spec/models/resource_milestone_event_spec.rb'
- './spec/models/resource_state_event_spec.rb'
- './spec/models/review_spec.rb'
- './spec/models/route_spec.rb'
- './spec/models/sent_notification_spec.rb'
- './spec/models/sentry_issue_spec.rb'
- './spec/models/service_desk_setting_spec.rb'
- './spec/models/shard_spec.rb'
- './spec/models/snippet_blob_spec.rb'
- './spec/models/snippet_input_action_collection_spec.rb'
- './spec/models/snippet_input_action_spec.rb'
- './spec/models/snippet_repository_spec.rb'
- './spec/models/snippet_spec.rb'
- './spec/models/snippets/repository_storage_move_spec.rb'
- './spec/models/snippet_statistics_spec.rb'
- './spec/models/spam_log_spec.rb'
- './spec/models/ssh_host_key_spec.rb'
- './spec/models/state_note_spec.rb'
- './spec/models/subscription_spec.rb'
- './spec/models/suggestion_spec.rb'
- './spec/models/synthetic_note_spec.rb'
- './spec/models/system_note_metadata_spec.rb'
- './spec/models/term_agreement_spec.rb'
- './spec/models/terraform/state_spec.rb'
- './spec/models/terraform/state_version_spec.rb'
- './spec/models/timelog_spec.rb'
- './spec/models/time_tracking/timelog_category_spec.rb'
- './spec/models/todo_spec.rb'
- './spec/models/tree_spec.rb'
- './spec/models/trending_project_spec.rb'
- './spec/models/uploads/fog_spec.rb'
- './spec/models/uploads/local_spec.rb'
- './spec/models/upload_spec.rb'
- './spec/models/user_agent_detail_spec.rb'
- './spec/models/user_custom_attribute_spec.rb'
- './spec/models/user_detail_spec.rb'
- './spec/models/user_highest_role_spec.rb'
- './spec/models/user_mentions/commit_user_mention_spec.rb'
- './spec/models/user_mentions/issue_user_mention_spec.rb'
- './spec/models/user_mentions/merge_request_user_mention_spec.rb'
- './spec/models/user_mentions/snippet_user_mention_spec.rb'
- './spec/models/user_preference_spec.rb'
- './spec/models/users/banned_user_spec.rb'
- './spec/models/users/calloutable_spec.rb'
- './spec/models/users/callout_spec.rb'
- './spec/models/users/credit_card_validation_spec.rb'
- './spec/models/users/group_callout_spec.rb'
- './spec/models/users/merge_request_interaction_spec.rb'
- './spec/models/user_spec.rb'
- './spec/models/users/project_callout_spec.rb'
- './spec/models/users/saved_reply_spec.rb'
- './spec/models/users_star_project_spec.rb'
- './spec/models/users_statistics_spec.rb'
- './spec/models/user_status_spec.rb'
- './spec/models/webauthn_registration_spec.rb'
- './spec/models/web_ide_terminal_spec.rb'
- './spec/models/wiki_directory_spec.rb'
- './spec/models/wiki_page/meta_spec.rb'
- './spec/models/wiki_page/slug_spec.rb'
- './spec/models/wiki_page_spec.rb'
- './spec/models/work_items/parent_link_spec.rb'
- './spec/models/work_item_spec.rb'
- './spec/models/work_items/type_spec.rb'
- './spec/models/work_items/widgets/assignees_spec.rb'
- './spec/models/work_items/widgets/base_spec.rb'
- './spec/models/work_items/widgets/description_spec.rb'
- './spec/models/work_items/widgets/hierarchy_spec.rb'
- './spec/models/work_items/widgets/labels_spec.rb'
- './spec/models/work_items/widgets/start_and_due_date_spec.rb'
- './spec/models/x509_certificate_spec.rb'
- './spec/models/x509_issuer_spec.rb'
- './spec/models/zoom_meeting_spec.rb'
- './spec/policies/alert_management/alert_policy_spec.rb'
- './spec/policies/alert_management/http_integration_policy_spec.rb'
- './spec/policies/application_setting_policy_spec.rb'
- './spec/policies/application_setting/term_policy_spec.rb'
- './spec/policies/award_emoji_policy_spec.rb'
- './spec/policies/base_policy_spec.rb'
- './spec/policies/blob_policy_spec.rb'
- './spec/policies/board_policy_spec.rb'
- './spec/policies/ci/bridge_policy_spec.rb'
- './spec/policies/ci/build_policy_spec.rb'
- './spec/policies/ci/pipeline_policy_spec.rb'
- './spec/policies/ci/pipeline_schedule_policy_spec.rb'
- './spec/policies/ci/trigger_policy_spec.rb'
- './spec/policies/clusters/agent_policy_spec.rb'
- './spec/policies/clusters/agents/activity_event_policy_spec.rb'
- './spec/policies/clusters/agent_token_policy_spec.rb'
- './spec/policies/clusters/cluster_policy_spec.rb'
- './spec/policies/clusters/instance_policy_spec.rb'
- './spec/policies/commit_policy_spec.rb'
- './spec/policies/concerns/policy_actor_spec.rb'
- './spec/policies/container_expiration_policy_policy_spec.rb'
- './spec/policies/custom_emoji_policy_spec.rb'
- './spec/policies/deploy_key_policy_spec.rb'
- './spec/policies/deploy_keys_project_policy_spec.rb'
- './spec/policies/deploy_token_policy_spec.rb'
- './spec/policies/design_management/design_policy_spec.rb'
- './spec/policies/environment_policy_spec.rb'
- './spec/policies/global_policy_spec.rb'
- './spec/policies/group_deploy_key_policy_spec.rb'
- './spec/policies/group_deploy_keys_group_policy_spec.rb'
- './spec/policies/group_member_policy_spec.rb'
- './spec/policies/identity_provider_policy_spec.rb'
- './spec/policies/instance_metadata_policy_spec.rb'
- './spec/policies/integration_policy_spec.rb'
- './spec/policies/issuable_policy_spec.rb'
- './spec/policies/issue_policy_spec.rb'
- './spec/policies/merge_request_policy_spec.rb'
- './spec/policies/namespace/root_storage_statistics_policy_spec.rb'
- './spec/policies/namespaces/project_namespace_policy_spec.rb'
- './spec/policies/namespaces/user_namespace_policy_spec.rb'
- './spec/policies/note_policy_spec.rb'
- './spec/policies/packages/package_policy_spec.rb'
- './spec/policies/packages/policies/group_policy_spec.rb'
- './spec/policies/packages/policies/project_policy_spec.rb'
- './spec/policies/personal_access_token_policy_spec.rb'
- './spec/policies/personal_snippet_policy_spec.rb'
- './spec/policies/project_hook_policy_spec.rb'
- './spec/policies/project_member_policy_spec.rb'
- './spec/policies/project_policy_spec.rb'
- './spec/policies/project_snippet_policy_spec.rb'
- './spec/policies/project_statistics_policy_spec.rb'
- './spec/policies/protected_branch_policy_spec.rb'
- './spec/policies/release_policy_spec.rb'
- './spec/policies/resource_label_event_policy_spec.rb'
- './spec/policies/system_hook_policy_spec.rb'
- './spec/policies/terraform/state_policy_spec.rb'
- './spec/policies/terraform/state_version_policy_spec.rb'
- './spec/policies/timelog_policy_spec.rb'
- './spec/policies/todo_policy_spec.rb'
- './spec/policies/upload_policy_spec.rb'
- './spec/policies/user_policy_spec.rb'
- './spec/policies/wiki_page_policy_spec.rb'
- './spec/policies/work_item_policy_spec.rb'
- './spec/presenters/alert_management/alert_presenter_spec.rb'
- './spec/presenters/award_emoji_presenter_spec.rb'
- './spec/presenters/blob_presenter_spec.rb'
- './spec/presenters/blobs/notebook_presenter_spec.rb'
- './spec/presenters/ci/bridge_presenter_spec.rb'
- './spec/presenters/ci/build_presenter_spec.rb'
- './spec/presenters/ci/build_runner_presenter_spec.rb'
- './spec/presenters/ci/group_variable_presenter_spec.rb'
- './spec/presenters/ci/pipeline_artifacts/code_coverage_presenter_spec.rb'
- './spec/presenters/ci/pipeline_artifacts/code_quality_mr_diff_presenter_spec.rb'
- './spec/presenters/ci/pipeline_presenter_spec.rb'
- './spec/presenters/ci/stage_presenter_spec.rb'
- './spec/presenters/ci/trigger_presenter_spec.rb'
- './spec/presenters/ci/variable_presenter_spec.rb'
- './spec/presenters/clusterable_presenter_spec.rb'
- './spec/presenters/clusters/cluster_presenter_spec.rb'
- './spec/presenters/commit_presenter_spec.rb'
- './spec/presenters/commit_status_presenter_spec.rb'
- './spec/presenters/dev_ops_report/metric_presenter_spec.rb'
- './spec/presenters/event_presenter_spec.rb'
- './spec/presenters/gitlab/blame_presenter_spec.rb'
- './spec/presenters/group_clusterable_presenter_spec.rb'
- './spec/presenters/group_member_presenter_spec.rb'
- './spec/presenters/instance_clusterable_presenter_spec.rb'
- './spec/presenters/issue_presenter_spec.rb'
- './spec/presenters/label_presenter_spec.rb'
- './spec/presenters/merge_request_presenter_spec.rb'
- './spec/presenters/milestone_presenter_spec.rb'
- './spec/presenters/packages/composer/packages_presenter_spec.rb'
- './spec/presenters/packages/conan/package_presenter_spec.rb'
- './spec/presenters/packages/nuget/package_metadata_presenter_spec.rb'
- './spec/presenters/packages/nuget/packages_metadata_presenter_spec.rb'
- './spec/presenters/packages/nuget/packages_versions_presenter_spec.rb'
- './spec/presenters/packages/nuget/search_results_presenter_spec.rb'
- './spec/presenters/packages/nuget/service_index_presenter_spec.rb'
- './spec/presenters/packages/pypi/simple_index_presenter_spec.rb'
- './spec/presenters/packages/pypi/simple_package_versions_presenter_spec.rb'
- './spec/presenters/pages_domain_presenter_spec.rb'
- './spec/presenters/project_clusterable_presenter_spec.rb'
- './spec/presenters/project_hook_presenter_spec.rb'
- './spec/presenters/project_member_presenter_spec.rb'
- './spec/presenters/project_presenter_spec.rb'
- './spec/presenters/projects/import_export/project_export_presenter_spec.rb'
- './spec/presenters/projects/security/configuration_presenter_spec.rb'
- './spec/presenters/projects/settings/deploy_keys_presenter_spec.rb'
- './spec/presenters/release_presenter_spec.rb'
- './spec/presenters/releases/link_presenter_spec.rb'
- './spec/presenters/search_service_presenter_spec.rb'
- './spec/presenters/sentry_error_presenter_spec.rb'
- './spec/presenters/service_hook_presenter_spec.rb'
- './spec/presenters/snippet_blob_presenter_spec.rb'
- './spec/presenters/snippet_presenter_spec.rb'
- './spec/presenters/terraform/modules_presenter_spec.rb'
- './spec/presenters/tree_entry_presenter_spec.rb'
- './spec/presenters/user_presenter_spec.rb'
- './spec/presenters/web_hook_log_presenter_spec.rb'
- './spec/rack_servers/puma_spec.rb'
- './spec/requests/abuse_reports_controller_spec.rb'
- './spec/requests/admin/applications_controller_spec.rb'
- './spec/requests/admin/background_migrations_controller_spec.rb'
- './spec/requests/admin/batched_jobs_controller_spec.rb'
- './spec/requests/admin/broadcast_messages_controller_spec.rb'
- './spec/requests/admin/impersonation_tokens_controller_spec.rb'
- './spec/requests/admin/integrations_controller_spec.rb'
- './spec/requests/admin/version_check_controller_spec.rb'
- './spec/requests/api/access_requests_spec.rb'
- './spec/requests/api/admin/batched_background_migrations_spec.rb'
- './spec/requests/api/admin/broadcast_messages_spec.rb'
- './spec/requests/api/admin/ci/variables_spec.rb'
- './spec/requests/api/admin/instance_clusters_spec.rb'
- './spec/requests/api/admin/plan_limits_spec.rb'
- './spec/requests/api/admin/sidekiq_spec.rb'
- './spec/requests/api/alert_management_alerts_spec.rb'
- './spec/requests/api/api_guard/admin_mode_middleware_spec.rb'
- './spec/requests/api/api_guard/response_coercer_middleware_spec.rb'
- './spec/requests/api/api_spec.rb'
- './spec/requests/api/appearance_spec.rb'
- './spec/requests/api/applications_spec.rb'
- './spec/requests/api/avatar_spec.rb'
- './spec/requests/api/award_emoji_spec.rb'
- './spec/requests/api/badges_spec.rb'
- './spec/requests/api/boards_spec.rb'
- './spec/requests/api/branches_spec.rb'
- './spec/requests/api/bulk_imports_spec.rb'
- './spec/requests/api/ci/job_artifacts_spec.rb'
- './spec/requests/api/ci/jobs_spec.rb'
- './spec/requests/api/ci/pipeline_schedules_spec.rb'
- './spec/requests/api/ci/pipelines_spec.rb'
- './spec/requests/api/ci/resource_groups_spec.rb'
- './spec/requests/api/ci/runner/jobs_artifacts_spec.rb'
- './spec/requests/api/ci/runner/jobs_put_spec.rb'
- './spec/requests/api/ci/runner/jobs_request_post_spec.rb'
- './spec/requests/api/ci/runner/jobs_trace_spec.rb'
- './spec/requests/api/ci/runner/runners_delete_spec.rb'
- './spec/requests/api/ci/runner/runners_post_spec.rb'
- './spec/requests/api/ci/runner/runners_reset_spec.rb'
- './spec/requests/api/ci/runner/runners_verify_post_spec.rb'
- './spec/requests/api/ci/runners_reset_registration_token_spec.rb'
- './spec/requests/api/ci/runners_spec.rb'
- './spec/requests/api/ci/secure_files_spec.rb'
- './spec/requests/api/ci/triggers_spec.rb'
- './spec/requests/api/ci/variables_spec.rb'
- './spec/requests/api/clusters/agents_spec.rb'
- './spec/requests/api/clusters/agent_tokens_spec.rb'
- './spec/requests/api/commits_spec.rb'
- './spec/requests/api/commit_statuses_spec.rb'
- './spec/requests/api/composer_packages_spec.rb'
- './spec/requests/api/container_registry_event_spec.rb'
- './spec/requests/api/container_repositories_spec.rb'
- './spec/requests/api/debian_group_packages_spec.rb'
- './spec/requests/api/debian_project_packages_spec.rb'
- './spec/requests/api/dependency_proxy_spec.rb'
- './spec/requests/api/deploy_keys_spec.rb'
- './spec/requests/api/deployments_spec.rb'
- './spec/requests/api/deploy_tokens_spec.rb'
- './spec/requests/api/discussions_spec.rb'
- './spec/requests/api/doorkeeper_access_spec.rb'
- './spec/requests/api/environments_spec.rb'
- './spec/requests/api/error_tracking/client_keys_spec.rb'
- './spec/requests/api/events_spec.rb'
- './spec/requests/api/feature_flags_spec.rb'
- './spec/requests/api/feature_flags_user_lists_spec.rb'
- './spec/requests/api/features_spec.rb'
- './spec/requests/api/files_spec.rb'
- './spec/requests/api/freeze_periods_spec.rb'
- './spec/requests/api/generic_packages_spec.rb'
- './spec/requests/api/geo_spec.rb'
- './spec/requests/api/go_proxy_spec.rb'
- './spec/requests/api/graphql/boards/board_list_issues_query_spec.rb'
- './spec/requests/api/graphql/boards/board_list_query_spec.rb'
- './spec/requests/api/graphql/boards/board_lists_query_spec.rb'
- './spec/requests/api/graphql/boards/boards_query_spec.rb'
- './spec/requests/api/graphql/ci/application_setting_spec.rb'
- './spec/requests/api/graphql/ci/ci_cd_setting_spec.rb'
- './spec/requests/api/graphql/ci/config_spec.rb'
- './spec/requests/api/graphql/ci/groups_spec.rb'
- './spec/requests/api/graphql/ci/group_variables_spec.rb'
- './spec/requests/api/graphql/ci/instance_variables_spec.rb'
- './spec/requests/api/graphql/ci/job_artifacts_spec.rb'
- './spec/requests/api/graphql/ci/job_spec.rb'
- './spec/requests/api/graphql/ci/jobs_spec.rb'
- './spec/requests/api/graphql/ci/manual_variables_spec.rb'
- './spec/requests/api/graphql/ci/pipelines_spec.rb'
- './spec/requests/api/graphql/ci/project_variables_spec.rb'
- './spec/requests/api/graphql/ci/runner_spec.rb'
- './spec/requests/api/graphql/ci/runner_web_url_edge_spec.rb'
- './spec/requests/api/graphql/ci/stages_spec.rb'
- './spec/requests/api/graphql/ci/template_spec.rb'
- './spec/requests/api/graphql/container_repository/container_repository_details_spec.rb'
- './spec/requests/api/graphql/crm/contacts_spec.rb'
- './spec/requests/api/graphql/current_user/groups_query_spec.rb'
- './spec/requests/api/graphql/current_user_query_spec.rb'
- './spec/requests/api/graphql/current_user/todos_query_spec.rb'
- './spec/requests/api/graphql/current_user_todos_spec.rb'
- './spec/requests/api/graphql/custom_emoji_query_spec.rb'
- './spec/requests/api/graphql/gitlab_schema_spec.rb'
- './spec/requests/api/graphql/group/container_repositories_spec.rb'
- './spec/requests/api/graphql/group/dependency_proxy_blobs_spec.rb'
- './spec/requests/api/graphql/group/dependency_proxy_group_setting_spec.rb'
- './spec/requests/api/graphql/group/dependency_proxy_image_ttl_policy_spec.rb'
- './spec/requests/api/graphql/group/dependency_proxy_manifests_spec.rb'
- './spec/requests/api/graphql/group/group_members_spec.rb'
- './spec/requests/api/graphql/group/issues_spec.rb'
- './spec/requests/api/graphql/group/merge_requests_spec.rb'
- './spec/requests/api/graphql/group/milestones_spec.rb'
- './spec/requests/api/graphql/group/packages_spec.rb'
- './spec/requests/api/graphql/group_query_spec.rb'
- './spec/requests/api/graphql/group/recent_issue_boards_query_spec.rb'
- './spec/requests/api/graphql/group/timelogs_spec.rb'
- './spec/requests/api/graphql/group/work_item_types_spec.rb'
- './spec/requests/api/graphql/issue/issue_spec.rb'
- './spec/requests/api/graphql/issue_status_counts_spec.rb'
- './spec/requests/api/graphql/merge_request/merge_request_spec.rb'
- './spec/requests/api/graphql/metadata_query_spec.rb'
- './spec/requests/api/graphql/milestone_spec.rb'
- './spec/requests/api/graphql/multiplexed_queries_spec.rb'
- './spec/requests/api/graphql/mutations/admin/sidekiq_queues/delete_jobs_spec.rb'
- './spec/requests/api/graphql/mutations/alert_management/alerts/create_alert_issue_spec.rb'
- './spec/requests/api/graphql/mutations/alert_management/alerts/set_assignees_spec.rb'
- './spec/requests/api/graphql/mutations/alert_management/alerts/todo/create_spec.rb'
- './spec/requests/api/graphql/mutations/alert_management/alerts/update_alert_status_spec.rb'
- './spec/requests/api/graphql/mutations/alert_management/http_integration/create_spec.rb'
- './spec/requests/api/graphql/mutations/alert_management/http_integration/destroy_spec.rb'
- './spec/requests/api/graphql/mutations/alert_management/http_integration/reset_token_spec.rb'
- './spec/requests/api/graphql/mutations/alert_management/http_integration/update_spec.rb'
- './spec/requests/api/graphql/mutations/alert_management/prometheus_integration/create_spec.rb'
- './spec/requests/api/graphql/mutations/alert_management/prometheus_integration/reset_token_spec.rb'
- './spec/requests/api/graphql/mutations/alert_management/prometheus_integration/update_spec.rb'
- './spec/requests/api/graphql/mutations/award_emojis/add_spec.rb'
- './spec/requests/api/graphql/mutations/award_emojis/remove_spec.rb'
- './spec/requests/api/graphql/mutations/award_emojis/toggle_spec.rb'
- './spec/requests/api/graphql/mutations/boards/create_spec.rb'
- './spec/requests/api/graphql/mutations/boards/destroy_spec.rb'
- './spec/requests/api/graphql/mutations/boards/issues/issue_move_list_spec.rb'
- './spec/requests/api/graphql/mutations/boards/lists/create_spec.rb'
- './spec/requests/api/graphql/mutations/boards/lists/destroy_spec.rb'
- './spec/requests/api/graphql/mutations/boards/lists/update_spec.rb'
- './spec/requests/api/graphql/mutations/branches/create_spec.rb'
- './spec/requests/api/graphql/mutations/ci/job_token_scope/add_project_spec.rb'
- './spec/requests/api/graphql/mutations/ci/job_token_scope/remove_project_spec.rb'
- './spec/requests/api/graphql/mutations/ci/pipeline_cancel_spec.rb'
- './spec/requests/api/graphql/mutations/ci/pipeline_destroy_spec.rb'
- './spec/requests/api/graphql/mutations/ci/pipeline_retry_spec.rb'
- './spec/requests/api/graphql/mutations/ci/runners_registration_token/reset_spec.rb'
- './spec/requests/api/graphql/mutations/clusters/agents/create_spec.rb'
- './spec/requests/api/graphql/mutations/clusters/agents/delete_spec.rb'
- './spec/requests/api/graphql/mutations/clusters/agent_tokens/agent_tokens/create_spec.rb'
- './spec/requests/api/graphql/mutations/commits/create_spec.rb'
- './spec/requests/api/graphql/mutations/container_expiration_policy/update_spec.rb'
- './spec/requests/api/graphql/mutations/container_repository/destroy_spec.rb'
- './spec/requests/api/graphql/mutations/container_repository/destroy_tags_spec.rb'
- './spec/requests/api/graphql/mutations/custom_emoji/create_spec.rb'
- './spec/requests/api/graphql/mutations/custom_emoji/destroy_spec.rb'
- './spec/requests/api/graphql/mutations/dependency_proxy/group_settings/update_spec.rb'
- './spec/requests/api/graphql/mutations/dependency_proxy/image_ttl_group_policy/update_spec.rb'
- './spec/requests/api/graphql/mutations/design_management/delete_spec.rb'
- './spec/requests/api/graphql/mutations/design_management/move_spec.rb'
- './spec/requests/api/graphql/mutations/design_management/upload_spec.rb'
- './spec/requests/api/graphql/mutations/discussions/toggle_resolve_spec.rb'
- './spec/requests/api/graphql/mutations/environments/canary_ingress/update_spec.rb'
- './spec/requests/api/graphql/mutations/groups/update_spec.rb'
- './spec/requests/api/graphql/mutations/issues/create_spec.rb'
- './spec/requests/api/graphql/mutations/issues/move_spec.rb'
- './spec/requests/api/graphql/mutations/issues/set_confidential_spec.rb'
- './spec/requests/api/graphql/mutations/issues/set_crm_contacts_spec.rb'
- './spec/requests/api/graphql/mutations/issues/set_due_date_spec.rb'
- './spec/requests/api/graphql/mutations/issues/set_escalation_status_spec.rb'
- './spec/requests/api/graphql/mutations/issues/set_locked_spec.rb'
- './spec/requests/api/graphql/mutations/issues/set_severity_spec.rb'
- './spec/requests/api/graphql/mutations/issues/set_subscription_spec.rb'
- './spec/requests/api/graphql/mutations/issues/update_spec.rb'
- './spec/requests/api/graphql/mutations/jira_import/import_users_spec.rb'
- './spec/requests/api/graphql/mutations/jira_import/start_spec.rb'
- './spec/requests/api/graphql/mutations/labels/create_spec.rb'
- './spec/requests/api/graphql/mutations/merge_requests/accept_spec.rb'
- './spec/requests/api/graphql/mutations/merge_requests/create_spec.rb'
- './spec/requests/api/graphql/mutations/merge_requests/reviewer_rereview_spec.rb'
- './spec/requests/api/graphql/mutations/merge_requests/set_assignees_spec.rb'
- './spec/requests/api/graphql/mutations/merge_requests/set_draft_spec.rb'
- './spec/requests/api/graphql/mutations/merge_requests/set_labels_spec.rb'
- './spec/requests/api/graphql/mutations/merge_requests/set_locked_spec.rb'
- './spec/requests/api/graphql/mutations/merge_requests/set_milestone_spec.rb'
- './spec/requests/api/graphql/mutations/merge_requests/set_reviewers_spec.rb'
- './spec/requests/api/graphql/mutations/merge_requests/set_subscription_spec.rb'
- './spec/requests/api/graphql/mutations/metrics/dashboard/annotations/create_spec.rb'
- './spec/requests/api/graphql/mutations/metrics/dashboard/annotations/delete_spec.rb'
- './spec/requests/api/graphql/mutations/namespace/package_settings/update_spec.rb'
- './spec/requests/api/graphql/mutations/notes/create/diff_note_spec.rb'
- './spec/requests/api/graphql/mutations/notes/create/image_diff_note_spec.rb'
- './spec/requests/api/graphql/mutations/notes/create/note_spec.rb'
- './spec/requests/api/graphql/mutations/notes/destroy_spec.rb'
- './spec/requests/api/graphql/mutations/notes/reposition_image_diff_note_spec.rb'
- './spec/requests/api/graphql/mutations/notes/update/image_diff_note_spec.rb'
- './spec/requests/api/graphql/mutations/notes/update/note_spec.rb'
- './spec/requests/api/graphql/mutations/packages/cleanup/policy/update_spec.rb'
- './spec/requests/api/graphql/mutations/packages/destroy_file_spec.rb'
- './spec/requests/api/graphql/mutations/packages/destroy_files_spec.rb'
- './spec/requests/api/graphql/mutations/packages/destroy_spec.rb'
- './spec/requests/api/graphql/mutations/release_asset_links/create_spec.rb'
- './spec/requests/api/graphql/mutations/release_asset_links/delete_spec.rb'
- './spec/requests/api/graphql/mutations/release_asset_links/update_spec.rb'
- './spec/requests/api/graphql/mutations/releases/create_spec.rb'
- './spec/requests/api/graphql/mutations/releases/delete_spec.rb'
- './spec/requests/api/graphql/mutations/releases/update_spec.rb'
- './spec/requests/api/graphql/mutations/security/ci_configuration/configure_sast_iac_spec.rb'
- './spec/requests/api/graphql/mutations/security/ci_configuration/configure_secret_detection_spec.rb'
- './spec/requests/api/graphql/mutations/snippets/create_spec.rb'
- './spec/requests/api/graphql/mutations/snippets/destroy_spec.rb'
- './spec/requests/api/graphql/mutations/snippets/mark_as_spam_spec.rb'
- './spec/requests/api/graphql/mutations/snippets/update_spec.rb'
- './spec/requests/api/graphql/mutations/timelogs/create_spec.rb'
- './spec/requests/api/graphql/mutations/timelogs/delete_spec.rb'
- './spec/requests/api/graphql/mutations/todos/create_spec.rb'
- './spec/requests/api/graphql/mutations/todos/mark_all_done_spec.rb'
- './spec/requests/api/graphql/mutations/todos/mark_done_spec.rb'
- './spec/requests/api/graphql/mutations/todos/restore_many_spec.rb'
- './spec/requests/api/graphql/mutations/todos/restore_spec.rb'
- './spec/requests/api/graphql/mutations/uploads/delete_spec.rb'
- './spec/requests/api/graphql/mutations/user_callouts/create_spec.rb'
- './spec/requests/api/graphql/mutations/user_preferences/update_spec.rb'
- './spec/requests/api/graphql/mutations/work_items/create_from_task_spec.rb'
- './spec/requests/api/graphql/mutations/work_items/create_spec.rb'
- './spec/requests/api/graphql/mutations/work_items/delete_spec.rb'
- './spec/requests/api/graphql/mutations/work_items/update_spec.rb'
- './spec/requests/api/graphql/namespace/package_settings_spec.rb'
- './spec/requests/api/graphql/namespace/projects_spec.rb'
- './spec/requests/api/graphql/namespace_query_spec.rb'
- './spec/requests/api/graphql/namespace/root_storage_statistics_spec.rb'
- './spec/requests/api/graphql/packages/composer_spec.rb'
- './spec/requests/api/graphql/packages/conan_spec.rb'
- './spec/requests/api/graphql/packages/helm_spec.rb'
- './spec/requests/api/graphql/packages/maven_spec.rb'
- './spec/requests/api/graphql/packages/nuget_spec.rb'
- './spec/requests/api/graphql/packages/package_spec.rb'
- './spec/requests/api/graphql/packages/pypi_spec.rb'
- './spec/requests/api/graphql/project/alert_management/alert/assignees_spec.rb'
- './spec/requests/api/graphql/project/alert_management/alert/issue_spec.rb'
- './spec/requests/api/graphql/project/alert_management/alert/notes_spec.rb'
- './spec/requests/api/graphql/project/alert_management/alerts_spec.rb'
- './spec/requests/api/graphql/project/alert_management/alert_status_counts_spec.rb'
- './spec/requests/api/graphql/project/alert_management/alert/todos_spec.rb'
- './spec/requests/api/graphql/project/alert_management/integrations_spec.rb'
- './spec/requests/api/graphql/project/base_service_spec.rb'
- './spec/requests/api/graphql/project/cluster_agents_spec.rb'
- './spec/requests/api/graphql/project/container_expiration_policy_spec.rb'
- './spec/requests/api/graphql/project/container_repositories_spec.rb'
- './spec/requests/api/graphql/project/error_tracking/sentry_detailed_error_request_spec.rb'
- './spec/requests/api/graphql/project/error_tracking/sentry_errors_request_spec.rb'
- './spec/requests/api/graphql/project/fork_targets_spec.rb'
- './spec/requests/api/graphql/project/grafana_integration_spec.rb'
- './spec/requests/api/graphql/project/issue/design_collection/version_spec.rb'
- './spec/requests/api/graphql/project/issue/design_collection/versions_spec.rb'
- './spec/requests/api/graphql/project/issue/designs/designs_spec.rb'
- './spec/requests/api/graphql/project/issue/designs/notes_spec.rb'
- './spec/requests/api/graphql/project/issue/notes_spec.rb'
- './spec/requests/api/graphql/project/issue_spec.rb'
- './spec/requests/api/graphql/project/issues_spec.rb'
- './spec/requests/api/graphql/project/jira_import_spec.rb'
- './spec/requests/api/graphql/project/jira_projects_spec.rb'
- './spec/requests/api/graphql/project/jira_service_spec.rb'
- './spec/requests/api/graphql/project/jobs_spec.rb'
- './spec/requests/api/graphql/project/labels_query_spec.rb'
- './spec/requests/api/graphql/project/merge_request/diff_notes_spec.rb'
- './spec/requests/api/graphql/project/merge_request/pipelines_spec.rb'
- './spec/requests/api/graphql/project/merge_request_spec.rb'
- './spec/requests/api/graphql/project/merge_requests_spec.rb'
- './spec/requests/api/graphql/project/milestones_spec.rb'
- './spec/requests/api/graphql/project/packages_cleanup_policy_spec.rb'
- './spec/requests/api/graphql/project/packages_spec.rb'
- './spec/requests/api/graphql/project/pipeline_spec.rb'
- './spec/requests/api/graphql/project/project_members_spec.rb'
- './spec/requests/api/graphql/project/project_pipeline_statistics_spec.rb'
- './spec/requests/api/graphql/project/project_statistics_spec.rb'
- './spec/requests/api/graphql/project_query_spec.rb'
- './spec/requests/api/graphql/project/recent_issue_boards_query_spec.rb'
- './spec/requests/api/graphql/project/release_spec.rb'
- './spec/requests/api/graphql/project/releases_spec.rb'
- './spec/requests/api/graphql/project/repository/blobs_spec.rb'
- './spec/requests/api/graphql/project/repository_spec.rb'
- './spec/requests/api/graphql/project/terraform/state_spec.rb'
- './spec/requests/api/graphql/project/terraform/states_spec.rb'
- './spec/requests/api/graphql/project/tree/tree_spec.rb'
- './spec/requests/api/graphql/project/work_items_spec.rb'
- './spec/requests/api/graphql/project/work_item_types_spec.rb'
- './spec/requests/api/graphql/query_spec.rb'
- './spec/requests/api/graphql/read_only_spec.rb'
- './spec/requests/api/graphql/snippets_spec.rb'
- './spec/requests/api/graphql_spec.rb'
- './spec/requests/api/graphql/tasks/task_completion_status_spec.rb'
- './spec/requests/api/graphql/terraform/state/delete_spec.rb'
- './spec/requests/api/graphql/terraform/state/lock_spec.rb'
- './spec/requests/api/graphql/terraform/state/unlock_spec.rb'
- './spec/requests/api/graphql/todo_query_spec.rb'
- './spec/requests/api/graphql/usage_trends_measurements_spec.rb'
- './spec/requests/api/graphql/user/group_member_query_spec.rb'
- './spec/requests/api/graphql/user/project_member_query_spec.rb'
- './spec/requests/api/graphql/user_query_spec.rb'
- './spec/requests/api/graphql/user_spec.rb'
- './spec/requests/api/graphql/users_spec.rb'
- './spec/requests/api/graphql/user/starred_projects_query_spec.rb'
- './spec/requests/api/graphql/work_item_spec.rb'
- './spec/requests/api/group_avatar_spec.rb'
- './spec/requests/api/group_boards_spec.rb'
- './spec/requests/api/group_clusters_spec.rb'
- './spec/requests/api/group_container_repositories_spec.rb'
- './spec/requests/api/group_debian_distributions_spec.rb'
- './spec/requests/api/group_export_spec.rb'
- './spec/requests/api/group_import_spec.rb'
- './spec/requests/api/group_labels_spec.rb'
- './spec/requests/api/group_milestones_spec.rb'
- './spec/requests/api/group_packages_spec.rb'
- './spec/requests/api/groups_spec.rb'
- './spec/requests/api/group_variables_spec.rb'
- './spec/requests/api/helm_packages_spec.rb'
- './spec/requests/api/helpers_spec.rb'
- './spec/requests/api/import_bitbucket_server_spec.rb'
- './spec/requests/api/import_github_spec.rb'
- './spec/requests/api/integrations/jira_connect/subscriptions_spec.rb'
- './spec/requests/api/integrations_spec.rb'
- './spec/requests/api/internal/base_spec.rb'
- './spec/requests/api/internal/error_tracking_spec.rb'
- './spec/requests/api/internal/kubernetes_spec.rb'
- './spec/requests/api/internal/lfs_spec.rb'
- './spec/requests/api/internal/mail_room_spec.rb'
- './spec/requests/api/internal/pages_spec.rb'
- './spec/requests/api/internal/workhorse_spec.rb'
- './spec/requests/api/invitations_spec.rb'
- './spec/requests/api/issue_links_spec.rb'
- './spec/requests/api/issues/get_group_issues_spec.rb'
- './spec/requests/api/issues/get_project_issues_spec.rb'
- './spec/requests/api/issues/issues_spec.rb'
- './spec/requests/api/issues/post_projects_issues_spec.rb'
- './spec/requests/api/issues/put_projects_issues_spec.rb'
- './spec/requests/api/keys_spec.rb'
- './spec/requests/api/labels_spec.rb'
- './spec/requests/api/lint_spec.rb'
- './spec/requests/api/markdown_spec.rb'
- './spec/requests/api/maven_packages_spec.rb'
- './spec/requests/api/members_spec.rb'
- './spec/requests/api/merge_request_approvals_spec.rb'
- './spec/requests/api/merge_request_diffs_spec.rb'
- './spec/requests/api/merge_requests_spec.rb'
- './spec/requests/api/metadata_spec.rb'
- './spec/requests/api/namespaces_spec.rb'
- './spec/requests/api/notes_spec.rb'
- './spec/requests/api/notification_settings_spec.rb'
- './spec/requests/api/npm_instance_packages_spec.rb'
- './spec/requests/api/npm_project_packages_spec.rb'
- './spec/requests/api/nuget_group_packages_spec.rb'
- './spec/requests/api/nuget_project_packages_spec.rb'
- './spec/requests/api/oauth_tokens_spec.rb'
- './spec/requests/api/package_files_spec.rb'
- './spec/requests/api/pages_domains_spec.rb'
- './spec/requests/api/pages/internal_access_spec.rb'
- './spec/requests/api/pages/pages_spec.rb'
- './spec/requests/api/pages/private_access_spec.rb'
- './spec/requests/api/pages/public_access_spec.rb'
- './spec/requests/api/performance_bar_spec.rb'
- './spec/requests/api/personal_access_tokens_spec.rb'
- './spec/requests/api/project_clusters_spec.rb'
- './spec/requests/api/project_container_repositories_spec.rb'
- './spec/requests/api/project_debian_distributions_spec.rb'
- './spec/requests/api/project_events_spec.rb'
- './spec/requests/api/project_export_spec.rb'
- './spec/requests/api/project_hooks_spec.rb'
- './spec/requests/api/project_import_spec.rb'
- './spec/requests/api/project_milestones_spec.rb'
- './spec/requests/api/project_packages_spec.rb'
- './spec/requests/api/project_repository_storage_moves_spec.rb'
- './spec/requests/api/project_snapshots_spec.rb'
- './spec/requests/api/project_snippets_spec.rb'
- './spec/requests/api/projects_spec.rb'
- './spec/requests/api/project_statistics_spec.rb'
- './spec/requests/api/project_templates_spec.rb'
- './spec/requests/api/protected_branches_spec.rb'
- './spec/requests/api/protected_tags_spec.rb'
- './spec/requests/api/pypi_packages_spec.rb'
- './spec/requests/api/release/links_spec.rb'
- './spec/requests/api/releases_spec.rb'
- './spec/requests/api/remote_mirrors_spec.rb'
- './spec/requests/api/repositories_spec.rb'
- './spec/requests/api/resource_access_tokens_spec.rb'
- './spec/requests/api/resource_label_events_spec.rb'
- './spec/requests/api/resource_milestone_events_spec.rb'
- './spec/requests/api/resource_state_events_spec.rb'
- './spec/requests/api/rubygem_packages_spec.rb'
- './spec/requests/api/search_spec.rb'
- './spec/requests/api/settings_spec.rb'
- './spec/requests/api/sidekiq_metrics_spec.rb'
- './spec/requests/api/snippet_repository_storage_moves_spec.rb'
- './spec/requests/api/snippets_spec.rb'
- './spec/requests/api/statistics_spec.rb'
- './spec/requests/api/submodules_spec.rb'
- './spec/requests/api/suggestions_spec.rb'
- './spec/requests/api/system_hooks_spec.rb'
- './spec/requests/api/tags_spec.rb'
- './spec/requests/api/task_completion_status_spec.rb'
- './spec/requests/api/templates_spec.rb'
- './spec/requests/api/terraform/state_spec.rb'
- './spec/requests/api/terraform/state_version_spec.rb'
- './spec/requests/api/todos_spec.rb'
- './spec/requests/api/topics_spec.rb'
- './spec/requests/api/unleash_spec.rb'
- './spec/requests/api/usage_data_non_sql_metrics_spec.rb'
- './spec/requests/api/usage_data_queries_spec.rb'
- './spec/requests/api/usage_data_spec.rb'
- './spec/requests/api/user_counts_spec.rb'
- './spec/requests/api/users_preferences_spec.rb'
- './spec/requests/api/users_spec.rb'
- './spec/requests/api/wikis_spec.rb'
- './spec/requests/dashboard_controller_spec.rb'
- './spec/requests/dashboard/projects_controller_spec.rb'
- './spec/requests/git_http_spec.rb'
- './spec/requests/groups/autocomplete_sources_spec.rb'
- './spec/requests/groups_controller_spec.rb'
- './spec/requests/groups/crm/contacts_controller_spec.rb'
- './spec/requests/groups/crm/organizations_controller_spec.rb'
- './spec/requests/groups/deploy_tokens_controller_spec.rb'
- './spec/requests/groups/harbor/artifacts_controller_spec.rb'
- './spec/requests/groups/harbor/repositories_controller_spec.rb'
- './spec/requests/groups/harbor/tags_controller_spec.rb'
- './spec/requests/groups/milestones_controller_spec.rb'
- './spec/requests/groups/registry/repositories_controller_spec.rb'
- './spec/requests/groups/settings/access_tokens_controller_spec.rb'
- './spec/requests/groups/settings/applications_controller_spec.rb'
- './spec/requests/health_controller_spec.rb'
- './spec/requests/import/gitlab_groups_controller_spec.rb'
- './spec/requests/import/gitlab_projects_controller_spec.rb'
- './spec/requests/import/url_controller_spec.rb'
- './spec/requests/jira_connect/installations_controller_spec.rb'
- './spec/requests/jira_connect/oauth_application_ids_controller_spec.rb'
- './spec/requests/jira_connect/oauth_callbacks_controller_spec.rb'
- './spec/requests/jira_connect/subscriptions_controller_spec.rb'
- './spec/requests/jira_routing_spec.rb'
- './spec/requests/jwks_controller_spec.rb'
- './spec/requests/jwt_controller_spec.rb'
- './spec/requests/lfs_http_spec.rb'
- './spec/requests/lfs_locks_api_spec.rb'
- './spec/requests/mailgun/webhooks_controller_spec.rb'
- './spec/requests/oauth/applications_controller_spec.rb'
- './spec/requests/oauth/authorizations_controller_spec.rb'
- './spec/requests/oauth/tokens_controller_spec.rb'
- './spec/requests/oauth_tokens_spec.rb'
- './spec/requests/openid_connect_spec.rb'
- './spec/requests/profiles/notifications_controller_spec.rb'
- './spec/requests/projects/ci/promeheus_metrics/histograms_controller_spec.rb'
- './spec/requests/projects/cluster_agents_controller_spec.rb'
- './spec/requests/projects/commits_controller_spec.rb'
- './spec/requests/projects_controller_spec.rb'
- './spec/requests/projects/cycle_analytics_events_spec.rb'
- './spec/requests/projects/environments_controller_spec.rb'
- './spec/requests/projects/google_cloud/configuration_controller_spec.rb'
- './spec/requests/projects/google_cloud/databases_controller_spec.rb'
- './spec/requests/projects/google_cloud/deployments_controller_spec.rb'
- './spec/requests/projects/google_cloud/gcp_regions_controller_spec.rb'
- './spec/requests/projects/google_cloud/revoke_oauth_controller_spec.rb'
- './spec/requests/projects/google_cloud/service_accounts_controller_spec.rb'
- './spec/requests/projects/harbor/artifacts_controller_spec.rb'
- './spec/requests/projects/harbor/repositories_controller_spec.rb'
- './spec/requests/projects/harbor/tags_controller_spec.rb'
- './spec/requests/projects/issue_links_controller_spec.rb'
- './spec/requests/projects/issues_controller_spec.rb'
- './spec/requests/projects/issues/discussions_spec.rb'
- './spec/requests/projects/merge_requests/content_spec.rb'
- './spec/requests/projects/merge_requests/context_commit_diffs_spec.rb'
- './spec/requests/projects/merge_requests_controller_spec.rb'
- './spec/requests/projects/merge_requests/creations_spec.rb'
- './spec/requests/projects/merge_requests/diffs_spec.rb'
- './spec/requests/projects/merge_requests_discussions_spec.rb'
- './spec/requests/projects/merge_requests_spec.rb'
- './spec/requests/projects/pipelines_controller_spec.rb'
- './spec/requests/projects/redirect_controller_spec.rb'
- './spec/requests/projects/releases_controller_spec.rb'
- './spec/requests/projects/settings/access_tokens_controller_spec.rb'
- './spec/requests/projects/settings/packages_and_registries_controller_spec.rb'
- './spec/requests/projects/tags_controller_spec.rb'
- './spec/requests/projects/uploads_spec.rb'
- './spec/requests/projects/usage_quotas_spec.rb'
- './spec/requests/projects/work_items_spec.rb'
- './spec/requests/pwa_controller_spec.rb'
- './spec/requests/rack_attack_global_spec.rb'
- './spec/requests/recursive_webhook_detection_spec.rb'
- './spec/requests/robots_txt_spec.rb'
- './spec/requests/runner_setup_controller_spec.rb'
- './spec/requests/sandbox_controller_spec.rb'
- './spec/requests/search_controller_spec.rb'
- './spec/requests/sessions_spec.rb'
- './spec/requests/terraform/services_controller_spec.rb'
- './spec/requests/user_activity_spec.rb'
- './spec/requests/user_avatar_spec.rb'
- './spec/requests/users_controller_spec.rb'
- './spec/requests/user_sends_malformed_strings_spec.rb'
- './spec/requests/users/group_callouts_spec.rb'
- './spec/requests/user_spoofs_ip_spec.rb'
- './spec/requests/users/project_callouts_spec.rb'
- './spec/requests/verifies_with_email_spec.rb'
- './spec/requests/whats_new_controller_spec.rb'
- './spec/serializers/accessibility_error_entity_spec.rb'
- './spec/serializers/accessibility_reports_comparer_entity_spec.rb'
- './spec/serializers/accessibility_reports_comparer_serializer_spec.rb'
- './spec/serializers/admin/user_entity_spec.rb'
- './spec/serializers/admin/user_serializer_spec.rb'
- './spec/serializers/analytics_build_entity_spec.rb'
- './spec/serializers/analytics_build_serializer_spec.rb'
- './spec/serializers/analytics/cycle_analytics/stage_entity_spec.rb'
- './spec/serializers/analytics_issue_entity_spec.rb'
- './spec/serializers/analytics_issue_serializer_spec.rb'
- './spec/serializers/analytics_merge_request_serializer_spec.rb'
- './spec/serializers/analytics_summary_serializer_spec.rb'
- './spec/serializers/base_discussion_entity_spec.rb'
- './spec/serializers/blob_entity_spec.rb'
- './spec/serializers/build_action_entity_spec.rb'
- './spec/serializers/build_artifact_entity_spec.rb'
- './spec/serializers/build_details_entity_spec.rb'
- './spec/serializers/build_trace_entity_spec.rb'
- './spec/serializers/ci/codequality_mr_diff_entity_spec.rb'
- './spec/serializers/ci/codequality_mr_diff_report_serializer_spec.rb'
- './spec/serializers/ci/daily_build_group_report_result_entity_spec.rb'
- './spec/serializers/ci/daily_build_group_report_result_serializer_spec.rb'
- './spec/serializers/ci/downloadable_artifact_entity_spec.rb'
- './spec/serializers/ci/downloadable_artifact_serializer_spec.rb'
- './spec/serializers/ci/group_variable_entity_spec.rb'
- './spec/serializers/ci/job_entity_spec.rb'
- './spec/serializers/ci/job_serializer_spec.rb'
- './spec/serializers/ci/lint/job_entity_spec.rb'
- './spec/serializers/ci/lint/result_entity_spec.rb'
- './spec/serializers/ci/lint/result_serializer_spec.rb'
- './spec/serializers/ci/pipeline_entity_spec.rb'
- './spec/serializers/ci/trigger_entity_spec.rb'
- './spec/serializers/ci/trigger_serializer_spec.rb'
- './spec/serializers/ci/variable_entity_spec.rb'
- './spec/serializers/cluster_entity_spec.rb'
- './spec/serializers/cluster_serializer_spec.rb'
- './spec/serializers/clusters/kubernetes_error_entity_spec.rb'
- './spec/serializers/codequality_degradation_entity_spec.rb'
- './spec/serializers/codequality_reports_comparer_entity_spec.rb'
- './spec/serializers/codequality_reports_comparer_serializer_spec.rb'
- './spec/serializers/commit_entity_spec.rb'
- './spec/serializers/container_repositories_serializer_spec.rb'
- './spec/serializers/container_repository_entity_spec.rb'
- './spec/serializers/container_tag_entity_spec.rb'
- './spec/serializers/context_commits_diff_entity_spec.rb'
- './spec/serializers/deploy_keys/basic_deploy_key_entity_spec.rb'
- './spec/serializers/deploy_keys/deploy_key_entity_spec.rb'
- './spec/serializers/deployment_cluster_entity_spec.rb'
- './spec/serializers/deployment_serializer_spec.rb'
- './spec/serializers/detailed_status_entity_spec.rb'
- './spec/serializers/diff_file_base_entity_spec.rb'
- './spec/serializers/diff_file_entity_spec.rb'
- './spec/serializers/diff_file_metadata_entity_spec.rb'
- './spec/serializers/diff_line_entity_spec.rb'
- './spec/serializers/diff_line_serializer_spec.rb'
- './spec/serializers/diffs_entity_spec.rb'
- './spec/serializers/diffs_metadata_entity_spec.rb'
- './spec/serializers/diff_viewer_entity_spec.rb'
- './spec/serializers/discussion_diff_file_entity_spec.rb'
- './spec/serializers/discussion_entity_spec.rb'
- './spec/serializers/entity_date_helper_spec.rb'
- './spec/serializers/entity_request_spec.rb'
- './spec/serializers/environment_entity_spec.rb'
- './spec/serializers/environment_serializer_spec.rb'
- './spec/serializers/evidences/evidence_entity_spec.rb'
- './spec/serializers/evidences/evidence_serializer_spec.rb'
- './spec/serializers/evidences/issue_entity_spec.rb'
- './spec/serializers/evidences/milestone_entity_spec.rb'
- './spec/serializers/evidences/project_entity_spec.rb'
- './spec/serializers/evidences/release_entity_spec.rb'
- './spec/serializers/evidences/release_serializer_spec.rb'
- './spec/serializers/feature_flag_entity_spec.rb'
- './spec/serializers/feature_flags_client_serializer_spec.rb'
- './spec/serializers/feature_flag_serializer_spec.rb'
- './spec/serializers/feature_flag_summary_entity_spec.rb'
- './spec/serializers/feature_flag_summary_serializer_spec.rb'
- './spec/serializers/fork_namespace_entity_spec.rb'
- './spec/serializers/fork_namespace_serializer_spec.rb'
- './spec/serializers/group_access_token_entity_spec.rb'
- './spec/serializers/group_access_token_serializer_spec.rb'
- './spec/serializers/group_child_entity_spec.rb'
- './spec/serializers/group_child_serializer_spec.rb'
- './spec/serializers/group_deploy_key_entity_spec.rb'
- './spec/serializers/group_link/group_group_link_entity_spec.rb'
- './spec/serializers/group_link/group_group_link_serializer_spec.rb'
- './spec/serializers/group_link/group_link_entity_spec.rb'
- './spec/serializers/group_link/project_group_link_entity_spec.rb'
- './spec/serializers/group_link/project_group_link_serializer_spec.rb'
- './spec/serializers/import/bitbucket_provider_repo_entity_spec.rb'
- './spec/serializers/import/bitbucket_server_provider_repo_entity_spec.rb'
- './spec/serializers/import/bulk_import_entity_spec.rb'
- './spec/serializers/import/fogbugz_provider_repo_entity_spec.rb'
- './spec/serializers/import/githubish_provider_repo_entity_spec.rb'
- './spec/serializers/import/gitlab_provider_repo_entity_spec.rb'
- './spec/serializers/import/manifest_provider_repo_entity_spec.rb'
- './spec/serializers/import/provider_repo_serializer_spec.rb'
- './spec/serializers/integrations/event_entity_spec.rb'
- './spec/serializers/integrations/field_entity_spec.rb'
- './spec/serializers/integrations/harbor_serializers/artifact_entity_spec.rb'
- './spec/serializers/integrations/harbor_serializers/artifact_serializer_spec.rb'
- './spec/serializers/integrations/harbor_serializers/repository_entity_spec.rb'
- './spec/serializers/integrations/harbor_serializers/repository_serializer_spec.rb'
- './spec/serializers/integrations/harbor_serializers/tag_entity_spec.rb'
- './spec/serializers/integrations/harbor_serializers/tag_serializer_spec.rb'
- './spec/serializers/integrations/project_entity_spec.rb'
- './spec/serializers/integrations/project_serializer_spec.rb'
- './spec/serializers/issuable_sidebar_extras_entity_spec.rb'
- './spec/serializers/issue_board_entity_spec.rb'
- './spec/serializers/issue_entity_spec.rb'
- './spec/serializers/issue_serializer_spec.rb'
- './spec/serializers/issue_sidebar_basic_entity_spec.rb'
- './spec/serializers/jira_connect/app_data_serializer_spec.rb'
- './spec/serializers/jira_connect/group_entity_spec.rb'
- './spec/serializers/jira_connect/subscription_entity_spec.rb'
- './spec/serializers/job_artifact_report_entity_spec.rb'
- './spec/serializers/label_serializer_spec.rb'
- './spec/serializers/lfs_file_lock_entity_spec.rb'
- './spec/serializers/linked_project_issue_entity_spec.rb'
- './spec/serializers/member_entity_spec.rb'
- './spec/serializers/member_serializer_spec.rb'
- './spec/serializers/member_user_entity_spec.rb'
- './spec/serializers/merge_request_basic_entity_spec.rb'
- './spec/serializers/merge_request_current_user_entity_spec.rb'
- './spec/serializers/merge_request_diff_entity_spec.rb'
- './spec/serializers/merge_request_for_pipeline_entity_spec.rb'
- './spec/serializers/merge_request_metrics_helper_spec.rb'
- './spec/serializers/merge_request_poll_cached_widget_entity_spec.rb'
- './spec/serializers/merge_request_poll_widget_entity_spec.rb'
- './spec/serializers/merge_request_serializer_spec.rb'
- './spec/serializers/merge_request_sidebar_extras_entity_spec.rb'
- './spec/serializers/merge_requests/pipeline_entity_spec.rb'
- './spec/serializers/merge_request_user_entity_spec.rb'
- './spec/serializers/merge_request_widget_commit_entity_spec.rb'
- './spec/serializers/merge_request_widget_entity_spec.rb'
- './spec/serializers/move_to_project_entity_spec.rb'
- './spec/serializers/move_to_project_serializer_spec.rb'
- './spec/serializers/namespace_basic_entity_spec.rb'
- './spec/serializers/namespace_serializer_spec.rb'
- './spec/serializers/note_entity_spec.rb'
- './spec/serializers/paginated_diff_entity_spec.rb'
- './spec/serializers/pipeline_details_entity_spec.rb'
- './spec/serializers/project_access_token_entity_spec.rb'
- './spec/serializers/project_access_token_serializer_spec.rb'
- './spec/serializers/project_import_entity_spec.rb'
- './spec/serializers/project_mirror_entity_spec.rb'
- './spec/serializers/project_mirror_serializer_spec.rb'
- './spec/serializers/project_note_entity_spec.rb'
- './spec/serializers/project_serializer_spec.rb'
- './spec/serializers/release_serializer_spec.rb'
- './spec/serializers/remote_mirror_entity_spec.rb'
- './spec/serializers/request_aware_entity_spec.rb'
- './spec/serializers/review_app_setup_entity_spec.rb'
- './spec/serializers/rollout_status_entity_spec.rb'
- './spec/serializers/rollout_statuses/ingress_entity_spec.rb'
- './spec/serializers/runner_entity_spec.rb'
- './spec/serializers/serverless/domain_entity_spec.rb'
- './spec/serializers/stage_entity_spec.rb'
- './spec/serializers/stage_serializer_spec.rb'
- './spec/serializers/suggestion_entity_spec.rb'
- './spec/serializers/test_case_entity_spec.rb'
- './spec/serializers/test_report_entity_spec.rb'
- './spec/serializers/test_reports_comparer_entity_spec.rb'
- './spec/serializers/test_reports_comparer_serializer_spec.rb'
- './spec/serializers/test_report_summary_entity_spec.rb'
- './spec/serializers/test_suite_comparer_entity_spec.rb'
- './spec/serializers/test_suite_entity_spec.rb'
- './spec/serializers/test_suite_summary_entity_spec.rb'
- './spec/serializers/trigger_variable_entity_spec.rb'
- './spec/serializers/user_entity_spec.rb'
- './spec/serializers/user_serializer_spec.rb'
- './spec/serializers/web_ide_terminal_entity_spec.rb'
- './spec/serializers/web_ide_terminal_serializer_spec.rb'
- './spec/services/access_token_validation_service_spec.rb'
- './spec/services/alert_management/alerts/todo/create_service_spec.rb'
- './spec/services/alert_management/alerts/update_service_spec.rb'
- './spec/services/alert_management/create_alert_issue_service_spec.rb'
- './spec/services/alert_management/http_integrations/create_service_spec.rb'
- './spec/services/alert_management/http_integrations/destroy_service_spec.rb'
- './spec/services/alert_management/http_integrations/update_service_spec.rb'
- './spec/services/alert_management/metric_images/upload_service_spec.rb'
- './spec/services/alert_management/process_prometheus_alert_service_spec.rb'
- './spec/services/applications/create_service_spec.rb'
- './spec/services/application_settings/update_service_spec.rb'
- './spec/services/audit_events/build_service_spec.rb'
- './spec/services/audit_event_service_spec.rb'
- './spec/services/auth/container_registry_authentication_service_spec.rb'
- './spec/services/auth/dependency_proxy_authentication_service_spec.rb'
- './spec/services/auto_merge/base_service_spec.rb'
- './spec/services/auto_merge_service_spec.rb'
- './spec/services/base_container_service_spec.rb'
- './spec/services/base_count_service_spec.rb'
- './spec/services/bulk_push_event_payload_service_spec.rb'
- './spec/services/captcha/captcha_verification_service_spec.rb'
- './spec/services/ci/abort_pipelines_service_spec.rb'
- './spec/services/ci/append_build_trace_service_spec.rb'
- './spec/services/ci/archive_trace_service_spec.rb'
- './spec/services/ci/build_cancel_service_spec.rb'
- './spec/services/ci/build_report_result_service_spec.rb'
- './spec/services/ci/build_unschedule_service_spec.rb'
- './spec/services/ci/change_variable_service_spec.rb'
- './spec/services/ci/change_variables_service_spec.rb'
- './spec/services/ci/compare_accessibility_reports_service_spec.rb'
- './spec/services/ci/compare_codequality_reports_service_spec.rb'
- './spec/services/ci/compare_reports_base_service_spec.rb'
- './spec/services/ci/compare_test_reports_service_spec.rb'
- './spec/services/ci/copy_cross_database_associations_service_spec.rb'
- './spec/services/ci/create_downstream_pipeline_service_spec.rb'
- './spec/services/ci/create_pipeline_service/artifacts_spec.rb'
- './spec/services/ci/create_pipeline_service/cache_spec.rb'
- './spec/services/ci/create_pipeline_service/creation_errors_and_warnings_spec.rb'
- './spec/services/ci/create_pipeline_service/cross_project_pipeline_spec.rb'
- './spec/services/ci/create_pipeline_service/custom_config_content_spec.rb'
- './spec/services/ci/create_pipeline_service/custom_yaml_tags_spec.rb'
- './spec/services/ci/create_pipeline_service/dry_run_spec.rb'
- './spec/services/ci/create_pipeline_service/environment_spec.rb'
- './spec/services/ci/create_pipeline_service/evaluate_runner_tags_spec.rb'
- './spec/services/ci/create_pipeline_service/include_spec.rb'
- './spec/services/ci/create_pipeline_service/logger_spec.rb'
- './spec/services/ci/create_pipeline_service/merge_requests_spec.rb'
- './spec/services/ci/create_pipeline_service/needs_spec.rb'
- './spec/services/ci/create_pipeline_service/parallel_spec.rb'
- './spec/services/ci/create_pipeline_service/parameter_content_spec.rb'
- './spec/services/ci/create_pipeline_service/parent_child_pipeline_spec.rb'
- './spec/services/ci/create_pipeline_service/pre_post_stages_spec.rb'
- './spec/services/ci/create_pipeline_service/rate_limit_spec.rb'
- './spec/services/ci/create_pipeline_service/rules_spec.rb'
- './spec/services/ci/create_pipeline_service_spec.rb'
- './spec/services/ci/create_pipeline_service/tags_spec.rb'
- './spec/services/ci/create_web_ide_terminal_service_spec.rb'
- './spec/services/ci/daily_build_group_report_result_service_spec.rb'
- './spec/services/ci/delete_objects_service_spec.rb'
- './spec/services/ci/delete_unit_tests_service_spec.rb'
- './spec/services/ci/deployments/destroy_service_spec.rb'
- './spec/services/ci/destroy_pipeline_service_spec.rb'
- './spec/services/ci/destroy_secure_file_service_spec.rb'
- './spec/services/ci/drop_pipeline_service_spec.rb'
- './spec/services/ci/expire_pipeline_cache_service_spec.rb'
- './spec/services/ci/external_pull_requests/create_pipeline_service_spec.rb'
- './spec/services/ci/find_exposed_artifacts_service_spec.rb'
- './spec/services/ci/generate_codequality_mr_diff_report_service_spec.rb'
- './spec/services/ci/generate_coverage_reports_service_spec.rb'
- './spec/services/ci/generate_kubeconfig_service_spec.rb'
- './spec/services/ci/generate_terraform_reports_service_spec.rb'
- './spec/services/ci/job_artifacts/create_service_spec.rb'
- './spec/services/ci/job_artifacts/delete_project_artifacts_service_spec.rb'
- './spec/services/ci/job_artifacts/destroy_all_expired_service_spec.rb'
- './spec/services/ci/job_artifacts/destroy_associations_service_spec.rb'
- './spec/services/ci/job_artifacts/destroy_batch_service_spec.rb'
- './spec/services/ci/job_artifacts/expire_project_build_artifacts_service_spec.rb'
- './spec/services/ci/job_artifacts/update_unknown_locked_status_service_spec.rb'
- './spec/services/ci/job_token_scope/add_project_service_spec.rb'
- './spec/services/ci/job_token_scope/remove_project_service_spec.rb'
- './spec/services/ci/list_config_variables_service_spec.rb'
- './spec/services/ci/parse_dotenv_artifact_service_spec.rb'
- './spec/services/ci/pipeline_artifacts/coverage_report_service_spec.rb'
- './spec/services/ci/pipeline_artifacts/create_code_quality_mr_diff_report_service_spec.rb'
- './spec/services/ci/pipeline_artifacts/destroy_all_expired_service_spec.rb'
- './spec/services/ci/pipeline_bridge_status_service_spec.rb'
- './spec/services/ci/pipeline_creation/start_pipeline_service_spec.rb'
- './spec/services/ci/pipeline_processing/atomic_processing_service_spec.rb'
- './spec/services/ci/pipeline_processing/atomic_processing_service/status_collection_spec.rb'
- './spec/services/ci/pipelines/add_job_service_spec.rb'
- './spec/services/ci/pipeline_schedule_service_spec.rb'
- './spec/services/ci/pipelines/hook_service_spec.rb'
- './spec/services/ci/pipeline_trigger_service_spec.rb'
- './spec/services/ci/play_bridge_service_spec.rb'
- './spec/services/ci/play_build_service_spec.rb'
- './spec/services/ci/play_manual_stage_service_spec.rb'
- './spec/services/ci/prepare_build_service_spec.rb'
- './spec/services/ci/process_build_service_spec.rb'
- './spec/services/ci/process_pipeline_service_spec.rb'
- './spec/services/ci/process_sync_events_service_spec.rb'
- './spec/services/ci/prometheus_metrics/observe_histograms_service_spec.rb'
- './spec/services/ci/register_job_service_spec.rb'
- './spec/services/ci/resource_groups/assign_resource_from_resource_group_service_spec.rb'
- './spec/services/ci/retry_job_service_spec.rb'
- './spec/services/ci/retry_pipeline_service_spec.rb'
- './spec/services/ci/runners/assign_runner_service_spec.rb'
- './spec/services/ci/runners/bulk_delete_runners_service_spec.rb'
- './spec/services/ci/runners/process_runner_version_update_service_spec.rb'
- './spec/services/ci/runners/reconcile_existing_runner_versions_service_spec.rb'
- './spec/services/ci/runners/register_runner_service_spec.rb'
- './spec/services/ci/runners/reset_registration_token_service_spec.rb'
- './spec/services/ci/runners/unassign_runner_service_spec.rb'
- './spec/services/ci/runners/unregister_runner_service_spec.rb'
- './spec/services/ci/runners/update_runner_service_spec.rb'
- './spec/services/ci/run_scheduled_build_service_spec.rb'
- './spec/services/ci/stuck_builds/drop_pending_service_spec.rb'
- './spec/services/ci/stuck_builds/drop_running_service_spec.rb'
- './spec/services/ci/stuck_builds/drop_scheduled_service_spec.rb'
- './spec/services/ci/test_failure_history_service_spec.rb'
- './spec/services/ci/track_failed_build_service_spec.rb'
- './spec/services/ci/unlock_artifacts_service_spec.rb'
- './spec/services/ci/update_build_queue_service_spec.rb'
- './spec/services/ci/update_build_state_service_spec.rb'
- './spec/services/ci/update_instance_variables_service_spec.rb'
- './spec/services/ci/update_pending_build_service_spec.rb'
- './spec/services/cohorts_service_spec.rb'
- './spec/services/compare_service_spec.rb'
- './spec/services/container_expiration_policies/cleanup_service_spec.rb'
- './spec/services/container_expiration_policies/update_service_spec.rb'
- './spec/services/customer_relations/contacts/create_service_spec.rb'
- './spec/services/customer_relations/contacts/update_service_spec.rb'
- './spec/services/customer_relations/organizations/create_service_spec.rb'
- './spec/services/customer_relations/organizations/update_service_spec.rb'
- './spec/services/database/consistency_check_service_spec.rb'
- './spec/services/database/consistency_fix_service_spec.rb'
- './spec/services/dependency_proxy/auth_token_service_spec.rb'
- './spec/services/dependency_proxy/find_cached_manifest_service_spec.rb'
- './spec/services/dependency_proxy/group_settings/update_service_spec.rb'
- './spec/services/dependency_proxy/head_manifest_service_spec.rb'
- './spec/services/dependency_proxy/image_ttl_group_policies/update_service_spec.rb'
- './spec/services/dependency_proxy/request_token_service_spec.rb'
- './spec/services/deploy_keys/create_service_spec.rb'
- './spec/services/deployments/archive_in_project_service_spec.rb'
- './spec/services/deployments/create_service_spec.rb'
- './spec/services/deployments/link_merge_requests_service_spec.rb'
- './spec/services/deployments/update_environment_service_spec.rb'
- './spec/services/deployments/update_service_spec.rb'
- './spec/services/design_management/copy_design_collection/copy_service_spec.rb'
- './spec/services/design_management/delete_designs_service_spec.rb'
- './spec/services/design_management/design_user_notes_count_service_spec.rb'
- './spec/services/design_management/generate_image_versions_service_spec.rb'
- './spec/services/design_management/move_designs_service_spec.rb'
- './spec/services/design_management/save_designs_service_spec.rb'
- './spec/services/discussions/capture_diff_note_position_service_spec.rb'
- './spec/services/discussions/capture_diff_note_positions_service_spec.rb'
- './spec/services/discussions/resolve_service_spec.rb'
- './spec/services/discussions/unresolve_service_spec.rb'
- './spec/services/discussions/update_diff_position_service_spec.rb'
- './spec/services/draft_notes/create_service_spec.rb'
- './spec/services/draft_notes/destroy_service_spec.rb'
- './spec/services/draft_notes/publish_service_spec.rb'
- './spec/services/emails/confirm_service_spec.rb'
- './spec/services/emails/create_service_spec.rb'
- './spec/services/emails/destroy_service_spec.rb'
- './spec/services/environments/auto_stop_service_spec.rb'
- './spec/services/environments/canary_ingress/update_service_spec.rb'
- './spec/services/environments/reset_auto_stop_service_spec.rb'
- './spec/services/environments/schedule_to_delete_review_apps_service_spec.rb'
- './spec/services/environments/stop_service_spec.rb'
- './spec/services/error_tracking/base_service_spec.rb'
- './spec/services/error_tracking/list_projects_service_spec.rb'
- './spec/services/event_create_service_spec.rb'
- './spec/services/events/destroy_service_spec.rb'
- './spec/services/events/render_service_spec.rb'
- './spec/services/feature_flags/create_service_spec.rb'
- './spec/services/feature_flags/destroy_service_spec.rb'
- './spec/services/feature_flags/hook_service_spec.rb'
- './spec/services/feature_flags/update_service_spec.rb'
- './spec/services/files/create_service_spec.rb'
- './spec/services/files/delete_service_spec.rb'
- './spec/services/files/multi_service_spec.rb'
- './spec/services/files/update_service_spec.rb'
- './spec/services/gpg_keys/create_service_spec.rb'
- './spec/services/gpg_keys/destroy_service_spec.rb'
- './spec/services/gravatar_service_spec.rb'
- './spec/services/groups/autocomplete_service_spec.rb'
- './spec/services/groups/auto_devops_service_spec.rb'
- './spec/services/groups/create_service_spec.rb'
- './spec/services/groups/deploy_tokens/create_service_spec.rb'
- './spec/services/groups/deploy_tokens/destroy_service_spec.rb'
- './spec/services/groups/deploy_tokens/revoke_service_spec.rb'
- './spec/services/groups/destroy_service_spec.rb'
- './spec/services/groups/group_links/create_service_spec.rb'
- './spec/services/groups/group_links/destroy_service_spec.rb'
- './spec/services/groups/group_links/update_service_spec.rb'
- './spec/services/groups/import_export/export_service_spec.rb'
- './spec/services/groups/import_export/import_service_spec.rb'
- './spec/services/groups/merge_requests_count_service_spec.rb'
- './spec/services/groups/nested_create_service_spec.rb'
- './spec/services/groups/open_issues_count_service_spec.rb'
- './spec/services/groups/participants_service_spec.rb'
- './spec/services/groups/transfer_service_spec.rb'
- './spec/services/groups/update_service_spec.rb'
- './spec/services/groups/update_statistics_service_spec.rb'
- './spec/services/ide/base_config_service_spec.rb'
- './spec/services/ide/schemas_config_service_spec.rb'
- './spec/services/ide/terminal_config_service_spec.rb'
- './spec/services/integrations/propagate_service_spec.rb'
- './spec/services/integrations/test/project_service_spec.rb'
- './spec/services/issuable/bulk_update_service_spec.rb'
- './spec/services/issuable/common_system_notes_service_spec.rb'
- './spec/services/issuable/destroy_label_links_service_spec.rb'
- './spec/services/issuable/destroy_service_spec.rb'
- './spec/services/issuable/process_assignees_spec.rb'
- './spec/services/issue_links/create_service_spec.rb'
- './spec/services/issue_links/destroy_service_spec.rb'
- './spec/services/issue_links/list_service_spec.rb'
- './spec/services/jira_connect_installations/destroy_service_spec.rb'
- './spec/services/jira_connect_subscriptions/create_service_spec.rb'
- './spec/services/jira_connect/sync_service_spec.rb'
- './spec/services/jira_import/cloud_users_mapper_service_spec.rb'
- './spec/services/jira_import/server_users_mapper_service_spec.rb'
- './spec/services/jira_import/start_import_service_spec.rb'
- './spec/services/jira_import/users_importer_spec.rb'
- './spec/services/jira/requests/projects/list_service_spec.rb'
- './spec/services/keys/create_service_spec.rb'
- './spec/services/keys/destroy_service_spec.rb'
- './spec/services/keys/expiry_notification_service_spec.rb'
- './spec/services/keys/last_used_service_spec.rb'
- './spec/services/labels/available_labels_service_spec.rb'
- './spec/services/labels/create_service_spec.rb'
- './spec/services/labels/find_or_create_service_spec.rb'
- './spec/services/labels/promote_service_spec.rb'
- './spec/services/labels/transfer_service_spec.rb'
- './spec/services/labels/update_service_spec.rb'
- './spec/services/lfs/file_transformer_spec.rb'
- './spec/services/lfs/lock_file_service_spec.rb'
- './spec/services/lfs/locks_finder_service_spec.rb'
- './spec/services/lfs/push_service_spec.rb'
- './spec/services/lfs/unlock_file_service_spec.rb'
- './spec/services/loose_foreign_keys/batch_cleaner_service_spec.rb'
- './spec/services/loose_foreign_keys/cleaner_service_spec.rb'
- './spec/services/markdown_content_rewriter_service_spec.rb'
- './spec/services/members/approve_access_request_service_spec.rb'
- './spec/services/members/create_service_spec.rb'
- './spec/services/members/creator_service_spec.rb'
- './spec/services/members/destroy_service_spec.rb'
- './spec/services/members/groups/creator_service_spec.rb'
- './spec/services/members/import_project_team_service_spec.rb'
- './spec/services/members/invitation_reminder_email_service_spec.rb'
- './spec/services/members/invite_member_builder_spec.rb'
- './spec/services/members/invite_service_spec.rb'
- './spec/services/members/projects/creator_service_spec.rb'
- './spec/services/members/request_access_service_spec.rb'
- './spec/services/members/standard_member_builder_spec.rb'
- './spec/services/members/unassign_issuables_service_spec.rb'
- './spec/services/merge_requests/add_context_service_spec.rb'
- './spec/services/merge_requests/add_spent_time_service_spec.rb'
- './spec/services/merge_requests/add_todo_when_build_fails_service_spec.rb'
- './spec/services/merge_requests/after_create_service_spec.rb'
- './spec/services/merge_requests/approval_service_spec.rb'
- './spec/services/merge_requests/assign_issues_service_spec.rb'
- './spec/services/merge_requests/base_service_spec.rb'
- './spec/services/merge_requests/build_service_spec.rb'
- './spec/services/merge_requests/cleanup_refs_service_spec.rb'
- './spec/services/merge_requests/close_service_spec.rb'
- './spec/services/merge_requests/conflicts/list_service_spec.rb'
- './spec/services/merge_requests/conflicts/resolve_service_spec.rb'
- './spec/services/merge_requests/create_approval_event_service_spec.rb'
- './spec/services/merge_requests/create_from_issue_service_spec.rb'
- './spec/services/merge_requests/create_pipeline_service_spec.rb'
- './spec/services/merge_requests/create_service_spec.rb'
- './spec/services/merge_requests/delete_non_latest_diffs_service_spec.rb'
- './spec/services/merge_requests/execute_approval_hooks_service_spec.rb'
- './spec/services/merge_requests/export_csv_service_spec.rb'
- './spec/services/merge_requests/get_urls_service_spec.rb'
- './spec/services/merge_requests/handle_assignees_change_service_spec.rb'
- './spec/services/merge_requests/link_lfs_objects_service_spec.rb'
- './spec/services/merge_requests/mergeability/check_base_service_spec.rb'
- './spec/services/merge_requests/mergeability/check_ci_status_service_spec.rb'
- './spec/services/merge_requests/mergeability/check_discussions_status_service_spec.rb'
- './spec/services/merge_requests/mergeability/check_draft_status_service_spec.rb'
- './spec/services/merge_requests/mergeability/check_open_status_service_spec.rb'
- './spec/services/merge_requests/mergeability_check_service_spec.rb'
- './spec/services/merge_requests/mergeability/run_checks_service_spec.rb'
- './spec/services/merge_requests/merge_orchestration_service_spec.rb'
- './spec/services/merge_requests/merge_service_spec.rb'
- './spec/services/merge_requests/merge_to_ref_service_spec.rb'
- './spec/services/merge_requests/migrate_external_diffs_service_spec.rb'
- './spec/services/merge_requests/post_merge_service_spec.rb'
- './spec/services/merge_requests/pushed_branches_service_spec.rb'
- './spec/services/merge_requests/push_options_handler_service_spec.rb'
- './spec/services/merge_requests/rebase_service_spec.rb'
- './spec/services/merge_requests/refresh_service_spec.rb'
- './spec/services/merge_requests/reload_diffs_service_spec.rb'
- './spec/services/merge_requests/reload_merge_head_diff_service_spec.rb'
- './spec/services/merge_requests/remove_approval_service_spec.rb'
- './spec/services/merge_requests/reopen_service_spec.rb'
- './spec/services/merge_requests/request_review_service_spec.rb'
- './spec/services/merge_requests/resolved_discussion_notification_service_spec.rb'
- './spec/services/merge_requests/resolve_todos_service_spec.rb'
- './spec/services/merge_requests/retarget_chain_service_spec.rb'
- './spec/services/merge_requests/update_assignees_service_spec.rb'
- './spec/services/merge_requests/update_reviewers_service_spec.rb'
- './spec/services/merge_requests/update_service_spec.rb'
- './spec/services/milestones/closed_issues_count_service_spec.rb'
- './spec/services/milestones/close_service_spec.rb'
- './spec/services/milestones/create_service_spec.rb'
- './spec/services/milestones/destroy_service_spec.rb'
- './spec/services/milestones/find_or_create_service_spec.rb'
- './spec/services/milestones/issues_count_service_spec.rb'
- './spec/services/milestones/merge_requests_count_service_spec.rb'
- './spec/services/milestones/promote_service_spec.rb'
- './spec/services/milestones/transfer_service_spec.rb'
- './spec/services/milestones/update_service_spec.rb'
- './spec/services/namespace_settings/assign_attributes_service_spec.rb'
- './spec/services/namespaces/package_settings/update_service_spec.rb'
- './spec/services/namespaces/statistics_refresher_service_spec.rb'
- './spec/services/notes/build_service_spec.rb'
- './spec/services/notes/copy_service_spec.rb'
- './spec/services/notes/create_service_spec.rb'
- './spec/services/notes/destroy_service_spec.rb'
- './spec/services/notes/post_process_service_spec.rb'
- './spec/services/notes/quick_actions_service_spec.rb'
- './spec/services/notes/render_service_spec.rb'
- './spec/services/notes/resolve_service_spec.rb'
- './spec/services/note_summary_spec.rb'
- './spec/services/notes/update_service_spec.rb'
- './spec/services/notification_recipients/builder/default_spec.rb'
- './spec/services/notification_recipients/builder/new_note_spec.rb'
- './spec/services/notification_recipients/build_service_spec.rb'
- './spec/services/notification_service_spec.rb'
- './spec/services/packages/cleanup/update_policy_service_spec.rb'
- './spec/services/packages/composer/composer_json_service_spec.rb'
- './spec/services/packages/composer/create_package_service_spec.rb'
- './spec/services/packages/composer/version_parser_service_spec.rb'
- './spec/services/packages/conan/create_package_file_service_spec.rb'
- './spec/services/packages/conan/create_package_service_spec.rb'
- './spec/services/packages/conan/search_service_spec.rb'
- './spec/services/packages/create_dependency_service_spec.rb'
- './spec/services/packages/create_event_service_spec.rb'
- './spec/services/packages/create_package_file_service_spec.rb'
- './spec/services/packages/create_temporary_package_service_spec.rb'
- './spec/services/packages/debian/create_distribution_service_spec.rb'
- './spec/services/packages/debian/create_package_file_service_spec.rb'
- './spec/services/packages/debian/extract_changes_metadata_service_spec.rb'
- './spec/services/packages/debian/extract_deb_metadata_service_spec.rb'
- './spec/services/packages/debian/extract_metadata_service_spec.rb'
- './spec/services/packages/debian/find_or_create_incoming_service_spec.rb'
- './spec/services/packages/debian/generate_distribution_key_service_spec.rb'
- './spec/services/packages/debian/generate_distribution_service_spec.rb'
- './spec/services/packages/debian/parse_debian822_service_spec.rb'
- './spec/services/packages/debian/sign_distribution_service_spec.rb'
- './spec/services/packages/debian/update_distribution_service_spec.rb'
- './spec/services/packages/generic/create_package_file_service_spec.rb'
- './spec/services/packages/generic/find_or_create_package_service_spec.rb'
- './spec/services/packages/go/create_package_service_spec.rb'
- './spec/services/packages/go/sync_packages_service_spec.rb'
- './spec/services/packages/helm/extract_file_metadata_service_spec.rb'
- './spec/services/packages/helm/process_file_service_spec.rb'
- './spec/services/packages/mark_package_files_for_destruction_service_spec.rb'
- './spec/services/packages/mark_package_for_destruction_service_spec.rb'
- './spec/services/packages/maven/create_package_service_spec.rb'
- './spec/services/packages/maven/find_or_create_package_service_spec.rb'
- './spec/services/packages/maven/metadata/append_package_file_service_spec.rb'
- './spec/services/packages/maven/metadata/create_plugins_xml_service_spec.rb'
- './spec/services/packages/maven/metadata/create_versions_xml_service_spec.rb'
- './spec/services/packages/maven/metadata/sync_service_spec.rb'
- './spec/services/packages/npm/create_package_service_spec.rb'
- './spec/services/packages/npm/create_tag_service_spec.rb'
- './spec/services/packages/nuget/create_dependency_service_spec.rb'
- './spec/services/packages/nuget/metadata_extraction_service_spec.rb'
- './spec/services/packages/nuget/sync_metadatum_service_spec.rb'
- './spec/services/packages/nuget/update_package_from_metadata_service_spec.rb'
- './spec/services/packages/pypi/create_package_service_spec.rb'
- './spec/services/packages/remove_tag_service_spec.rb'
- './spec/services/packages/rubygems/create_dependencies_service_spec.rb'
- './spec/services/packages/rubygems/create_gemspec_service_spec.rb'
- './spec/services/packages/rubygems/dependency_resolver_service_spec.rb'
- './spec/services/packages/rubygems/metadata_extraction_service_spec.rb'
- './spec/services/packages/rubygems/process_gem_service_spec.rb'
- './spec/services/packages/update_package_file_service_spec.rb'
- './spec/services/packages/update_tags_service_spec.rb'
- './spec/services/pages/delete_service_spec.rb'
- './spec/services/pages/destroy_deployments_service_spec.rb'
- './spec/services/pages_domains/create_acme_order_service_spec.rb'
- './spec/services/pages_domains/obtain_lets_encrypt_certificate_service_spec.rb'
- './spec/services/pages_domains/retry_acme_order_service_spec.rb'
- './spec/services/personal_access_tokens/create_service_spec.rb'
- './spec/services/personal_access_tokens/last_used_service_spec.rb'
- './spec/services/personal_access_tokens/revoke_service_spec.rb'
- './spec/services/post_receive_service_spec.rb'
- './spec/services/preview_markdown_service_spec.rb'
- './spec/services/projects/after_rename_service_spec.rb'
- './spec/services/projects/alerting/notify_service_spec.rb'
- './spec/services/projects/all_issues_count_service_spec.rb'
- './spec/services/projects/all_merge_requests_count_service_spec.rb'
- './spec/services/projects/apple_target_platform_detector_service_spec.rb'
- './spec/services/projects/autocomplete_service_spec.rb'
- './spec/services/projects/auto_devops/disable_service_spec.rb'
- './spec/services/projects/batch_open_issues_count_service_spec.rb'
- './spec/services/projects/branches_by_mode_service_spec.rb'
- './spec/services/projects/cleanup_service_spec.rb'
- './spec/services/projects/container_repository/cleanup_tags_service_spec.rb'
- './spec/services/projects/container_repository/delete_tags_service_spec.rb'
- './spec/services/projects/container_repository/destroy_service_spec.rb'
- './spec/services/projects/container_repository/gitlab/delete_tags_service_spec.rb'
- './spec/services/projects/container_repository/third_party/delete_tags_service_spec.rb'
- './spec/services/projects/count_service_spec.rb'
- './spec/services/projects/create_from_template_service_spec.rb'
- './spec/services/projects/create_service_spec.rb'
- './spec/services/projects/deploy_tokens/create_service_spec.rb'
- './spec/services/projects/deploy_tokens/destroy_service_spec.rb'
- './spec/services/projects/destroy_service_spec.rb'
- './spec/services/projects/detect_repository_languages_service_spec.rb'
- './spec/services/projects/download_service_spec.rb'
- './spec/services/projects/enable_deploy_key_service_spec.rb'
- './spec/services/projects/fetch_statistics_increment_service_spec.rb'
- './spec/services/projects/forks_count_service_spec.rb'
- './spec/services/projects/fork_service_spec.rb'
- './spec/services/projects/git_deduplication_service_spec.rb'
- './spec/services/projects/gitlab_projects_import_service_spec.rb'
- './spec/services/projects/group_links/create_service_spec.rb'
- './spec/services/projects/group_links/destroy_service_spec.rb'
- './spec/services/projects/group_links/update_service_spec.rb'
- './spec/services/projects/hashed_storage/base_attachment_service_spec.rb'
- './spec/services/projects/hashed_storage/migrate_attachments_service_spec.rb'
- './spec/services/projects/hashed_storage/migration_service_spec.rb'
- './spec/services/projects/import_error_filter_spec.rb'
- './spec/services/projects/import_export/export_service_spec.rb'
- './spec/services/projects/import_export/relation_export_service_spec.rb'
- './spec/services/projects/import_service_spec.rb'
- './spec/services/projects/lfs_pointers/lfs_download_link_list_service_spec.rb'
- './spec/services/projects/lfs_pointers/lfs_download_service_spec.rb'
- './spec/services/projects/lfs_pointers/lfs_import_service_spec.rb'
- './spec/services/projects/lfs_pointers/lfs_link_service_spec.rb'
- './spec/services/projects/lfs_pointers/lfs_object_download_list_service_spec.rb'
- './spec/services/projects/move_access_service_spec.rb'
- './spec/services/projects/move_deploy_keys_projects_service_spec.rb'
- './spec/services/projects/move_forks_service_spec.rb'
- './spec/services/projects/move_lfs_objects_projects_service_spec.rb'
- './spec/services/projects/move_notification_settings_service_spec.rb'
- './spec/services/projects/move_project_authorizations_service_spec.rb'
- './spec/services/projects/move_project_group_links_service_spec.rb'
- './spec/services/projects/move_project_members_service_spec.rb'
- './spec/services/projects/move_users_star_projects_service_spec.rb'
- './spec/services/projects/open_issues_count_service_spec.rb'
- './spec/services/projects/open_merge_requests_count_service_spec.rb'
- './spec/services/projects/operations/update_service_spec.rb'
- './spec/services/projects/overwrite_project_service_spec.rb'
- './spec/services/projects/participants_service_spec.rb'
- './spec/services/projects/prometheus/alerts/notify_service_spec.rb'
- './spec/services/projects/protect_default_branch_service_spec.rb'
- './spec/services/projects/readme_renderer_service_spec.rb'
- './spec/services/projects/record_target_platforms_service_spec.rb'
- './spec/services/projects/refresh_build_artifacts_size_statistics_service_spec.rb'
- './spec/services/projects/repository_languages_service_spec.rb'
- './spec/services/projects/schedule_bulk_repository_shard_moves_service_spec.rb'
- './spec/services/projects/transfer_service_spec.rb'
- './spec/services/projects/unlink_fork_service_spec.rb'
- './spec/services/projects/update_pages_service_spec.rb'
- './spec/services/projects/update_remote_mirror_service_spec.rb'
- './spec/services/projects/update_repository_storage_service_spec.rb'
- './spec/services/projects/update_service_spec.rb'
- './spec/services/projects/update_statistics_service_spec.rb'
- './spec/services/protected_branches/cache_service_spec.rb'
- './spec/services/protected_branches/create_service_spec.rb'
- './spec/services/protected_branches/destroy_service_spec.rb'
- './spec/services/protected_branches/update_service_spec.rb'
- './spec/services/protected_tags/create_service_spec.rb'
- './spec/services/protected_tags/destroy_service_spec.rb'
- './spec/services/protected_tags/update_service_spec.rb'
- './spec/services/push_event_payload_service_spec.rb'
- './spec/services/quick_actions/interpret_service_spec.rb'
- './spec/services/quick_actions/target_service_spec.rb'
- './spec/services/releases/create_evidence_service_spec.rb'
- './spec/services/releases/create_service_spec.rb'
- './spec/services/releases/destroy_service_spec.rb'
- './spec/services/releases/update_service_spec.rb'
- './spec/services/repositories/changelog_service_spec.rb'
- './spec/services/repositories/destroy_service_spec.rb'
- './spec/services/repositories/housekeeping_service_spec.rb'
- './spec/services/repository_archive_clean_up_service_spec.rb'
- './spec/services/reset_project_cache_service_spec.rb'
- './spec/services/resource_access_tokens/create_service_spec.rb'
- './spec/services/resource_access_tokens/revoke_service_spec.rb'
- './spec/services/resource_events/change_labels_service_spec.rb'
- './spec/services/resource_events/change_milestone_service_spec.rb'
- './spec/services/resource_events/change_state_service_spec.rb'
- './spec/services/resource_events/merge_into_notes_service_spec.rb'
- './spec/services/resource_events/synthetic_label_notes_builder_service_spec.rb'
- './spec/services/resource_events/synthetic_milestone_notes_builder_service_spec.rb'
- './spec/services/resource_events/synthetic_state_notes_builder_service_spec.rb'
- './spec/services/search/global_service_spec.rb'
- './spec/services/search/group_service_spec.rb'
- './spec/services/search/snippet_service_spec.rb'
- './spec/services/security/ci_configuration/container_scanning_create_service_spec.rb'
- './spec/services/security/ci_configuration/sast_create_service_spec.rb'
- './spec/services/security/ci_configuration/sast_iac_create_service_spec.rb'
- './spec/services/security/ci_configuration/sast_parser_service_spec.rb'
- './spec/services/security/ci_configuration/secret_detection_create_service_spec.rb'
- './spec/services/security/merge_reports_service_spec.rb'
- './spec/services/service_desk_settings/update_service_spec.rb'
- './spec/services/service_ping/submit_service_spec.rb'
- './spec/services/service_response_spec.rb'
- './spec/services/snippets/bulk_destroy_service_spec.rb'
- './spec/services/snippets/count_service_spec.rb'
- './spec/services/snippets/create_service_spec.rb'
- './spec/services/snippets/destroy_service_spec.rb'
- './spec/services/snippets/repository_validation_service_spec.rb'
- './spec/services/snippets/schedule_bulk_repository_shard_moves_service_spec.rb'
- './spec/services/snippets/update_repository_storage_service_spec.rb'
- './spec/services/snippets/update_service_spec.rb'
- './spec/services/snippets/update_statistics_service_spec.rb'
- './spec/services/spam/akismet_mark_as_spam_service_spec.rb'
- './spec/services/spam/akismet_service_spec.rb'
- './spec/services/spam/ham_service_spec.rb'
- './spec/services/spam/spam_action_service_spec.rb'
- './spec/services/spam/spam_params_spec.rb'
- './spec/services/spam/spam_verdict_service_spec.rb'
- './spec/services/submodules/update_service_spec.rb'
- './spec/services/suggestions/apply_service_spec.rb'
- './spec/services/suggestions/create_service_spec.rb'
- './spec/services/suggestions/outdate_service_spec.rb'
- './spec/services/system_hooks_service_spec.rb'
- './spec/services/system_notes/alert_management_service_spec.rb'
- './spec/services/system_notes/base_service_spec.rb'
- './spec/services/system_notes/commit_service_spec.rb'
- './spec/services/system_notes/design_management_service_spec.rb'
- './spec/services/system_note_service_spec.rb'
- './spec/services/system_notes/issuables_service_spec.rb'
- './spec/services/system_notes/merge_requests_service_spec.rb'
- './spec/services/system_notes/time_tracking_service_spec.rb'
- './spec/services/system_notes/zoom_service_spec.rb'
- './spec/services/tags/create_service_spec.rb'
- './spec/services/tags/destroy_service_spec.rb'
- './spec/services/task_list_toggle_service_spec.rb'
- './spec/services/terraform/remote_state_handler_spec.rb'
- './spec/services/terraform/states/destroy_service_spec.rb'
- './spec/services/terraform/states/trigger_destroy_service_spec.rb'
- './spec/services/test_hooks/project_service_spec.rb'
- './spec/services/test_hooks/system_service_spec.rb'
- './spec/services/timelogs/create_service_spec.rb'
- './spec/services/timelogs/delete_service_spec.rb'
- './spec/services/todo_service_spec.rb'
- './spec/services/topics/merge_service_spec.rb'
- './spec/services/two_factor/destroy_service_spec.rb'
- './spec/services/update_container_registry_info_service_spec.rb'
- './spec/services/update_merge_request_metrics_service_spec.rb'
- './spec/services/uploads/destroy_service_spec.rb'
- './spec/services/upload_service_spec.rb'
- './spec/services/user_preferences/update_service_spec.rb'
- './spec/services/user_project_access_changed_service_spec.rb'
- './spec/services/users/activity_service_spec.rb'
- './spec/services/users/approve_service_spec.rb'
- './spec/services/users/authorized_build_service_spec.rb'
- './spec/services/users/banned_user_base_service_spec.rb'
- './spec/services/users/ban_service_spec.rb'
- './spec/services/users/batch_status_cleaner_service_spec.rb'
- './spec/services/users/block_service_spec.rb'
- './spec/services/users/build_service_spec.rb'
- './spec/services/users/create_service_spec.rb'
- './spec/services/users/destroy_service_spec.rb'
- './spec/services/users/dismiss_callout_service_spec.rb'
- './spec/services/users/dismiss_group_callout_service_spec.rb'
- './spec/services/users/dismiss_project_callout_service_spec.rb'
- './spec/services/users/email_verification/generate_token_service_spec.rb'
- './spec/services/users/email_verification/validate_token_service_spec.rb'
- './spec/services/users/keys_count_service_spec.rb'
- './spec/services/users/last_push_event_service_spec.rb'
- './spec/services/users/refresh_authorized_projects_service_spec.rb'
- './spec/services/users/registrations_build_service_spec.rb'
- './spec/services/users/reject_service_spec.rb'
- './spec/services/users/repair_ldap_blocked_service_spec.rb'
- './spec/services/users/respond_to_terms_service_spec.rb'
- './spec/services/users/set_status_service_spec.rb'
- './spec/services/users/unban_service_spec.rb'
- './spec/services/users/update_highest_member_role_service_spec.rb'
- './spec/services/users/update_service_spec.rb'
- './spec/services/users/update_todo_count_cache_service_spec.rb'
- './spec/services/users/upsert_credit_card_validation_service_spec.rb'
- './spec/services/users/validate_manual_otp_service_spec.rb'
- './spec/services/users/validate_push_otp_service_spec.rb'
- './spec/services/verify_pages_domain_service_spec.rb'
- './spec/services/webauthn/authenticate_service_spec.rb'
- './spec/services/webauthn/register_service_spec.rb'
- './spec/services/web_hook_service_spec.rb'
- './spec/services/work_items/build_service_spec.rb'
- './spec/services/work_items/create_and_link_service_spec.rb'
- './spec/services/work_items/create_from_task_service_spec.rb'
- './spec/services/work_items/create_service_spec.rb'
- './spec/services/work_items/delete_service_spec.rb'
- './spec/services/work_items/parent_links/create_service_spec.rb'
- './spec/services/work_items/parent_links/destroy_service_spec.rb'
- './spec/services/work_items/task_list_reference_replacement_service_spec.rb'
- './spec/services/work_items/update_service_spec.rb'
- './spec/services/work_items/widgets/hierarchy_service/update_service_spec.rb'
- './spec/services/x509_certificate_revoke_service_spec.rb'
- './spec/sidekiq_cluster/sidekiq_cluster_spec.rb'
- './spec/sidekiq/cron/job_gem_dependency_spec.rb'
- './spec/spam/concerns/has_spam_action_response_fields_spec.rb'
- './spec/support_specs/database/prevent_cross_joins_spec.rb'
- './spec/support_specs/graphql/arguments_spec.rb'
- './spec/support_specs/graphql/field_selection_spec.rb'
- './spec/support_specs/graphql/var_spec.rb'
- './spec/support_specs/helpers/active_record/query_recorder_spec.rb'
- './spec/support_specs/helpers/graphql_helpers_spec.rb'
- './spec/support_specs/helpers/migrations_helpers_spec.rb'
- './spec/support_specs/helpers/redis_commands/recorder_spec.rb'
- './spec/support_specs/helpers/stub_feature_flags_spec.rb'
- './spec/support_specs/helpers/stub_method_calls_spec.rb'
- './spec/support_specs/matchers/be_sorted_spec.rb'
- './spec/support_specs/matchers/exceed_query_limit_helpers_spec.rb'
- './spec/tasks/admin_mode_spec.rb'
- './spec/tasks/dev_rake_spec.rb'
- './spec/tasks/gitlab/artifacts/check_rake_spec.rb'
- './spec/tasks/gitlab/artifacts/migrate_rake_spec.rb'
- './spec/tasks/gitlab/background_migrations_rake_spec.rb'
- './spec/tasks/gitlab/backup_rake_spec.rb'
- './spec/tasks/gitlab/check_rake_spec.rb'
- './spec/tasks/gitlab/cleanup_rake_spec.rb'
- './spec/tasks/gitlab/container_registry_rake_spec.rb'
- './spec/tasks/gitlab/db/decomposition/rollback/bump_ci_sequences_rake_spec.rb'
- './spec/tasks/gitlab/db/lock_writes_rake_spec.rb'
- './spec/tasks/gitlab/db_rake_spec.rb'
- './spec/tasks/gitlab/db/validate_config_rake_spec.rb'
- './spec/tasks/gitlab/dependency_proxy/migrate_rake_spec.rb'
- './spec/tasks/gitlab/external_diffs_rake_spec.rb'
- './spec/tasks/gitlab/gitaly_rake_spec.rb'
- './spec/tasks/gitlab/git_rake_spec.rb'
- './spec/tasks/gitlab/ldap_rake_spec.rb'
- './spec/tasks/gitlab/lfs/check_rake_spec.rb'
- './spec/tasks/gitlab/lfs/migrate_rake_spec.rb'
- './spec/tasks/gitlab/packages/migrate_rake_spec.rb'
- './spec/tasks/gitlab/pages_rake_spec.rb'
- './spec/tasks/gitlab/password_rake_spec.rb'
- './spec/tasks/gitlab/praefect_rake_spec.rb'
- './spec/tasks/gitlab/refresh_project_statistics_build_artifacts_size_rake_spec.rb'
- './spec/tasks/gitlab/seed/group_seed_rake_spec.rb'
- './spec/tasks/gitlab/setup_rake_spec.rb'
- './spec/tasks/gitlab/shell_rake_spec.rb'
- './spec/tasks/gitlab/sidekiq_rake_spec.rb'
- './spec/tasks/gitlab/smtp_rake_spec.rb'
- './spec/tasks/gitlab/snippets_rake_spec.rb'
- './spec/tasks/gitlab/terraform/migrate_rake_spec.rb'
- './spec/tasks/gitlab/update_templates_rake_spec.rb'
- './spec/tasks/gitlab/uploads/check_rake_spec.rb'
- './spec/tasks/gitlab/uploads/migrate_rake_spec.rb'
- './spec/tasks/gitlab/usage_data_rake_spec.rb'
- './spec/tasks/gitlab/user_management_rake_spec.rb'
- './spec/tasks/gitlab/web_hook_rake_spec.rb'
- './spec/tasks/gitlab/workhorse_rake_spec.rb'
- './spec/tasks/gitlab/x509/update_rake_spec.rb'
- './spec/tasks/migrate/schema_check_rake_spec.rb'
- './spec/tasks/rubocop_rake_spec.rb'
- './spec/tooling/docs/deprecation_handling_spec.rb'
- './spec/tooling/graphql/docs/renderer_spec.rb'
- './spec/tooling/lib/tooling/find_codeowners_spec.rb'
- './spec/tooling/lib/tooling/helm3_client_spec.rb'
- './spec/tooling/lib/tooling/kubernetes_client_spec.rb'
- './spec/tooling/lib/tooling/parallel_rspec_runner_spec.rb'
- './spec/tooling/lib/tooling/test_map_generator_spec.rb'
- './spec/tooling/lib/tooling/test_map_packer_spec.rb'
- './spec/tooling/merge_request_spec.rb'
- './spec/tooling/quality/test_level_spec.rb'
- './spec/uploaders/attachment_uploader_spec.rb'
- './spec/uploaders/avatar_uploader_spec.rb'
- './spec/uploaders/ci/pipeline_artifact_uploader_spec.rb'
- './spec/uploaders/ci/secure_file_uploader_spec.rb'
- './spec/uploaders/content_type_whitelist_spec.rb'
- './spec/uploaders/dependency_proxy/file_uploader_spec.rb'
- './spec/uploaders/design_management/design_v432x230_uploader_spec.rb'
- './spec/uploaders/external_diff_uploader_spec.rb'
- './spec/uploaders/favicon_uploader_spec.rb'
- './spec/uploaders/file_mover_spec.rb'
- './spec/uploaders/file_uploader_spec.rb'
- './spec/uploaders/gitlab_uploader_spec.rb'
- './spec/uploaders/import_export_uploader_spec.rb'
- './spec/uploaders/job_artifact_uploader_spec.rb'
- './spec/uploaders/lfs_object_uploader_spec.rb'
- './spec/uploaders/metric_image_uploader_spec.rb'
- './spec/uploaders/namespace_file_uploader_spec.rb'
- './spec/uploaders/object_storage_spec.rb'
- './spec/uploaders/packages/debian/component_file_uploader_spec.rb'
- './spec/uploaders/packages/debian/distribution_release_file_uploader_spec.rb'
- './spec/uploaders/packages/package_file_uploader_spec.rb'
- './spec/uploaders/pages/deployment_uploader_spec.rb'
- './spec/uploaders/personal_file_uploader_spec.rb'
- './spec/uploaders/records_uploads_spec.rb'
- './spec/uploaders/terraform/state_uploader_spec.rb'
- './spec/uploaders/uploader_helper_spec.rb'
- './spec/uploaders/workers/object_storage/migrate_uploads_worker_spec.rb'
- './spec/workers/admin_email_worker_spec.rb'
- './spec/workers/analytics/usage_trends/counter_job_worker_spec.rb'
- './spec/workers/analytics/usage_trends/count_job_trigger_worker_spec.rb'
- './spec/workers/approve_blocked_pending_approval_users_worker_spec.rb'
- './spec/workers/authorized_keys_worker_spec.rb'
- './spec/workers/authorized_projects_worker_spec.rb'
- './spec/workers/authorized_project_update/periodic_recalculate_worker_spec.rb'
- './spec/workers/authorized_project_update/project_recalculate_per_user_worker_spec.rb'
- './spec/workers/authorized_project_update/project_recalculate_worker_spec.rb'
- './spec/workers/authorized_project_update/user_refresh_from_replica_worker_spec.rb'
- './spec/workers/authorized_project_update/user_refresh_over_user_range_worker_spec.rb'
- './spec/workers/authorized_project_update/user_refresh_with_low_urgency_worker_spec.rb'
- './spec/workers/auto_devops/disable_worker_spec.rb'
- './spec/workers/auto_merge_process_worker_spec.rb'
- './spec/workers/background_migration/ci_database_worker_spec.rb'
- './spec/workers/background_migration_worker_spec.rb'
- './spec/workers/build_queue_worker_spec.rb'
- './spec/workers/bulk_imports/entity_worker_spec.rb'
- './spec/workers/bulk_imports/export_request_worker_spec.rb'
- './spec/workers/bulk_imports/pipeline_worker_spec.rb'
- './spec/workers/bulk_imports/relation_export_worker_spec.rb'
- './spec/workers/bulk_imports/stale_import_worker_spec.rb'
- './spec/workers/bulk_import_worker_spec.rb'
- './spec/workers/chat_notification_worker_spec.rb'
- './spec/workers/ci/archive_traces_cron_worker_spec.rb'
- './spec/workers/ci/archive_trace_worker_spec.rb'
- './spec/workers/ci/build_finished_worker_spec.rb'
- './spec/workers/ci/build_prepare_worker_spec.rb'
- './spec/workers/ci/build_schedule_worker_spec.rb'
- './spec/workers/ci/build_trace_chunk_flush_worker_spec.rb'
- './spec/workers/ci/cancel_pipeline_worker_spec.rb'
- './spec/workers/ci/create_downstream_pipeline_worker_spec.rb'
- './spec/workers/ci/daily_build_group_report_results_worker_spec.rb'
- './spec/workers/ci/delete_objects_worker_spec.rb'
- './spec/workers/ci/delete_unit_tests_worker_spec.rb'
- './spec/workers/ci/drop_pipeline_worker_spec.rb'
- './spec/workers/ci/external_pull_requests/create_pipeline_worker_spec.rb'
- './spec/workers/ci/initial_pipeline_process_worker_spec.rb'
- './spec/workers/ci/job_artifacts/expire_project_build_artifacts_worker_spec.rb'
- './spec/workers/ci/merge_requests/add_todo_when_build_fails_worker_spec.rb'
- './spec/workers/ci/pending_builds/update_group_worker_spec.rb'
- './spec/workers/ci/pending_builds/update_project_worker_spec.rb'
- './spec/workers/ci/pipeline_artifacts/coverage_report_worker_spec.rb'
- './spec/workers/ci/pipeline_artifacts/create_quality_report_worker_spec.rb'
- './spec/workers/ci/pipeline_artifacts/expire_artifacts_worker_spec.rb'
- './spec/workers/ci/pipeline_bridge_status_worker_spec.rb'
- './spec/workers/ci/ref_delete_unlock_artifacts_worker_spec.rb'
- './spec/workers/ci/resource_groups/assign_resource_from_resource_group_worker_spec.rb'
- './spec/workers/ci/retry_pipeline_worker_spec.rb'
- './spec/workers/ci/runners/process_runner_version_update_worker_spec.rb'
- './spec/workers/ci/runners/reconcile_existing_runner_versions_cron_worker_spec.rb'
- './spec/workers/ci/schedule_delete_objects_cron_worker_spec.rb'
- './spec/workers/ci/stuck_builds/drop_running_worker_spec.rb'
- './spec/workers/ci/stuck_builds/drop_scheduled_worker_spec.rb'
- './spec/workers/ci/test_failure_history_worker_spec.rb'
- './spec/workers/ci/track_failed_build_worker_spec.rb'
- './spec/workers/ci/update_locked_unknown_artifacts_worker_spec.rb'
- './spec/workers/cleanup_container_repository_worker_spec.rb'
- './spec/workers/clusters/agents/delete_expired_events_worker_spec.rb'
- './spec/workers/clusters/applications/activate_integration_worker_spec.rb'
- './spec/workers/clusters/applications/deactivate_integration_worker_spec.rb'
- './spec/workers/clusters/cleanup/project_namespace_worker_spec.rb'
- './spec/workers/clusters/cleanup/service_account_worker_spec.rb'
- './spec/workers/concerns/application_worker_spec.rb'
- './spec/workers/concerns/cluster_agent_queue_spec.rb'
- './spec/workers/concerns/cronjob_queue_spec.rb'
- './spec/workers/concerns/gitlab/github_import/object_importer_spec.rb'
- './spec/workers/concerns/gitlab/github_import/rescheduling_methods_spec.rb'
- './spec/workers/concerns/limited_capacity/job_tracker_spec.rb'
- './spec/workers/concerns/limited_capacity/worker_spec.rb'
- './spec/workers/concerns/packages/cleanup_artifact_worker_spec.rb'
- './spec/workers/concerns/project_import_options_spec.rb'
- './spec/workers/concerns/reenqueuer_spec.rb'
- './spec/workers/concerns/repository_check_queue_spec.rb'
- './spec/workers/concerns/worker_attributes_spec.rb'
- './spec/workers/concerns/worker_context_spec.rb'
- './spec/workers/container_expiration_policies/cleanup_container_repository_worker_spec.rb'
- './spec/workers/container_expiration_policy_worker_spec.rb'
- './spec/workers/create_commit_signature_worker_spec.rb'
- './spec/workers/create_note_diff_file_worker_spec.rb'
- './spec/workers/create_pipeline_worker_spec.rb'
- './spec/workers/database/batched_background_migration/ci_database_worker_spec.rb'
- './spec/workers/database/batched_background_migration_worker_spec.rb'
- './spec/workers/database/ci_namespace_mirrors_consistency_check_worker_spec.rb'
- './spec/workers/database/ci_project_mirrors_consistency_check_worker_spec.rb'
- './spec/workers/database/drop_detached_partitions_worker_spec.rb'
- './spec/workers/database/partition_management_worker_spec.rb'
- './spec/workers/delete_diff_files_worker_spec.rb'
- './spec/workers/delete_merged_branches_worker_spec.rb'
- './spec/workers/delete_user_worker_spec.rb'
- './spec/workers/dependency_proxy/cleanup_blob_worker_spec.rb'
- './spec/workers/dependency_proxy/cleanup_dependency_proxy_worker_spec.rb'
- './spec/workers/dependency_proxy/cleanup_manifest_worker_spec.rb'
- './spec/workers/dependency_proxy/image_ttl_group_policy_worker_spec.rb'
- './spec/workers/deployments/archive_in_project_worker_spec.rb'
- './spec/workers/deployments/hooks_worker_spec.rb'
- './spec/workers/deployments/link_merge_request_worker_spec.rb'
- './spec/workers/deployments/update_environment_worker_spec.rb'
- './spec/workers/design_management/copy_design_collection_worker_spec.rb'
- './spec/workers/design_management/new_version_worker_spec.rb'
- './spec/workers/destroy_pages_deployments_worker_spec.rb'
- './spec/workers/detect_repository_languages_worker_spec.rb'
- './spec/workers/disallow_two_factor_for_group_worker_spec.rb'
- './spec/workers/disallow_two_factor_for_subgroups_worker_spec.rb'
- './spec/workers/email_receiver_worker_spec.rb'
- './spec/workers/emails_on_push_worker_spec.rb'
- './spec/workers/environments/auto_delete_cron_worker_spec.rb'
- './spec/workers/environments/auto_stop_cron_worker_spec.rb'
- './spec/workers/environments/auto_stop_worker_spec.rb'
- './spec/workers/environments/canary_ingress/update_worker_spec.rb'
- './spec/workers/error_tracking_issue_link_worker_spec.rb'
- './spec/workers/expire_build_artifacts_worker_spec.rb'
- './spec/workers/export_csv_worker_spec.rb'
- './spec/workers/external_service_reactive_caching_worker_spec.rb'
- './spec/workers/file_hook_worker_spec.rb'
- './spec/workers/flush_counter_increments_worker_spec.rb'
- './spec/workers/google_cloud/create_cloudsql_instance_worker_spec.rb'
- './spec/workers/group_destroy_worker_spec.rb'
- './spec/workers/group_export_worker_spec.rb'
- './spec/workers/group_import_worker_spec.rb'
- './spec/workers/groups/update_statistics_worker_spec.rb'
- './spec/workers/import_issues_csv_worker_spec.rb'
- './spec/workers/integrations/create_external_cross_reference_worker_spec.rb'
- './spec/workers/integrations/execute_worker_spec.rb'
- './spec/workers/integrations/irker_worker_spec.rb'
- './spec/workers/invalid_gpg_signature_update_worker_spec.rb'
- './spec/workers/issuable_export_csv_worker_spec.rb'
- './spec/workers/issuable/label_links_destroy_worker_spec.rb'
- './spec/workers/issuables/clear_groups_issue_counter_worker_spec.rb'
- './spec/workers/issue_due_scheduler_worker_spec.rb'
- './spec/workers/issues/placement_worker_spec.rb'
- './spec/workers/issues/rebalancing_worker_spec.rb'
- './spec/workers/issues/reschedule_stuck_issue_rebalances_worker_spec.rb'
- './spec/workers/jira_connect/forward_event_worker_spec.rb'
- './spec/workers/jira_connect/retry_request_worker_spec.rb'
- './spec/workers/jira_connect/sync_branch_worker_spec.rb'
- './spec/workers/jira_connect/sync_builds_worker_spec.rb'
- './spec/workers/jira_connect/sync_deployments_worker_spec.rb'
- './spec/workers/jira_connect/sync_feature_flags_worker_spec.rb'
- './spec/workers/jira_connect/sync_merge_request_worker_spec.rb'
- './spec/workers/jira_connect/sync_project_worker_spec.rb'
- './spec/workers/loose_foreign_keys/cleanup_worker_spec.rb'
- './spec/workers/mail_scheduler/issue_due_worker_spec.rb'
- './spec/workers/mail_scheduler/notification_service_worker_spec.rb'
- './spec/workers/member_invitation_reminder_emails_worker_spec.rb'
- './spec/workers/members_destroyer/unassign_issuables_worker_spec.rb'
- './spec/workers/merge_request_cleanup_refs_worker_spec.rb'
- './spec/workers/merge_request_mergeability_check_worker_spec.rb'
- './spec/workers/merge_requests/close_issue_worker_spec.rb'
- './spec/workers/merge_requests/create_approval_event_worker_spec.rb'
- './spec/workers/merge_requests/create_approval_note_worker_spec.rb'
- './spec/workers/merge_requests/create_pipeline_worker_spec.rb'
- './spec/workers/merge_requests/delete_source_branch_worker_spec.rb'
- './spec/workers/merge_requests/execute_approval_hooks_worker_spec.rb'
- './spec/workers/merge_requests/handle_assignees_change_worker_spec.rb'
- './spec/workers/merge_requests/resolve_todos_after_approval_worker_spec.rb'
- './spec/workers/merge_requests/resolve_todos_worker_spec.rb'
- './spec/workers/merge_requests/update_head_pipeline_worker_spec.rb'
- './spec/workers/merge_worker_spec.rb'
- './spec/workers/migrate_external_diffs_worker_spec.rb'
- './spec/workers/namespaces/process_sync_events_worker_spec.rb'
- './spec/workers/namespaces/prune_aggregation_schedules_worker_spec.rb'
- './spec/workers/namespaces/root_statistics_worker_spec.rb'
- './spec/workers/namespaces/schedule_aggregation_worker_spec.rb'
- './spec/workers/namespaces/update_root_statistics_worker_spec.rb'
- './spec/workers/new_issue_worker_spec.rb'
- './spec/workers/new_merge_request_worker_spec.rb'
- './spec/workers/new_note_worker_spec.rb'
- './spec/workers/object_pool/create_worker_spec.rb'
- './spec/workers/object_pool/destroy_worker_spec.rb'
- './spec/workers/object_pool/join_worker_spec.rb'
- './spec/workers/packages/cleanup/execute_policy_worker_spec.rb'
- './spec/workers/packages/cleanup_package_file_worker_spec.rb'
- './spec/workers/packages/cleanup_package_registry_worker_spec.rb'
- './spec/workers/packages/debian/generate_distribution_worker_spec.rb'
- './spec/workers/packages/go/sync_packages_worker_spec.rb'
- './spec/workers/packages/helm/extraction_worker_spec.rb'
- './spec/workers/packages/mark_package_files_for_destruction_worker_spec.rb'
- './spec/workers/packages/maven/metadata/sync_worker_spec.rb'
- './spec/workers/packages/nuget/extraction_worker_spec.rb'
- './spec/workers/packages/rubygems/extraction_worker_spec.rb'
- './spec/workers/pages_domain_removal_cron_worker_spec.rb'
- './spec/workers/pages_domain_ssl_renewal_cron_worker_spec.rb'
- './spec/workers/pages_domain_ssl_renewal_worker_spec.rb'
- './spec/workers/pages_domain_verification_cron_worker_spec.rb'
- './spec/workers/pages_domain_verification_worker_spec.rb'
- './spec/workers/pages_worker_spec.rb'
- './spec/workers/personal_access_tokens/expired_notification_worker_spec.rb'
- './spec/workers/personal_access_tokens/expiring_worker_spec.rb'
- './spec/workers/pipeline_hooks_worker_spec.rb'
- './spec/workers/pipeline_metrics_worker_spec.rb'
- './spec/workers/pipeline_notification_worker_spec.rb'
- './spec/workers/pipeline_process_worker_spec.rb'
- './spec/workers/pipeline_schedule_worker_spec.rb'
- './spec/workers/post_receive_spec.rb'
- './spec/workers/process_commit_worker_spec.rb'
- './spec/workers/project_cache_worker_spec.rb'
- './spec/workers/project_export_worker_spec.rb'
- './spec/workers/projects/after_import_worker_spec.rb'
- './spec/workers/projects/git_garbage_collect_worker_spec.rb'
- './spec/workers/projects/import_export/relation_export_worker_spec.rb'
- './spec/workers/projects/inactive_projects_deletion_cron_worker_spec.rb'
- './spec/workers/projects/inactive_projects_deletion_notification_worker_spec.rb'
- './spec/workers/projects/post_creation_worker_spec.rb'
- './spec/workers/projects/process_sync_events_worker_spec.rb'
- './spec/workers/projects/record_target_platforms_worker_spec.rb'
- './spec/workers/projects/refresh_build_artifacts_size_statistics_worker_spec.rb'
- './spec/workers/projects/schedule_bulk_repository_shard_moves_worker_spec.rb'
- './spec/workers/projects/schedule_refresh_build_artifacts_size_statistics_worker_spec.rb'
- './spec/workers/projects/update_repository_storage_worker_spec.rb'
- './spec/workers/propagate_integration_group_worker_spec.rb'
- './spec/workers/propagate_integration_inherit_descendant_worker_spec.rb'
- './spec/workers/propagate_integration_inherit_worker_spec.rb'
- './spec/workers/propagate_integration_project_worker_spec.rb'
- './spec/workers/propagate_integration_worker_spec.rb'
- './spec/workers/prune_old_events_worker_spec.rb'
- './spec/workers/purge_dependency_proxy_cache_worker_spec.rb'
- './spec/workers/reactive_caching_worker_spec.rb'
- './spec/workers/rebase_worker_spec.rb'
- './spec/workers/releases/create_evidence_worker_spec.rb'
- './spec/workers/releases/manage_evidence_worker_spec.rb'
- './spec/workers/remote_mirror_notification_worker_spec.rb'
- './spec/workers/remove_expired_group_links_worker_spec.rb'
- './spec/workers/remove_expired_members_worker_spec.rb'
- './spec/workers/remove_unaccepted_member_invites_worker_spec.rb'
- './spec/workers/remove_unreferenced_lfs_objects_worker_spec.rb'
- './spec/workers/repository_check/batch_worker_spec.rb'
- './spec/workers/repository_check/clear_worker_spec.rb'
- './spec/workers/repository_check/dispatch_worker_spec.rb'
- './spec/workers/repository_check/single_repository_worker_spec.rb'
- './spec/workers/repository_cleanup_worker_spec.rb'
- './spec/workers/repository_fork_worker_spec.rb'
- './spec/workers/repository_import_worker_spec.rb'
- './spec/workers/repository_update_remote_mirror_worker_spec.rb'
- './spec/workers/run_pipeline_schedule_worker_spec.rb'
- './spec/workers/schedule_merge_request_cleanup_refs_worker_spec.rb'
- './spec/workers/schedule_migrate_external_diffs_worker_spec.rb'
- './spec/workers/service_desk_email_receiver_worker_spec.rb'
- './spec/workers/snippets/schedule_bulk_repository_shard_moves_worker_spec.rb'
- './spec/workers/snippets/update_repository_storage_worker_spec.rb'
- './spec/workers/ssh_keys/expired_notification_worker_spec.rb'
- './spec/workers/ssh_keys/expiring_soon_notification_worker_spec.rb'
- './spec/workers/stage_update_worker_spec.rb'
- './spec/workers/stuck_ci_jobs_worker_spec.rb'
- './spec/workers/stuck_export_jobs_worker_spec.rb'
- './spec/workers/stuck_merge_jobs_worker_spec.rb'
- './spec/workers/system_hook_push_worker_spec.rb'
- './spec/workers/terraform/states/destroy_worker_spec.rb'
- './spec/workers/todos_destroyer/confidential_issue_worker_spec.rb'
- './spec/workers/todos_destroyer/destroyed_designs_worker_spec.rb'
- './spec/workers/todos_destroyer/destroyed_issuable_worker_spec.rb'
- './spec/workers/todos_destroyer/entity_leave_worker_spec.rb'
- './spec/workers/todos_destroyer/group_private_worker_spec.rb'
- './spec/workers/todos_destroyer/private_features_worker_spec.rb'
- './spec/workers/todos_destroyer/project_private_worker_spec.rb'
- './spec/workers/trending_projects_worker_spec.rb'
- './spec/workers/update_container_registry_info_worker_spec.rb'
- './spec/workers/update_external_pull_requests_worker_spec.rb'
- './spec/workers/update_head_pipeline_for_merge_request_worker_spec.rb'
- './spec/workers/update_highest_role_worker_spec.rb'
- './spec/workers/update_merge_requests_worker_spec.rb'
- './spec/workers/update_project_statistics_worker_spec.rb'
- './spec/workers/upload_checksum_worker_spec.rb'
- './spec/workers/users/create_statistics_worker_spec.rb'
- './spec/workers/users/deactivate_dormant_users_worker_spec.rb'
- './spec/workers/user_status_cleanup/batch_worker_spec.rb'
- './spec/workers/web_hooks/log_destroy_worker_spec.rb'
- './spec/workers/web_hook_worker_spec.rb'
- './spec/workers/wikis/git_garbage_collect_worker_spec.rb'
- './spec/workers/x509_certificate_revoke_worker_spec.rb'
- './spec/workers/x509_issuer_crl_check_worker_spec.rb'
