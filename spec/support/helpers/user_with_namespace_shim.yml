---
- ee/spec/controllers/admin/users_controller_spec.rb
- ee/spec/controllers/autocomplete_controller_spec.rb
- ee/spec/controllers/ee/profiles/preferences_controller_spec.rb
- ee/spec/controllers/ee/search_controller_spec.rb
- ee/spec/controllers/groups/sso_controller_spec.rb
- ee/spec/controllers/profiles/billings_controller_spec.rb
- ee/spec/controllers/projects/iterations_controller_spec.rb
- ee/spec/controllers/projects_controller_spec.rb
- ee/spec/controllers/users_controller_spec.rb
- ee/spec/features/account_recovery_regular_check_spec.rb
- ee/spec/features/admin/admin_audit_logs_spec.rb
- ee/spec/features/admin/admin_groups_spec.rb
- ee/spec/features/admin/admin_reset_pipeline_minutes_spec.rb
- ee/spec/features/admin/admin_users_spec.rb
- ee/spec/features/admin/groups/admin_changes_plan_spec.rb
- ee/spec/features/admin/users/users_spec.rb
- ee/spec/features/analytics/code_analytics_spec.rb
- ee/spec/features/billings/billing_plans_spec.rb
- ee/spec/features/boards/boards_licensed_features_spec.rb
- ee/spec/features/boards/boards_spec.rb
- ee/spec/features/boards/swimlanes/epics_swimlanes_spec.rb
- ee/spec/features/ci/ci_minutes_spec.rb
- ee/spec/features/ci_shared_runner_warnings_spec.rb
- ee/spec/features/dashboards/todos_spec.rb
- ee/spec/features/groups/analytics/productivity_analytics_spec.rb
- ee/spec/features/groups/member_roles_spec.rb
- ee/spec/features/groups/members/list_members_spec.rb
- ee/spec/features/groups/usage_quotas/code_suggestions_usage_tab_spec.rb
- ee/spec/features/groups/wikis_spec.rb
- ee/spec/features/incidents/incident_details_spec.rb
- ee/spec/features/issues/epic_in_issue_sidebar_spec.rb
- ee/spec/features/issues/issue_sidebar_spec.rb
- ee/spec/features/issues/list/user_bulk_edits_issues_spec.rb
- ee/spec/features/issues/list/user_sees_empty_state_spec.rb
- ee/spec/features/issues/list/user_views_issues_spec.rb
- ee/spec/features/merge_request/code_owner_approvals_reset_after_merging_to_source_branch_spec.rb
- ee/spec/features/merge_request/draft_comments_spec.rb
- ee/spec/features/merge_request/user_approves_with_password_spec.rb
- ee/spec/features/merge_request/user_approves_with_saml_auth_spec.rb
- ee/spec/features/merge_request/user_comments_on_merge_request_spec.rb
- ee/spec/features/merge_request/user_creates_merge_request_spec.rb
- ee/spec/features/merge_request/user_creates_multiple_assignees_mr_spec.rb
- ee/spec/features/merge_request/user_creates_multiple_reviewers_mr_spec.rb
- ee/spec/features/merge_request/user_edits_multiple_assignees_mr_spec.rb
- ee/spec/features/merge_request/user_edits_multiple_reviewers_mr_spec.rb
- ee/spec/features/merge_request/user_merges_immediately_spec.rb
- ee/spec/features/merge_request/user_merges_with_namespace_storage_limits_spec.rb
- ee/spec/features/merge_request/user_merges_with_push_rules_spec.rb
- ee/spec/features/merge_request/user_sees_approve_via_custom_role_spec.rb
- ee/spec/features/merge_request/user_sees_merge_widget_spec.rb
- ee/spec/features/merge_request/user_sees_security_policy_rules_licence_compliance_spec.rb
- ee/spec/features/merge_request/user_sees_status_checks_widget_spec.rb
- ee/spec/features/merge_request/user_sets_approval_rules_spec.rb
- ee/spec/features/merge_request/user_sets_approvers_spec.rb
- ee/spec/features/merge_request/user_uses_slash_commands_spec.rb
- ee/spec/features/merge_request/user_views_blocked_merge_request_spec.rb
- ee/spec/features/merge_trains/user_adds_to_merge_train_when_pipeline_succeeds_spec.rb
- ee/spec/features/namespace_user_cap_reached_alert_spec.rb
- ee/spec/features/pending_project_memberships_spec.rb
- ee/spec/features/profiles/account_spec.rb
- ee/spec/features/profiles/password_spec.rb
- ee/spec/features/profiles/user_visits_profile_spec.rb
- ee/spec/features/profiles/user_visits_public_profile_spec.rb
- ee/spec/features/projects/active_tabs_spec.rb
- ee/spec/features/projects/audit_events_spec.rb
- ee/spec/features/projects/custom_projects_template_spec.rb
- ee/spec/features/projects/environments/environment_spec.rb
- ee/spec/features/projects/feature_flags/feature_flag_issues_spec.rb
- ee/spec/features/projects/feature_flags/user_creates_feature_flag_spec.rb
- ee/spec/features/projects/feature_flags/user_deletes_feature_flag_spec.rb
- ee/spec/features/projects/feature_flags/user_sees_feature_flag_list_spec.rb
- ee/spec/features/projects/feature_flags/user_updates_feature_flag_spec.rb
- ee/spec/features/projects/members/member_leaves_project_spec.rb
- ee/spec/features/projects/merge_requests/user_approves_merge_request_spec.rb
- ee/spec/features/projects/milestones/milestone_spec.rb
- ee/spec/features/projects/navbar_spec.rb
- ee/spec/features/projects/new_project_from_template_spec.rb
- ee/spec/features/projects/new_project_spec.rb
- ee/spec/features/projects/path_locks_spec.rb
- ee/spec/features/projects/push_rules_spec.rb
- ee/spec/features/projects/settings/ee/repository_mirrors_settings_spec.rb
- ee/spec/features/projects/settings/merge_requests/user_manages_merge_requests_template_spec.rb
- ee/spec/features/projects/settings/packages_spec.rb
- ee/spec/features/projects/settings/user_changes_default_branch_spec.rb
- ee/spec/features/projects/settings/user_manages_issues_template_spec.rb
- ee/spec/features/projects/settings/user_manages_merge_requests_template_spec.rb
- ee/spec/features/projects/show_project_spec.rb
- ee/spec/features/projects/show_spec.rb
- ee/spec/features/projects/view_blob_with_code_owners_spec.rb
- ee/spec/features/projects/wiki/user_views_wiki_empty_spec.rb
- ee/spec/features/projects_spec.rb
- ee/spec/features/protected_tags_spec.rb
- ee/spec/features/remote_development/workspaces_dropdown_group_spec.rb
- ee/spec/features/search/elastic/global_search_spec.rb
- ee/spec/features/search/elastic/project_search_spec.rb
- ee/spec/features/search/elastic/project_search_redactions_work_item_spec.rb
- ee/spec/features/search/elastic/snippet_search_spec.rb
- ee/spec/features/security/dashboard_access_spec.rb
- ee/spec/features/security/profile_access_spec.rb
- ee/spec/features/security/project/discover_spec.rb
- ee/spec/features/security/project/internal_access_spec.rb
- ee/spec/features/security/project/private_access_spec.rb
- ee/spec/features/security/project/public_access_spec.rb
- ee/spec/features/security/project/snippet/internal_access_spec.rb
- ee/spec/features/security/project/snippet/public_access_spec.rb
- ee/spec/features/subscriptions/subscription_flow_for_existing_user_with_eligible_group_spec.rb
- ee/spec/features/user_sees_marketing_header_spec.rb
- ee/spec/features/user_settings/password_spec.rb
- ee/spec/features/users/login_spec.rb
- ee/spec/features/work_items/epics/gfm_autocomplete_spec.rb
- ee/spec/features/work_items/epics/issue_promotion_spec.rb
- ee/spec/features/work_items/epics/referencing_epics_spec.rb
- ee/spec/finders/compliance_management/merge_requests/compliance_violations_finder_spec.rb
- ee/spec/finders/ee/fork_targets_finder_spec.rb
- ee/spec/finders/issues_finder_spec.rb
- ee/spec/finders/security/approval_groups_finder_spec.rb
- ee/spec/frontend/fixtures/search.rb
- ee/spec/graphql/ee/resolvers/board_lists_resolver_spec.rb
- ee/spec/graphql/mutations/security_policy/assign_security_policy_project_spec.rb
- ee/spec/graphql/mutations/security_policy/commit_scan_execution_policy_spec.rb
- ee/spec/graphql/mutations/security_policy/create_security_policy_project_spec.rb
- ee/spec/graphql/mutations/security_policy/unassign_security_policy_project_spec.rb
- ee/spec/helpers/ee/users/callouts_helper_spec.rb
- ee/spec/helpers/projects/security/discover_helper_spec.rb
- ee/spec/helpers/subscriptions_helper_spec.rb
- ee/spec/lib/ee/api/entities/user_with_admin_spec.rb
- ee/spec/lib/ee/gitlab/background_migration/migrate_approver_to_approval_rules_spec.rb
- ee/spec/lib/ee/gitlab/ci/pipeline/chain/validate/external_spec.rb
- ee/spec/lib/ee/gitlab/import_export/project/tree_restorer_spec.rb
- ee/spec/lib/ee/gitlab/issuable_metadata_spec.rb
- ee/spec/lib/gitlab/auth/smartcard/certificate_spec.rb
- ee/spec/lib/gitlab/auth/smartcard/ldap_certificate_spec.rb
- ee/spec/lib/gitlab/background_migration/create_vulnerability_links_spec.rb
- ee/spec/lib/gitlab/geo_spec.rb
- ee/spec/lib/gitlab/graphql/aggregations/security_orchestration_policies/lazy_dast_profile_aggregate_spec.rb
- ee/spec/lib/gitlab/llm/stage_check_spec.rb
- ee/spec/lib/gitlab/llm/chain/tools/embeddings_completion_spec.rb
- ee/spec/lib/gitlab/search/zoekt/client_spec.rb
- ee/spec/lib/gitlab/subscription_portal/clients/graphql_spec.rb
- ee/spec/lib/sidebars/groups/menus/analytics_menu_spec.rb
- ee/spec/migrations/20220831132802_delete_approval_rules_for_vulnerability_spec.rb
- ee/spec/migrations/20220907122648_populate_security_orchestration_policy_configuration_id_spec.rb
- ee/spec/migrations/20221130192239_fix_approval_project_rules_without_protected_branches_spec.rb
- ee/spec/migrations/20230113201308_backfill_namespace_ldap_settings_spec.rb
- ee/spec/migrations/20230127155217_add_id_column_to_package_metadata_join_table_spec.rb
- ee/spec/migrations/20230310213308_sync_security_policy_rule_schedules_that_may_have_been_deleted_by_a_bug_spec.rb
- ee/spec/migrations/20230403221928_resync_scan_result_policies_for_namespaces_spec.rb
- ee/spec/migrations/20230501165244_remove_software_license_policies_without_scan_result_policy_id_spec.rb
- ee/spec/migrations/20230612162643_pm_checkpoints_remove_advisory_entries_spec.rb
- ee/spec/migrations/20231030154117_insert_new_ultimate_trial_plan_into_plans_spec.rb
- ee/spec/migrations/cleanup_orphan_software_licenses_spec.rb
- ee/spec/migrations/geo/migrate_ci_job_artifacts_to_separate_registry_spec.rb
- ee/spec/migrations/geo/migrate_lfs_objects_to_separate_registry_spec.rb
- ee/spec/migrations/geo/resync_direct_upload_job_artifact_registry_spec.rb
- ee/spec/migrations/update_can_create_group_application_setting_spec.rb
- ee/spec/migrations/update_ci_max_total_yaml_size_bytes_default_value_spec.rb
- ee/spec/models/concerns/ee/mentionable_spec.rb
- ee/spec/models/concerns/elastic/note_spec.rb
- ee/spec/models/ee/member_spec.rb
- ee/spec/models/ee/namespace/root_storage_statistics_spec.rb
- ee/spec/models/ee/namespace_spec.rb
- ee/spec/models/ee/namespace_statistics_spec.rb
- ee/spec/models/ee/namespaces/user_namespace_spec.rb
- ee/spec/models/ee/pages_domain_spec.rb
- ee/spec/models/ee/project_member_spec.rb
- ee/spec/models/ee/project_spec.rb
- ee/spec/models/elastic/migration_record_spec.rb
- ee/spec/models/epic_spec.rb
- ee/spec/models/gitlab_subscription_spec.rb
- ee/spec/models/iteration_spec.rb
- ee/spec/models/member_spec.rb
- ee/spec/models/namespace_setting_spec.rb
- ee/spec/models/namespaces/free_user_cap/enforcement_spec.rb
- ee/spec/models/project_member_spec.rb
- ee/spec/models/projects/compliance_standards/adherence_spec.rb
- ee/spec/models/push_rule_spec.rb
- ee/spec/policies/ci/minutes/namespace_monthly_usage_policy_spec.rb
- ee/spec/policies/dependency_proxy/packages/setting_policy_spec.rb
- ee/spec/policies/global_policy_spec.rb
- ee/spec/policies/packages/policies/project_policy_spec.rb
- ee/spec/policies/project_policy_spec.rb
- ee/spec/policies/requirements_management/requirement_policy_spec.rb
- ee/spec/policies/vulnerabilities/feedback_policy_spec.rb
- ee/spec/policies/vulnerabilities/merge_request_link_policy_spec.rb
- ee/spec/presenters/audit_event_presenter_spec.rb
- ee/spec/requests/admin/users_controller_spec.rb
- ee/spec/requests/api/discussions_spec.rb
- ee/spec/requests/api/epics_spec.rb
- ee/spec/requests/api/graphql/ci/minutes/usage_spec.rb
- ee/spec/requests/api/graphql/compliance_management/merge_requests/compliance_violations_spec.rb
- ee/spec/requests/api/graphql/mutations/security/finding/create_merge_request_spec.rb
- ee/spec/requests/api/graphql/mutations/security_policy/assign_security_policy_project_spec.rb
- ee/spec/requests/api/graphql/mutations/security_policy/commit_scan_execution_policy_spec.rb
- ee/spec/requests/api/graphql/mutations/security_policy/create_security_policy_project_spec.rb
- ee/spec/requests/api/graphql/mutations/security_policy/unassign_security_policy_project_spec.rb
- ee/spec/requests/api/graphql/project/path_locks_spec.rb
- ee/spec/requests/api/graphql/project/pipelines/dast_profile_spec.rb
- ee/spec/requests/api/graphql/project/push_rules_spec.rb
- ee/spec/requests/api/graphql/project/requirements_management/requirements_spec.rb
- ee/spec/requests/api/graphql/work_item_spec.rb
- ee/spec/requests/api/groups_spec.rb
- ee/spec/requests/api/integrations_spec.rb
- ee/spec/requests/api/internal/ai/x_ray/scan_spec.rb
- ee/spec/requests/api/internal/base_spec.rb
- ee/spec/requests/api/issues_spec.rb
- ee/spec/requests/api/merge_request_approval_rules_spec.rb
- ee/spec/requests/api/merge_request_approvals_spec.rb
- ee/spec/requests/api/merge_requests_spec.rb
- ee/spec/requests/api/namespaces_spec.rb
- ee/spec/requests/api/project_approval_rules_spec.rb
- ee/spec/requests/api/project_approval_settings_spec.rb
- ee/spec/requests/api/project_approvals_spec.rb
- ee/spec/requests/api/project_milestones_spec.rb
- ee/spec/requests/api/project_push_rule_spec.rb
- ee/spec/requests/api/projects_spec.rb
- ee/spec/requests/api/resource_weight_events_spec.rb
- ee/spec/requests/api/search_spec.rb
- ee/spec/requests/api/users_spec.rb
- ee/spec/requests/custom_roles/regular/admin_merge_request/request_spec.rb
- ee/spec/requests/custom_roles/regular/admin_vulnerability/request_spec.rb
- ee/spec/requests/custom_roles/regular/manage_project_access_tokens/request_spec.rb
- ee/spec/requests/projects/analytics/code_reviews_controller_spec.rb
- ee/spec/requests/projects/issues_controller_spec.rb
- ee/spec/requests/projects/metrics_controller_spec.rb
- ee/spec/requests/projects/logs_controller_spec.rb
- ee/spec/requests/projects/security/policies_controller_spec.rb
- ee/spec/requests/projects/tracing_controller_spec.rb
- ee/spec/requests/gitlab_subscriptions/hand_raise_leads_spec.rb
- ee/spec/requests/trial_registrations_controller_spec.rb
- ee/spec/requests/users_controller_spec.rb
- ee/spec/serializers/clusters/environment_serializer_spec.rb
- ee/spec/services/analytics/cycle_analytics/data_loader_service_spec.rb
- ee/spec/services/ci/minutes/additional_packs/change_namespace_service_spec.rb
- ee/spec/services/ci/minutes/update_project_and_namespace_usage_service_spec.rb
- ee/spec/services/ee/auth/container_registry_authentication_service_spec.rb
- ee/spec/services/ee/commits/create_service_spec.rb
- ee/spec/services/ee/issues/create_service_spec.rb
- ee/spec/services/ee/issues/update_service_spec.rb
- ee/spec/services/ee/notes/create_service_spec.rb
- ee/spec/services/ee/notes/quick_actions_service_spec.rb
- ee/spec/services/ee/notification_service_spec.rb
- ee/spec/services/ee/post_receive_service_spec.rb
- ee/spec/services/ee/projects/remove_paid_features_service_spec.rb
- ee/spec/services/ee/users/destroy_service_spec.rb
- ee/spec/services/ee/users/migrate_records_to_ghost_user_service_spec.rb
- ee/spec/services/epics/issue_promote_service_spec.rb
- ee/spec/services/epics/transfer_service_spec.rb
- ee/spec/services/namespaces/service_accounts/create_service_spec.rb
- ee/spec/services/projects/create_from_template_service_spec.rb
- ee/spec/services/projects/create_service_spec.rb
- ee/spec/services/projects/destroy_service_spec.rb
- ee/spec/services/projects/fork_service_spec.rb
- ee/spec/services/projects/mark_for_deletion_service_spec.rb
- ee/spec/services/projects/restore_service_spec.rb
- ee/spec/services/projects/transfer_service_spec.rb
- ee/spec/services/projects/update_service_spec.rb
- ee/spec/services/quick_actions/interpret_service_spec.rb
- ee/spec/services/todo_service_spec.rb
- ee/spec/services/users/service_accounts/create_service_spec.rb
- ee/spec/services/vulnerability_exports/exporters/csv_service_spec.rb
- ee/spec/services/work_items/legacy_epics/old_epics_update_service_spec.rb
- ee/spec/services/work_items/legacy_epics/issue_promote_service_spec.rb
- ee/spec/tasks/gitlab/elastic_rake_spec.rb
- ee/spec/views/admin/dashboard/index.html.haml_spec.rb
- ee/spec/views/admin/users/show.html.haml_spec.rb
- ee/spec/views/compliance_management/compliance_framework/_project_settings.html.haml_spec.rb
- ee/spec/views/profiles/preferences/show.html.haml_spec.rb
- ee/spec/views/shared/promotions/_promotion_link_project.html.haml_spec.rb
- ee/spec/workers/groups/enterprise_users/associate_worker_spec.rb
- ee/spec/workers/groups/enterprise_users/bulk_associate_by_domain_worker_spec.rb
- spec/controllers/admin/users_controller_spec.rb
- spec/controllers/concerns/checks_collaboration_spec.rb
- spec/controllers/concerns/routable_actions_spec.rb
- spec/controllers/dashboard/snippets_controller_spec.rb
- spec/controllers/dashboard/todos_controller_spec.rb
- spec/controllers/explore/snippets_controller_spec.rb
- spec/controllers/groups/shared_projects_controller_spec.rb
- spec/controllers/import/bitbucket_controller_spec.rb
- spec/controllers/import/bitbucket_server_controller_spec.rb
- spec/controllers/import/gitea_controller_spec.rb
- spec/controllers/import/github_controller_spec.rb
- spec/controllers/oauth/applications_controller_spec.rb
- spec/controllers/oauth/authorizations_controller_spec.rb
- spec/controllers/profiles/notifications_controller_spec.rb
- spec/controllers/profiles/preferences_controller_spec.rb
- spec/controllers/profiles_controller_spec.rb
- spec/controllers/projects/blame_controller_spec.rb
- spec/controllers/projects/blob_controller_spec.rb
- spec/controllers/projects/ci/pipeline_editor_controller_spec.rb
- spec/controllers/projects/commit_controller_spec.rb
- spec/controllers/projects/forks_controller_spec.rb
- spec/controllers/projects/labels_controller_spec.rb
- spec/controllers/projects/merge_requests/drafts_controller_spec.rb
- spec/controllers/projects/milestones_controller_spec.rb
- spec/controllers/projects/project_members_controller_spec.rb
- spec/controllers/projects/snippets_controller_spec.rb
- spec/controllers/projects/tree_controller_spec.rb
- spec/controllers/projects/web_ide_schemas_controller_spec.rb
- spec/controllers/projects/web_ide_terminals_controller_spec.rb
- spec/controllers/projects/wikis_controller_spec.rb
- spec/controllers/projects_controller_spec.rb
- spec/controllers/search_controller_spec.rb
- spec/controllers/snippets/notes_controller_spec.rb
- spec/features/abuse_report_spec.rb
- spec/features/admin/admin_abuse_reports_spec.rb
- spec/features/admin/admin_appearance_spec.rb
- spec/features/admin/admin_disables_two_factor_spec.rb
- spec/features/admin/admin_groups_spec.rb
- spec/features/admin/admin_mode/workers_spec.rb
- spec/features/admin/admin_mode_spec.rb
- spec/features/admin/admin_projects_spec.rb
- spec/features/admin/users/admin_impersonates_user_spec.rb
- spec/features/admin/users/admin_sees_unconfirmed_user_spec.rb
- spec/features/admin/users/admin_sees_user_spec.rb
- spec/features/admin/users/user_spec.rb
- spec/features/admin/users/users_spec.rb
- spec/features/atom/users_spec.rb
- spec/features/boards/boards_spec.rb
- spec/features/breadcrumbs_schema_markup_spec.rb
- spec/features/calendar_spec.rb
- spec/features/canonical_link_spec.rb
- spec/features/commits_spec.rb
- spec/features/dashboard/datetime_on_tooltips_spec.rb
- spec/features/dashboard/issuables_counter_spec.rb
- spec/features/dashboard/issues_filter_spec.rb
- spec/features/dashboard/milestones_spec.rb
- spec/features/dashboard/project_member_activity_index_spec.rb
- spec/features/dashboard/projects_spec.rb
- spec/features/dashboard/user_filters_projects_spec.rb
- spec/features/discussion_comments/merge_request_spec.rb
- spec/features/discussion_comments/snippets_spec.rb
- spec/features/expand_collapse_diffs_spec.rb
- spec/features/explore/catalog/catalog_settings_spec.rb
- spec/features/file_uploads/project_import_spec.rb
- spec/features/file_uploads/user_avatar_spec.rb
- spec/features/frequently_visited_projects_and_groups_spec.rb
- spec/features/global_search_spec.rb
- spec/features/groups/group_settings_spec.rb
- spec/features/groups/participants_autocomplete_spec.rb
- spec/features/groups_spec.rb
- spec/features/help_dropdown_spec.rb
- spec/features/ide/user_opens_merge_request_spec.rb
- spec/features/ide_spec.rb
- spec/features/import/manifest_import_spec.rb
- spec/features/incidents/user_creates_new_incident_spec.rb
- spec/features/incidents/user_views_incident_spec.rb
- spec/features/issuables/issuable_templates_spec.rb
- spec/features/issuables/markdown_references/jira_spec.rb
- spec/features/issuables/shortcuts_issuable_spec.rb
- spec/features/issuables/task_lists_spec.rb
- spec/features/issuables/user_sees_sidebar_spec.rb
- spec/features/issues/confidential_notes_spec.rb
- spec/features/issues/create_issue_for_discussions_in_merge_request_spec.rb
- spec/features/issues/create_issue_for_single_discussion_in_merge_request_spec.rb
- spec/features/issues/design_management/user_links_to_designs_in_issue_spec.rb
- spec/features/issues/design_management/user_views_design_spec.rb
- spec/features/issues/discussion_lock_spec.rb
- spec/features/issues/email_participants_spec.rb
- spec/features/issues/issue_detail_spec.rb
- spec/features/issues/issue_sidebar_spec.rb
- spec/features/issues/list/csv_spec.rb
- spec/features/issues/list/user_sorts_issues_spec.rb
- spec/features/issues/markdown_toolbar_spec.rb
- spec/features/issues/move_spec.rb
- spec/features/issues/new/form_spec.rb
- spec/features/issues/new/user_creates_issue_spec.rb
- spec/features/issues/note_polling_spec.rb
- spec/features/issues/notes_on_issues_spec.rb
- spec/features/issues/related_issues_spec.rb
- spec/features/issues/user_comments_on_issue_spec.rb
- spec/features/issues/user_creates_branch_and_merge_request_spec.rb
- spec/features/issues/user_edits_issue_spec.rb
- spec/features/issues/user_interacts_with_awards_spec.rb
- spec/features/issues/user_resets_their_incoming_email_token_spec.rb
- spec/features/issues/user_toggles_subscription_spec.rb
- spec/features/issues/user_uploads_file_to_note_spec.rb
- spec/features/issues/user_uses_quick_actions_spec.rb
- spec/features/jira_connect/branches_spec.rb
- spec/features/markdown/gitlab_flavored_markdown_spec.rb
- spec/features/markdown/markdown_spec.rb
- spec/features/merge_request/admin_views_hidden_merge_request_spec.rb
- spec/features/merge_request/batch_comments_spec.rb
- spec/features/merge_request/close_reopen_report_toggle_spec.rb
- spec/features/merge_request/hide_default_award_emojis_spec.rb
- spec/features/merge_request/maintainer_edits_fork_spec.rb
- spec/features/merge_request/merge_request_discussion_lock_spec.rb
- spec/features/merge_request/user_accepts_merge_request_spec.rb
- spec/features/merge_request/user_allows_commits_from_memebers_who_can_merge_spec.rb
- spec/features/merge_request/user_approves_spec.rb
- spec/features/merge_request/user_assigns_themselves_reviewer_spec.rb
- spec/features/merge_request/user_assigns_themselves_spec.rb
- spec/features/merge_request/user_closes_reopens_merge_request_state_spec.rb
- spec/features/merge_request/user_comments_on_commit_spec.rb
- spec/features/merge_request/user_comments_on_diff_spec.rb
- spec/features/merge_request/user_comments_on_merge_request_spec.rb
- spec/features/merge_request/user_comments_on_whitespace_hidden_diff_spec.rb
- spec/features/merge_request/user_creates_custom_emoji_spec.rb
- spec/features/merge_request/user_creates_discussion_on_diff_file_spec.rb
- spec/features/merge_request/user_creates_merge_request_spec.rb
- spec/features/merge_request/user_creates_mr_spec.rb
- spec/features/merge_request/user_edits_assignees_sidebar_spec.rb
- spec/features/merge_request/user_edits_merge_request_spec.rb
- spec/features/merge_request/user_edits_mr_spec.rb
- spec/features/merge_request/user_edits_reviewers_sidebar_spec.rb
- spec/features/merge_request/user_locks_discussion_spec.rb
- spec/features/merge_request/user_manages_subscription_spec.rb
- spec/features/merge_request/user_marks_merge_request_as_draft_spec.rb
- spec/features/merge_request/user_opens_checkout_branch_modal_spec.rb
- spec/features/merge_request/user_opens_context_commits_modal_spec.rb
- spec/features/merge_request/user_resolves_diff_notes_and_discussions_resolve_spec.rb
- spec/features/merge_request/user_reverts_merge_request_spec.rb
- spec/features/merge_request/user_sees_check_out_branch_modal_spec.rb
- spec/features/merge_request/user_sees_cherry_pick_modal_spec.rb
- spec/features/merge_request/user_sees_deployment_widget_spec.rb
- spec/features/merge_request/user_sees_diff_spec.rb
- spec/features/merge_request/user_sees_merge_request_file_tree_sidebar_spec.rb
- spec/features/merge_request/user_sees_merge_widget_spec.rb
- spec/features/merge_request/user_sees_pipelines_spec.rb
- spec/features/merge_request/user_selects_branches_for_new_mr_spec.rb
- spec/features/merge_request/user_squashes_merge_request_spec.rb
- spec/features/merge_request/user_suggests_changes_on_diff_spec.rb
- spec/features/merge_request/user_tries_to_access_private_project_info_through_new_mr_spec.rb
- spec/features/merge_request/user_uses_quick_actions_spec.rb
- spec/features/merge_request/user_views_auto_expanding_diff_spec.rb
- spec/features/merge_request/user_views_comment_on_diff_file_spec.rb
- spec/features/merge_request/user_views_diffs_file_by_file_spec.rb
- spec/features/merge_request/user_views_open_merge_request_spec.rb
- spec/features/merge_requests/user_sees_empty_state_spec.rb
- spec/features/monitor_sidebar_link_spec.rb
- spec/features/nav/new_nav_invite_members_spec.rb
- spec/features/nav/pinned_nav_items_spec.rb
- spec/features/oauth_provider_authorize_spec.rb
- spec/features/participants_autocomplete_spec.rb
- spec/features/profile_spec.rb
- spec/features/profiles/account_spec.rb
- spec/features/profiles/active_sessions_spec.rb
- spec/features/profiles/chat_names_spec.rb
- spec/features/profiles/emails_spec.rb
- spec/features/profiles/gpg_keys_spec.rb
- spec/features/profiles/list_users_comment_template_spec.rb
- spec/features/profiles/oauth_applications_spec.rb
- spec/features/profiles/password_spec.rb
- spec/features/profiles/two_factor_auths_spec.rb
- spec/features/profiles/user_changes_notified_of_own_activity_spec.rb
- spec/features/profiles/user_creates_comment_template_spec.rb
- spec/features/profiles/user_deletes_comment_template_spec.rb
- spec/features/profiles/user_edit_preferences_spec.rb
- spec/features/profiles/user_edit_profile_spec.rb
- spec/features/profiles/user_manages_applications_spec.rb
- spec/features/profiles/user_manages_emails_spec.rb
- spec/features/profiles/user_search_settings_spec.rb
- spec/features/profiles/user_updates_comment_template_spec.rb
- spec/features/profiles/user_uses_comment_template_spec.rb
- spec/features/profiles/user_visits_notifications_tab_spec.rb
- spec/features/profiles/user_visits_profile_authentication_log_spec.rb
- spec/features/profiles/user_visits_profile_preferences_page_spec.rb
- spec/features/profiles/user_visits_profile_spec.rb
- spec/features/projects/active_tabs_spec.rb
- spec/features/projects/activity/user_sees_private_activity_spec.rb
- spec/features/projects/blobs/blame_spec.rb
- spec/features/projects/blobs/blob_show_spec.rb
- spec/features/projects/blobs/edit_spec.rb
- spec/features/projects/blobs/user_views_pipeline_editor_button_spec.rb
- spec/features/projects/branches/user_creates_branch_spec.rb
- spec/features/projects/branches_spec.rb
- spec/features/projects/commit/cherry_pick_spec.rb
- spec/features/projects/commit/user_reverts_commit_spec.rb
- spec/features/projects/commits/user_browses_commits_spec.rb
- spec/features/projects/confluence/user_views_confluence_page_spec.rb
- spec/features/projects/environments/environment_spec.rb
- spec/features/projects/environments/environments_spec.rb
- spec/features/projects/feature_flags/user_creates_feature_flag_spec.rb
- spec/features/projects/feature_flags/user_deletes_feature_flag_spec.rb
- spec/features/projects/feature_flags/user_sees_feature_flag_list_spec.rb
- spec/features/projects/feature_flags/user_updates_feature_flag_spec.rb
- spec/features/projects/features_visibility_spec.rb
- spec/features/projects/files/editing_a_file_spec.rb
- spec/features/projects/files/project_owner_creates_license_file_spec.rb
- spec/features/projects/files/user_browses_files_spec.rb
- spec/features/projects/files/user_creates_directory_spec.rb
- spec/features/projects/files/user_creates_files_spec.rb
- spec/features/projects/files/user_deletes_files_spec.rb
- spec/features/projects/files/user_edits_files_spec.rb
- spec/features/projects/files/user_find_file_spec.rb
- spec/features/projects/files/user_reads_pipeline_status_spec.rb
- spec/features/projects/files/user_replaces_files_spec.rb
- spec/features/projects/files/user_searches_for_files_spec.rb
- spec/features/projects/files/user_uploads_files_spec.rb
- spec/features/projects/fork_spec.rb
- spec/features/projects/forks/fork_list_spec.rb
- spec/features/projects/graph_spec.rb
- spec/features/projects/import_export/import_file_spec.rb
- spec/features/projects/jobs/permissions_spec.rb
- spec/features/projects/jobs/user_browses_job_spec.rb
- spec/features/projects/jobs/user_triggers_manual_job_with_variables_spec.rb
- spec/features/projects/jobs_spec.rb
- spec/features/projects/labels/issues_sorted_by_priority_spec.rb
- spec/features/projects/labels/update_prioritization_spec.rb
- spec/features/projects/labels/user_views_labels_spec.rb
- spec/features/projects/members/group_member_cannot_request_access_to_his_group_project_spec.rb
- spec/features/projects/members/group_requester_cannot_request_access_to_project_spec.rb
- spec/features/projects/members/manage_members_spec.rb
- spec/features/projects/members/member_leaves_project_spec.rb
- spec/features/projects/members/sorting_spec.rb
- spec/features/projects/members/tabs_spec.rb
- spec/features/projects/members/user_requests_access_spec.rb
- spec/features/projects/merge_request_button_spec.rb
- spec/features/projects/milestones/milestone_editing_spec.rb
- spec/features/projects/milestones/milestone_showing_spec.rb
- spec/features/projects/milestones/milestone_spec.rb
- spec/features/projects/milestones/milestones_sorting_spec.rb
- spec/features/projects/milestones/new_spec.rb
- spec/features/projects/milestones/user_interacts_with_labels_spec.rb
- spec/features/projects/navbar_spec.rb
- spec/features/projects/network_graph_spec.rb
- spec/features/projects/new_project_from_template_spec.rb
- spec/features/projects/new_project_spec.rb
- spec/features/projects/pipeline_schedules_spec.rb
- spec/features/projects/settings/merge_requests_settings_spec.rb
- spec/features/projects/settings/monitor_settings_spec.rb
- spec/features/projects/settings/registry_settings_cleanup_tags_spec.rb
- spec/features/projects/settings/registry_settings_spec.rb
- spec/features/projects/settings/user_archives_project_spec.rb
- spec/features/projects/settings/user_changes_default_branch_spec.rb
- spec/features/projects/settings/user_manages_merge_requests_settings_spec.rb
- spec/features/projects/settings/user_renames_a_project_spec.rb
- spec/features/projects/settings/user_searches_in_settings_spec.rb
- spec/features/projects/settings/user_tags_project_spec.rb
- spec/features/projects/settings/user_transfers_a_project_spec.rb
- spec/features/projects/settings/visibility_settings_spec.rb
- spec/features/projects/show/clone_button_spec.rb
- spec/features/projects/show/download_buttons_spec.rb
- spec/features/projects/show/no_password_spec.rb
- spec/features/projects/show/redirects_spec.rb
- spec/features/projects/show/rss_spec.rb
- spec/features/projects/show/user_interacts_with_auto_devops_banner_spec.rb
- spec/features/projects/show/user_interacts_with_dropdown_actions_spec.rb
- spec/features/projects/show/user_interacts_with_stars_spec.rb
- spec/features/projects/show/user_sees_collaboration_links_spec.rb
- spec/features/projects/show/user_sees_git_instructions_spec.rb
- spec/features/projects/show/user_sees_setup_shortcut_buttons_spec.rb
- spec/features/projects/show/user_uploads_files_spec.rb
- spec/features/projects/snippets/show_spec.rb
- spec/features/projects/snippets/user_updates_snippet_spec.rb
- spec/features/projects/sourcegraph_csp_spec.rb
- spec/features/projects/tags/user_views_tag_spec.rb
- spec/features/projects/tags/user_views_tags_spec.rb
- spec/features/projects/tree/rss_spec.rb
- spec/features/projects/tree/tree_show_spec.rb
- spec/features/projects/user_creates_project_spec.rb
- spec/features/projects/user_sees_sidebar_spec.rb
- spec/features/projects/user_sees_user_popover_spec.rb
- spec/features/projects/user_uses_shortcuts_spec.rb
- spec/features/projects/wiki/user_views_wiki_empty_spec.rb
- spec/features/projects/wikis_spec.rb
- spec/features/projects_spec.rb
- spec/features/reportable_note/merge_request_spec.rb
- spec/features/search/user_searches_for_code_spec.rb
- spec/features/search/user_searches_for_comments_spec.rb
- spec/features/search/user_searches_for_commits_spec.rb
- spec/features/search/user_searches_for_issues_spec.rb
- spec/features/search/user_searches_for_merge_requests_spec.rb
- spec/features/search/user_searches_for_milestones_spec.rb
- spec/features/search/user_searches_for_wiki_pages_spec.rb
- spec/features/search/user_uses_header_search_field_spec.rb
- spec/features/search/user_uses_search_filters_spec.rb
- spec/features/security/dashboard_access_spec.rb
- spec/features/security/profile_access_spec.rb
- spec/features/security/project/internal_access_spec.rb
- spec/features/security/project/private_access_spec.rb
- spec/features/security/project/public_access_spec.rb
- spec/features/security/project/snippet/internal_access_spec.rb
- spec/features/security/project/snippet/private_access_spec.rb
- spec/features/security/project/snippet/public_access_spec.rb
- spec/features/snippets/notes_on_personal_snippets_spec.rb
- spec/features/tags/developer_views_tags_spec.rb
- spec/features/unsubscribe_links_spec.rb
- spec/features/uploads/user_uploads_avatar_to_profile_spec.rb
- spec/features/user_opens_link_to_comment_spec.rb
- spec/features/user_sees_active_nav_items_spec.rb
- spec/features/user_settings/active_sessions_spec.rb
- spec/features/user_settings/password_spec.rb
- spec/features/user_settings/personal_access_tokens_spec.rb
- spec/features/user_settings/ssh_keys_spec.rb
- spec/features/users/login_spec.rb
- spec/features/users/overview_spec.rb
- spec/features/users/rss_spec.rb
- spec/features/users/show_spec.rb
- spec/features/users/signup_spec.rb
- spec/features/users/snippets_spec.rb
- spec/features/users/user_browses_projects_on_user_page_spec.rb
- spec/features/webauthn_spec.rb
- spec/features/whats_new_spec.rb
- spec/features/work_items/detail/work_item_detail_spec.rb
- spec/finders/admin/projects_finder_spec.rb
- spec/finders/autocomplete/move_to_project_finder_spec.rb
- spec/finders/autocomplete/routes_finder_spec.rb
- spec/finders/events_finder_spec.rb
- spec/finders/fork_targets_finder_spec.rb
- spec/finders/issues_finder_spec.rb
- spec/finders/members_finder_spec.rb
- spec/finders/notes_finder_spec.rb
- spec/finders/packages/npm/package_finder_spec.rb
- spec/finders/personal_projects_finder_spec.rb
- spec/finders/projects/topics_finder_spec.rb
- spec/finders/work_items/work_items_finder_spec.rb
- spec/frontend/fixtures/autocomplete_sources.rb
- spec/frontend/fixtures/issues.rb
- spec/frontend/fixtures/namespaces.rb
- spec/frontend/fixtures/pipeline_details.rb
- spec/frontend/fixtures/snippet.rb
- spec/frontend/fixtures/users.rb
- spec/frontend/fixtures/webauthn.rb
- spec/graphql/mutations/users/set_namespace_commit_email_spec.rb
- spec/graphql/resolvers/board_list_issues_resolver_spec.rb
- spec/graphql/resolvers/board_lists_resolver_spec.rb
- spec/graphql/resolvers/board_resolver_spec.rb
- spec/graphql/resolvers/boards_resolver_spec.rb
- spec/graphql/resolvers/namespace_projects_resolver_spec.rb
- spec/graphql/resolvers/projects/fork_targets_resolver_spec.rb
- spec/graphql/resolvers/recent_boards_resolver_spec.rb
- spec/graphql/types/project_type_spec.rb
- spec/helpers/avatars_helper_spec.rb
- spec/helpers/award_emoji_helper_spec.rb
- spec/helpers/blob_helper_spec.rb
- spec/helpers/boards_helper_spec.rb
- spec/helpers/events_helper_spec.rb
- spec/helpers/gitlab_routing_helper_spec.rb
- spec/helpers/markup_helper_spec.rb
- spec/helpers/notes_helper_spec.rb
- spec/helpers/projects_helper_spec.rb
- spec/helpers/search_helper_spec.rb
- spec/helpers/submodule_helper_spec.rb
- spec/helpers/tree_helper_spec.rb
- spec/lib/banzai/filter/references/user_reference_filter_spec.rb
- spec/lib/banzai/filter/truncate_visible_filter_spec.rb
- spec/lib/banzai/reference_parser/project_parser_spec.rb
- spec/lib/bulk_imports/groups/loaders/group_loader_spec.rb
- spec/lib/constraints/user_url_constrainer_spec.rb
- spec/lib/feature_spec.rb
- spec/lib/generators/gitlab/partitioning/foreign_keys_generator_spec.rb
- spec/lib/gitlab/analytics/usage_trends/workers_argument_builder_spec.rb
- spec/lib/gitlab/background_migration/backfill_user_details_fields_spec.rb
- spec/lib/gitlab/background_migration/job_coordinator_spec.rb
- spec/lib/gitlab/checks/container_moved_spec.rb
- spec/lib/gitlab/checks/project_created_spec.rb
- spec/lib/gitlab/ci/trace/archive_spec.rb
- spec/lib/gitlab/database/decomposition/migrate_spec.rb
- spec/lib/gitlab/database/load_balancing/transaction_leaking_spec.rb
- spec/lib/gitlab/database/load_balancing_spec.rb
- spec/lib/gitlab/database/lock_writes_manager_spec.rb
- spec/lib/gitlab/database/migration_helpers/loose_foreign_key_helpers_spec.rb
- spec/lib/gitlab/database/migration_helpers/v2_spec.rb
- spec/lib/gitlab/database/migration_helpers_spec.rb
- spec/lib/gitlab/database/migrations/observers/transaction_duration_spec.rb
- spec/lib/gitlab/database/migrations/test_batched_background_runner_spec.rb
- spec/lib/gitlab/database/migrations/timeout_helpers_spec.rb
- spec/lib/gitlab/database/partitioning/list/convert_table_spec.rb
- spec/lib/gitlab/database/partitioning/monthly_strategy_spec.rb
- spec/lib/gitlab/database/partitioning/sliding_list_strategy_spec.rb
- spec/lib/gitlab/database/partitioning_migration_helpers/table_management_helpers_spec.rb
- spec/lib/gitlab/database/reflection_spec.rb
- spec/lib/gitlab/database/transaction/observer_spec.rb
- spec/lib/gitlab/database/with_lock_retries_spec.rb
- spec/lib/gitlab/error_tracking/processor/sidekiq_processor_spec.rb
- spec/lib/gitlab/fogbugz_import/project_creator_spec.rb
- spec/lib/gitlab/git_access_project_spec.rb
- spec/lib/gitlab/git_access_spec.rb
- spec/lib/gitlab/gitaly_client/with_feature_flag_actors_spec.rb
- spec/lib/gitlab/import_export/importer_spec.rb
- spec/lib/gitlab/import_export/project/export_task_spec.rb
- spec/lib/gitlab/import_export/project/import_task_spec.rb
- spec/lib/gitlab/import_export/snippet_repo_restorer_spec.rb
- spec/lib/gitlab/import_export/snippet_repo_saver_spec.rb
- spec/lib/gitlab/import_export/snippets_repo_restorer_spec.rb
- spec/lib/gitlab/issuable_metadata_spec.rb
- spec/lib/gitlab/legacy_github_import/project_creator_spec.rb
- spec/lib/gitlab/noteable_metadata_spec.rb
- spec/lib/gitlab/patch/redis_cache_store_spec.rb
- spec/lib/gitlab/project_search_results_spec.rb
- spec/lib/gitlab/project_template_spec.rb
- spec/lib/gitlab/reference_extractor_spec.rb
- spec/lib/gitlab/relative_positioning/mover_spec.rb
- spec/lib/gitlab/sample_data_template_spec.rb
- spec/lib/gitlab/themes_spec.rb
- spec/mailers/emails/service_desk_spec.rb
- spec/mailers/notify_spec.rb
- spec/migrations/20221002234454_finalize_group_member_namespace_id_migration_spec.rb
- spec/migrations/20221018050323_add_objective_and_keyresult_to_work_item_types_spec.rb
- spec/migrations/20221018062308_schedule_backfill_project_namespace_details_spec.rb
- spec/migrations/20221018095434_schedule_disable_legacy_open_source_license_for_projects_less_than_five_mb_spec.rb
- spec/migrations/20221018193635_ensure_task_note_renaming_background_migration_finished_spec.rb
- spec/migrations/20221021145820_create_routing_table_for_builds_metadata_v2_spec.rb
- spec/migrations/20221025043930_change_default_value_on_password_last_changed_at_to_user_details_spec.rb
- spec/migrations/20221028022627_add_index_on_password_last_changed_at_to_user_details_spec.rb
- spec/migrations/20221101032521_add_default_preferred_language_to_application_settings_spec.rb
- spec/migrations/20221101032600_add_text_limit_to_default_preferred_language_on_application_settings_spec.rb
- spec/migrations/20221102090943_create_second_partition_for_builds_metadata_spec.rb
- spec/migrations/20221102231130_finalize_backfill_user_details_fields_spec.rb
- spec/migrations/20221104115712_backfill_project_statistics_storage_size_without_uploads_size_spec.rb
- spec/migrations/20221110152133_delete_orphans_approval_rules_spec.rb
- spec/migrations/20221115173607_ensure_work_item_type_backfill_migration_finished_spec.rb
- spec/migrations/20221122132812_schedule_prune_stale_project_export_jobs_spec.rb
- spec/migrations/20221123133054_queue_reset_status_on_container_repositories_spec.rb
- spec/migrations/20221209110934_update_import_sources_on_application_settings_spec.rb
- spec/migrations/20221209110935_fix_update_import_sources_on_application_settings_spec.rb
- spec/migrations/20221209235940_cleanup_o_auth_access_tokens_with_null_expires_in_spec.rb
- spec/migrations/20221210154044_update_active_billable_users_index_spec.rb
- spec/migrations/20221215151822_schedule_backfill_releases_author_id_spec.rb
- spec/migrations/20221219122320_copy_clickhouse_connection_string_to_encrypted_var_spec.rb
- spec/migrations/20221220131020_bump_default_partition_id_value_for_ci_tables_spec.rb
- spec/migrations/20221221110733_remove_temp_index_for_project_statistics_upload_size_migration_spec.rb
- spec/migrations/20221223123019_delete_queued_jobs_for_vulnerabilities_feedback_migration_spec.rb
- spec/migrations/20221226153252_queue_fix_incoherent_packages_size_on_project_statistics_spec.rb
- spec/migrations/20230116111252_finalize_todo_sanitization_spec.rb
- spec/migrations/20230117114739_clear_duplicate_jobs_cookies_spec.rb
- spec/migrations/20230118144623_schedule_migration_for_remediation_spec.rb
- spec/migrations/20230125195503_queue_backfill_compliance_violations_spec.rb
- spec/migrations/20230130182412_schedule_create_vulnerability_links_migration_spec.rb
- spec/migrations/20230131125844_add_project_id_name_id_version_index_to_installable_npm_packages_spec.rb
- spec/migrations/20230201171450_finalize_backfill_environment_tier_migration_spec.rb
- spec/migrations/20230208125736_schedule_migration_for_links_spec.rb
- spec/migrations/20230209222452_schedule_remove_project_group_link_with_missing_groups_spec.rb
- spec/migrations/20230220102212_swap_columns_ci_build_needs_big_int_conversion_spec.rb
- spec/migrations/20230221093533_add_tmp_partial_index_on_vulnerability_report_types_spec.rb
- spec/migrations/20230223065753_finalize_nullify_creator_id_of_orphaned_projects_spec.rb
- spec/migrations/20230224085743_update_issues_internal_id_scope_spec.rb
- spec/migrations/20230224144233_migrate_evidences_from_raw_metadata_spec.rb
- spec/migrations/20230228142350_add_notifications_work_item_widget_spec.rb
- spec/migrations/20230302811133_re_migrate_redis_slot_keys_spec.rb
- spec/migrations/20230309071242_delete_security_policy_bot_users_spec.rb
- spec/migrations/20230313142631_backfill_ml_candidates_package_id_spec.rb
- spec/migrations/20230313150531_reschedule_migration_for_remediation_spec.rb
- spec/migrations/20230317004428_migrate_daily_redis_hll_events_to_weekly_aggregation_spec.rb
- spec/migrations/20230317162059_add_current_user_todos_work_item_widget_spec.rb
- spec/migrations/20230321170823_backfill_ml_candidates_internal_id_spec.rb
- spec/migrations/20230322085041_remove_user_namespace_records_from_vsa_aggregation_spec.rb
- spec/migrations/20230327123333_backfill_product_analytics_data_collector_host_spec.rb
- spec/migrations/20230328030101_add_secureflag_training_provider_spec.rb
- spec/migrations/20230328100534_truncate_error_tracking_tables_spec.rb
- spec/migrations/20230412141541_reschedule_links_avoiding_duplication_spec.rb
- spec/migrations/20230419105225_remove_phabricator_from_application_settings_spec.rb
- spec/migrations/20230426102200_fix_import_sources_on_application_settings_after_phabricator_removal_spec.rb
- spec/migrations/20230428085332_remove_shimo_zentao_integration_records_spec.rb
- spec/migrations/20230515153600_finalize_back_fill_prepared_at_merge_requests_spec.rb
- spec/migrations/20230522220709_ensure_incident_work_item_type_backfill_is_finished_spec.rb
- spec/migrations/add_namespaces_emails_enabled_column_data_spec.rb
- spec/migrations/add_okr_hierarchy_restrictions_spec.rb
- spec/migrations/add_projects_emails_enabled_column_data_spec.rb
- spec/migrations/cleanup_vulnerability_state_transitions_with_same_from_state_to_state_spec.rb
- spec/migrations/delete_migrate_shared_vulnerability_scanners_spec.rb
- spec/migrations/ensure_commit_user_mentions_note_id_bigint_backfill_is_finished_for_gitlab_dot_com_spec.rb
- spec/migrations/ensure_design_user_mentions_note_id_bigint_backfill_is_finished_for_gitlab_dot_com_spec.rb
- spec/migrations/ensure_epic_user_mentions_bigint_backfill_is_finished_for_gitlab_dot_com_spec.rb
- spec/migrations/ensure_issue_user_mentions_bigint_backfill_is_finished_for_gitlab_dot_com_spec.rb
- spec/migrations/ensure_merge_request_metrics_id_bigint_backfill_is_finished_for_gitlab_dot_com_spec.rb
- spec/migrations/ensure_mr_user_mentions_note_id_bigint_backfill_is_finished_for_gitlab_dot_com_spec.rb
- spec/migrations/ensure_suggestions_note_id_bigint_backfill_is_finished_for_gitlab_dot_com_spec.rb
- spec/migrations/ensure_timelogs_note_id_bigint_backfill_is_finished_for_gitlab_dot_com_spec.rb
- spec/migrations/ensure_unique_debian_packages_spec.rb
- spec/migrations/ensure_vum_bigint_backfill_is_finished_for_gl_dot_com_spec.rb
- spec/migrations/finalize_invalid_member_cleanup_spec.rb
- spec/migrations/finalize_issues_iid_scoping_to_namespace_spec.rb
- spec/migrations/finalize_issues_namespace_id_backfilling_spec.rb
- spec/migrations/insert_daily_invites_trial_plan_limits_spec.rb
- spec/migrations/nullify_last_error_from_project_mirror_data_spec.rb
- spec/migrations/queue_backfill_prepared_at_data_spec.rb
- spec/migrations/queue_backfill_user_details_fields_spec.rb
- spec/migrations/queue_populate_projects_star_count_spec.rb
- spec/migrations/recount_epic_cache_counts_spec.rb
- spec/migrations/recount_epic_cache_counts_v3_spec.rb
- spec/migrations/remove_flowdock_integration_records_spec.rb
- spec/migrations/requeue_backfill_admin_mode_scope_for_personal_access_tokens_spec.rb
- spec/migrations/reschedule_incident_work_item_type_id_backfill_spec.rb
- spec/migrations/reschedule_migrate_shared_vulnerability_scanners_spec.rb
- spec/migrations/schedule_fixing_security_scan_statuses_spec.rb
- spec/migrations/second_recount_epic_cache_counts_spec.rb
- spec/migrations/set_email_confirmation_setting_before_removing_send_user_confirmation_email_column_spec.rb
- spec/migrations/set_email_confirmation_setting_from_send_user_confirmation_email_setting_spec.rb
- spec/migrations/set_email_confirmation_setting_from_soft_email_confirmation_ff_spec.rb
- spec/models/abuse_report_spec.rb
- spec/models/analytics/cycle_analytics/aggregation_spec.rb
- spec/models/analytics/cycle_analytics/issue_stage_event_spec.rb
- spec/models/analytics/cycle_analytics/merge_request_stage_event_spec.rb
- spec/models/application_record_spec.rb
- spec/models/concerns/mentionable_spec.rb
- spec/models/concerns/pg_full_text_searchable_spec.rb
- spec/models/concerns/reset_on_column_errors_spec.rb
- spec/models/concerns/routable_spec.rb
- spec/models/container_repository_spec.rb
- spec/models/deploy_key_spec.rb
- spec/models/design_management/design_spec.rb
- spec/models/event_spec.rb
- spec/models/hooks/system_hook_spec.rb
- spec/models/issue_spec.rb
- spec/models/member_spec.rb
- spec/models/namespace/root_storage_statistics_spec.rb
- spec/models/namespace_setting_spec.rb
- spec/models/namespace_spec.rb
- spec/models/namespace_statistics_spec.rb
- spec/models/note_spec.rb
- spec/models/notification_recipient_spec.rb
- spec/models/project_authorization_spec.rb
- spec/models/project_spec.rb
- spec/models/project_team_spec.rb
- spec/models/project_wiki_spec.rb
- spec/models/review_spec.rb
- spec/models/snippet_spec.rb
- spec/models/user_spec.rb
- spec/policies/ci/build_policy_spec.rb
- spec/policies/design_management/design_policy_spec.rb
- spec/policies/global_policy_spec.rb
- spec/policies/namespace/root_storage_statistics_policy_spec.rb
- spec/policies/note_policy_spec.rb
- spec/policies/packages/policies/project_policy_spec.rb
- spec/policies/project_policy_spec.rb
- spec/presenters/merge_request_presenter_spec.rb
- spec/requests/admin/users_controller_spec.rb
- spec/requests/api/access_requests_spec.rb
- spec/requests/api/alert_management_alerts_spec.rb
- spec/requests/api/boards_spec.rb
- spec/requests/api/clusters/agent_tokens_spec.rb
- spec/requests/api/clusters/agents_spec.rb
- spec/requests/api/commits_spec.rb
- spec/requests/api/discussions_spec.rb
- spec/requests/api/doorkeeper_access_spec.rb
- spec/requests/api/environments_spec.rb
- spec/requests/api/events_spec.rb
- spec/requests/api/files_spec.rb
- spec/requests/api/graphql/boards/board_list_issues_query_spec.rb
- spec/requests/api/graphql/ci/config_spec.rb
- spec/requests/api/graphql/ci/pipeline_schedules_spec.rb
- spec/requests/api/graphql/current_user_query_spec.rb
- spec/requests/api/graphql/merge_request/merge_request_spec.rb
- spec/requests/api/graphql/mutations/award_emojis/add_spec.rb
- spec/requests/api/graphql/mutations/award_emojis/toggle_spec.rb
- spec/requests/api/graphql/namespace/projects_spec.rb
- spec/requests/api/graphql/namespace/root_storage_statistics_spec.rb
- spec/requests/api/graphql/project/fork_targets_spec.rb
- spec/requests/api/graphql/project/merge_request_spec.rb
- spec/requests/api/graphql/project/merge_requests_spec.rb
- spec/requests/api/graphql/project/project_members_spec.rb
- spec/requests/api/graphql/project/recent_issue_boards_query_spec.rb
- spec/requests/api/graphql/users/set_namespace_commit_email_spec.rb
- spec/requests/api/groups_spec.rb
- spec/requests/api/import_bitbucket_server_spec.rb
- spec/requests/api/import_github_spec.rb
- spec/requests/api/integrations_spec.rb
- spec/requests/api/internal/base_spec.rb
- spec/requests/api/invitations_spec.rb
- spec/requests/api/issues/get_project_issues_spec.rb
- spec/requests/api/issues/issues_spec.rb
- spec/requests/api/issues/post_projects_issues_spec.rb
- spec/requests/api/issues/put_projects_issues_spec.rb
- spec/requests/api/keys_spec.rb
- spec/requests/api/labels_spec.rb
- spec/requests/api/maven_packages_spec.rb
- spec/requests/api/merge_request_approvals_spec.rb
- spec/requests/api/merge_requests_spec.rb
- spec/requests/api/namespaces_spec.rb
- spec/requests/api/notes_spec.rb
- spec/requests/api/project_events_spec.rb
- spec/requests/api/project_hooks_spec.rb
- spec/requests/api/project_import_spec.rb
- spec/requests/api/project_milestones_spec.rb
- spec/requests/api/project_packages_spec.rb
- spec/requests/api/projects_spec.rb
- spec/requests/api/protected_tags_spec.rb
- spec/requests/api/resource_access_tokens_spec.rb
- spec/requests/api/resource_label_events_spec.rb
- spec/requests/api/resource_milestone_events_spec.rb
- spec/requests/api/resource_state_events_spec.rb
- spec/requests/api/submodules_spec.rb
- spec/requests/api/task_completion_status_spec.rb
- spec/requests/api/users_preferences_spec.rb
- spec/requests/api/users_spec.rb
- spec/requests/git_http_spec.rb
- spec/requests/ide_controller_spec.rb
- spec/requests/lfs_http_spec.rb
- spec/requests/oauth/applications_controller_spec.rb
- spec/requests/oauth/authorizations_controller_spec.rb
- spec/requests/profiles/comment_templates_controller_spec.rb
- spec/requests/profiles/notifications_controller_spec.rb
- spec/requests/projects/harbor/repositories_controller_spec.rb
- spec/requests/projects/settings/packages_and_registries_controller_spec.rb
- spec/requests/projects/tags_controller_spec.rb
- spec/requests/projects/wikis_controller_spec.rb
- spec/requests/recursive_webhook_detection_spec.rb
- spec/requests/search_controller_spec.rb
- spec/requests/user_settings_spec.rb
- spec/requests/users_controller_spec.rb
- spec/requests/warden_spec.rb
- spec/serializers/admin/abuse_report_details_entity_spec.rb
- spec/serializers/ci/pipeline_entity_spec.rb
- spec/serializers/diff_file_base_entity_spec.rb
- spec/serializers/merge_request_current_user_entity_spec.rb
- spec/services/admin/plan_limits/update_service_spec.rb
- spec/services/admin/set_feature_flag_service_spec.rb
- spec/services/auth/container_registry_authentication_service_spec.rb
- spec/services/award_emojis/add_service_spec.rb
- spec/services/ci/abort_pipelines_service_spec.rb
- spec/services/ci/drop_pipelines_and_disable_schedules_for_user_service_spec.rb
- spec/services/draft_notes/publish_service_spec.rb
- spec/services/environments/schedule_to_delete_review_apps_service_spec.rb
- spec/services/import/bitbucket_server_service_spec.rb
- spec/services/import/fogbugz_service_spec.rb
- spec/services/import/github_service_spec.rb
- spec/services/import/gitlab_projects/create_project_service_spec.rb
- spec/services/issuable/bulk_update_service_spec.rb
- spec/services/issues/create_service_spec.rb
- spec/services/issues/update_service_spec.rb
- spec/services/merge_requests/build_service_spec.rb
- spec/services/merge_requests/create_service_spec.rb
- spec/services/merge_requests/push_options_handler_service_spec.rb
- spec/services/merge_requests/update_service_spec.rb
- spec/services/milestones/promote_service_spec.rb
- spec/services/milestones/transfer_service_spec.rb
- spec/services/namespace_settings/assign_attributes_service_spec.rb
- spec/services/notes/build_service_spec.rb
- spec/services/notes/copy_service_spec.rb
- spec/services/notes/create_service_spec.rb
- spec/services/notes/update_service_spec.rb
- spec/services/notification_service_spec.rb
- spec/services/packages/npm/create_package_service_spec.rb
- spec/services/post_receive_service_spec.rb
- spec/services/preview_markdown_service_spec.rb
- spec/services/projects/create_from_template_service_spec.rb
- spec/services/projects/create_service_spec.rb
- spec/services/projects/destroy_service_spec.rb
- spec/services/projects/download_service_spec.rb
- spec/services/projects/fork_service_spec.rb
- spec/services/projects/move_access_service_spec.rb
- spec/services/projects/move_deploy_keys_projects_service_spec.rb
- spec/services/projects/move_forks_service_spec.rb
- spec/services/projects/move_lfs_objects_projects_service_spec.rb
- spec/services/projects/move_notification_settings_service_spec.rb
- spec/services/projects/move_project_authorizations_service_spec.rb
- spec/services/projects/move_project_group_links_service_spec.rb
- spec/services/projects/move_project_members_service_spec.rb
- spec/services/projects/move_users_star_projects_service_spec.rb
- spec/services/projects/overwrite_project_service_spec.rb
- spec/services/projects/participants_service_spec.rb
- spec/services/projects/transfer_service_spec.rb
- spec/services/projects/update_service_spec.rb
- spec/services/repositories/destroy_service_spec.rb
- spec/services/resource_access_tokens/create_service_spec.rb
- spec/services/snippets/create_service_spec.rb
- spec/services/todo_service_spec.rb
- spec/services/upload_service_spec.rb
- spec/services/users/destroy_service_spec.rb
- spec/services/users/migrate_records_to_ghost_user_service_spec.rb
- spec/services/users/set_namespace_commit_email_service_spec.rb
- spec/services/users/update_service_spec.rb
- spec/services/work_items/update_service_spec.rb
- spec/tasks/gitlab/backup_rake_spec.rb
- spec/tasks/gitlab/check_rake_spec.rb
- spec/views/layouts/header/_new_dropdown.haml_spec.rb
- spec/views/layouts/profile.html.haml_spec.rb
- spec/views/profiles/preferences/show.html.haml_spec.rb
- spec/workers/new_issue_worker_spec.rb
- spec/workers/new_merge_request_worker_spec.rb
