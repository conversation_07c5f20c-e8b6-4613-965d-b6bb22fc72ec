# Allow list for spec/support/finder_collection.rb

# Permanent excludes
# For example:
# FooFinder  # Reason: It uses a memory backend
- Namespaces::BilledUsersFinder # Reason: There is no need to have anything else besides the ids is current structure
- Namespaces::FreeUserCap::UsersFinder # Reason: There is no need to have anything else besides the count
- Groups::EnvironmentScopesFinder # Reason: There is no need to have anything else besides the simple strucutre with the scope name
- Security::RelatedPipelinesFinder # Reason: There is no need to have anything else besides the IDs of pipelines
- Llm::ExtraResourceFinder # Reason: The finder does not deal with DB-backend resource for now.
- WorkItems::Statuses::Finder # Reason: The finder intentionally returns a single model instance.
- Sbom::PathFinder # Reason: we need an [[Sbom::Occurrence]]
- Security::VulnerabilityElasticSearchFinder # Reason: The finder deals with Elasticsearch records and not DB records
- Security::VulnerabilityElasticIdentifierNamesFinder # Reason: The finder deals with Elasticsearch records and not DB records
- Security::VulnerabilityElasticCountBySeverityFinder # Reason: The finder deals with Elasticsearch records and not DB records
- Security::VulnerabilityElasticCountOverTimeFinder # Reason: The finder deals with Elasticsearch records and not DB records
- AuditEvents::CombinedAuditEventFinder # Reason: The finder combines result from 4 different tables and also returns cursor to next page
- Security::AnalyzerGroupStatusFinder # Reason: To give accurate counts, return all analyzer types, even when there is no DB record

# Temporary excludes (aka TODOs)
# For example:
# BarFinder  # See <ISSUE_URL>
- AccessRequestsFinder
- Admin::PlansFinder
- Analytics::CycleAnalytics::StageFinder
- ApplicationsFinder
- Autocomplete::GroupFinder
- Autocomplete::ProjectFinder
- Autocomplete::UsersFinder
- BilledUsersFinder
- Boards::BoardsFinder
- Boards::VisitsFinder
- BranchesFinder
- Ci::AuthJobFinder
- Ci::CommitStatusesFinder
- Ci::DailyBuildGroupReportResultsFinder
- ClusterAncestorsFinder
- Clusters::Agents::Authorizations::CiAccess::Finder
- Clusters::Agents::Authorizations::UserAccess::Finder
- Clusters::KubernetesNamespaceFinder
- ComplianceManagement::MergeRequests::ComplianceViolationsFinder
- ContainerRepositoriesFinder
- ContextCommitsFinder
- Environments::EnvironmentNamesFinder
- Environments::EnvironmentsByDeploymentsFinder
- EventsFinder
- GroupDescendantsFinder
- Groups::ProjectsRequiringAuthorizationsRefresh::OnDirectMembershipFinder
- Groups::ProjectsRequiringAuthorizationsRefresh::OnTransferFinder
- KeysFinder
- LfsPointersFinder
- LicenseTemplateFinder
- MergeRequests::OldestPerCommitFinder
- NotesFinder
- Organizations::OrganizationAssociationCounter
- Packages::BuildInfosFinder
- Packages::Conan::PackageFileFinder
- Packages::Go::ModuleFinder
- Packages::Go::PackageFinder
- Packages::Go::VersionFinder
- Packages::PackageFileFinder
- Packages::PackageFinder
- Packages::Pypi::PackageFinder
- Projects::Integrations::Jira::ByIdsFinder
- Projects::Integrations::Jira::IssuesFinder
- PushRuleFinder
- Releases::EvidencePipelineFinder
- Repositories::BranchNamesFinder
- Repositories::ChangelogTagFinder
- Repositories::TreeFinder
- Security::PipelineVulnerabilitiesFinder
- Security::ScanExecutionPoliciesFinder
- Security::ScanResultPoliciesFinder
- Security::PipelineExecutionPoliciesFinder
- Security::PipelineExecutionSchedulePoliciesFinder
- Security::VulnerabilityManagementPoliciesFinder
- Security::AllPoliciesFinder
- Security::SecurityPoliciesFinder
- Security::SecurityProjectGroupFinder
- SentryIssueFinder
- ServerlessDomainFinder
- TagsFinder
- TemplateFinder
- UploaderFinder
- UserGroupNotificationSettingsFinder
- UserGroupsCounter
- Ai::FeatureSettings::FeatureSettingFinder
- Autocomplete::VulnerabilitiesAutocompleteFinder
- Ai::ModelSelection::Namespaces::FeatureSettingFinder
