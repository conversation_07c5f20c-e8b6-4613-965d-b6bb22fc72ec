{"private": true, "scripts": {"check-dependencies": "scripts/frontend/check_dependencies.sh", "deps:check": "depcruise --config config/dependency_cruiser.js --no-ignore-known", "deps:check:all": "yarn run deps:check .", "clean": "rm -rf public/assets tmp/cache/webpack tmp/cache/vite", "dev-server": "NODE_OPTIONS=\"${NODE_OPTIONS:=--max-old-space-size=5120}\" node scripts/frontend/webpack_dev_server.js", "file-coverage": "scripts/frontend/file_test_coverage.js", "lint-docs": "scripts/lint-doc.sh", "internal:eslint": "eslint --cache --max-warnings 0 --report-unused-disable-directives", "internal:stylelint": "stylelint -q --rd '{ee/,}app/assets/{stylesheets/**/*.{css,scss},builds/tailwind{,_cqs}.css}'", "prejest": "yarn check-dependencies", "build:css": "node scripts/frontend/build_css.mjs", "tailwindcss:build": "node scripts/frontend/tailwindcss.mjs", "tailwindcss:cqs:build": "USE_TAILWIND_CONTAINER_QUERIES=true node scripts/frontend/tailwindcss.mjs", "jest": "jest --config jest.config.js", "jest-debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "jest:contract": "PACT_DO_NOT_TRACK=true jest --config jest.config.contract.js --runInBand", "jest:integration": "jest --config jest.config.integration.js", "jest:scripts": "jest --config jest.config.scripts.js", "jest:eslint": "jest --config jest.config.eslint.js", "jest:quarantine": "grep -r 'quarantine:' spec/frontend ee/spec/frontend", "jest:snapshots": "jest --config jest.config.snapshots.js", "lint:eslint": "node scripts/frontend/eslint.js", "lint:eslint:fix": "node scripts/frontend/eslint.js --fix", "lint:eslint:all": "node scripts/frontend/eslint.js .", "lint:eslint:all:fix": "yarn run lint:eslint:all --fix", "lint:eslint:report": "node scripts/frontend/eslint.js --format html --output-file ./eslint-report.html --no-inline-config .", "lint:eslint:staged": "scripts/frontend/execute-on-staged-files.sh internal:eslint '(js|vue)'", "lint:eslint:staged:fix": "yarn run lint:eslint:staged --fix", "lint:prettier": "yarn run prettier --check '**/*.{graphql,js,vue}'", "lint:prettier:fix": "yarn run prettier --write '**/*.{graphql,js,vue}'", "lint:prettier:staged": "scripts/frontend/execute-on-staged-files.sh prettier '(graphql|js|vue)' --check", "lint:prettier:staged:fix": "scripts/frontend/execute-on-staged-files.sh prettier '(graphql|js|vue)' --write", "lint:stylelint": "stylelint '{ee/,}app/assets/{stylesheets/**/*.{css,scss},builds/tailwind{,_cqs}.css}'", "prelint:stylelint": "yarn run tailwindcss:build", "lint:stylelint:fix": "yarn run lint:stylelint --fix", "lint:stylelint:staged": "scripts/frontend/execute-on-staged-files.sh stylelint '(css|scss)' -q", "lint:stylelint:staged:fix": "yarn run lint:stylelint:staged --fix", "preinstall": "node ./scripts/frontend/preinstall.mjs", "postinstall": "node ./scripts/frontend/postinstall.js", "storybook:install": "yarn --cwd ./storybook install", "storybook:doctor": "yarn --cwd ./storybook doctor", "storybook:dev:test": "yarn --cwd ./storybook test", "storybook:build": "yarn tailwindcss:build && yarn --cwd ./storybook build --quiet", "storybook:start": "./scripts/frontend/start_storybook.sh", "storybook:start:skip-fixtures-update": "./scripts/frontend/start_storybook.sh --skip-fixtures-update", "swagger:validate": "swagger-cli validate", "webpack": "NODE_OPTIONS=\"${NODE_OPTIONS:=--max-old-space-size=5120}\" webpack --config config/webpack.config.js", "webpack-vendor": "NODE_OPTIONS=\"${NODE_OPTIONS:=--max-old-space-size=5120}\" webpack --config config/webpack.vendor.config.js", "webpack-prod": "NODE_OPTIONS=\"${NODE_OPTIONS:=--max-old-space-size=5120}\" NODE_ENV=production webpack --config config/webpack.config.js", "vite-prod": "NODE_OPTIONS=\"${NODE_OPTIONS:=--max-old-space-size=8000}\" NODE_ENV=production vite build"}, "dependencies": {"@apollo/client": "^3.5.10", "@babel/core": "^7.23.7", "@babel/preset-env": "^7.23.7", "@csstools/postcss-global-data": "^2.1.1", "@cubejs-client/core": "^1.0.0", "@floating-ui/dom": "^1.7.4", "@gitlab/application-sdk-browser": "^0.3.4", "@gitlab/at.js": "1.5.7", "@gitlab/cluster-client": "^3.0.0", "@gitlab/duo-ui": "10.15.4", "@gitlab/favicon-overlay": "2.0.0", "@gitlab/fonts": "^1.3.1", "@gitlab/query-language-rust": "0.20.1", "@gitlab/svgs": "3.146.0", "@gitlab/ui": "119.1.0", "@gitlab/vue-router-vue3": "npm:vue-router@4.5.1", "@gitlab/vuex-vue3": "npm:vuex@4.1.0", "@gitlab/web-ide": "^0.0.1-dev-20250818141247", "@gleam-lang/highlight.js-gleam": "^1.5.0", "@mattiasbuelens/web-streams-adapter": "^0.1.0", "@rails/actioncable": "7.1.501", "@rails/ujs": "7.1.501", "@rollup/plugin-graphql": "^2.0.5", "@sentry/browser": "10.5.0", "@snowplow/browser-plugin-client-hints": "^3.24.2", "@snowplow/browser-plugin-form-tracking": "^3.24.2", "@snowplow/browser-plugin-ga-cookies": "^3.24.2", "@snowplow/browser-plugin-link-click-tracking": "^3.24.2", "@snowplow/browser-plugin-performance-timing": "^3.24.2", "@snowplow/browser-plugin-timezone": "^3.24.2", "@snowplow/browser-tracker": "^3.24.2", "@sourcegraph/code-host-integration": "0.0.95", "@tiptap/core": "^2.26.1", "@tiptap/extension-blockquote": "^2.26.1", "@tiptap/extension-bold": "^2.26.1", "@tiptap/extension-bubble-menu": "^2.26.1", "@tiptap/extension-bullet-list": "^2.26.1", "@tiptap/extension-code": "^2.26.1", "@tiptap/extension-code-block": "^2.26.1", "@tiptap/extension-code-block-lowlight": "^2.26.1", "@tiptap/extension-document": "^2.26.1", "@tiptap/extension-dropcursor": "^2.26.1", "@tiptap/extension-gapcursor": "^2.26.1", "@tiptap/extension-hard-break": "^2.26.1", "@tiptap/extension-heading": "^2.26.1", "@tiptap/extension-highlight": "^2.26.1", "@tiptap/extension-history": "^2.26.1", "@tiptap/extension-horizontal-rule": "^2.26.1", "@tiptap/extension-image": "^2.26.1", "@tiptap/extension-italic": "^2.26.1", "@tiptap/extension-link": "^2.26.1", "@tiptap/extension-list-item": "^2.26.1", "@tiptap/extension-ordered-list": "^2.26.1", "@tiptap/extension-paragraph": "^2.26.1", "@tiptap/extension-strike": "^2.26.1", "@tiptap/extension-subscript": "^2.26.1", "@tiptap/extension-superscript": "^2.26.1", "@tiptap/extension-table": "^2.26.1", "@tiptap/extension-table-cell": "^2.26.1", "@tiptap/extension-table-header": "^2.26.1", "@tiptap/extension-table-row": "^2.26.1", "@tiptap/extension-task-item": "^2.26.1", "@tiptap/extension-task-list": "^2.26.1", "@tiptap/extension-text": "^2.26.1", "@tiptap/pm": "^2.26.1", "@tiptap/suggestion": "^2.26.1", "@tiptap/vue-2": "^2.26.1", "@vitejs/plugin-vue2": "^2.3.3", "@vue/apollo-components": "^4.0.0-beta.4", "@vue/apollo-option": "^4.0.0-beta.4", "apollo-upload-client": "15.0.0", "apollo3-cache-persist": "^0.14.1", "autoprefixer": "^10.4.8", "autosize": "^6.0.1", "axios": "^0.24.0", "babel-loader": "^8.4.1", "babel-plugin-lodash": "^3.3.4", "browserslist": "^4.21.3", "cache-loader": "^4.1.0", "canvas-confetti": "^1.4.0", "chokidar": "^3.5.3", "clipboard": "^2.0.8", "colord": "^2.9.3", "commander": "^14.0.0", "compression-webpack-plugin": "^5.0.2", "copy-webpack-plugin": "^6.4.1", "core-js": "^3.41.0", "cron-validator": "^1.1.1", "cronstrue": "^1.122.0", "cropperjs": "^1.6.1", "css-loader": "^2.1.1", "d3": "^5.16.0", "d3-sankey": "^0.12.3", "d3-selection": "^1.2.0", "dateformat": "^5.0.1", "deckar01-task_list": "^2.3.1", "dexie": "^3.2.3", "diff": "^3.4.0", "dompurify": "^3.2.6", "dropzone": "^4.2.0", "editorconfig": "^0.15.3", "emoji-regex": "^10.3.0", "fast-mersenne-twister": "1.0.2", "file-loader": "^6.2.0", "fuzzaldrin-plus": "^0.6.0", "gettext-parser": "^6.0.0", "globby": "^11.0.1", "graphiql": "^3.7.1", "graphql": "16.11.0", "graphql-tag": "^2.11.0", "gridstack": "^12.2.1", "highlight.js": "^11.11.1", "highlightjs-glimmer": "^2.2.2", "highlightjs-veryl": "^0.16.3", "immer": "^9.0.15", "ipaddr.js": "^1.9.1", "jed": "^1.1.1", "jest-worker": "^29.7.0", "jquery": "^3.6.0", "jquery.caret": "^0.3.1", "js-cookie": "^3.0.0", "js-yaml": "^3.13.1", "jszip": "^3.1.3", "katex": "^0.16.19", "leaflet": "^1.9.4", "lodash": "^4.17.20", "lowlight": "^2.9.0", "marked": "^12.0.0", "marked-bidi": "^1.0.3", "mathjax": "3", "mdurl": "^1.0.1", "mermaid": "10.7.0", "micromatch": "^4.0.5", "minimatch": "^3.0.4", "modern-screenshot": "^4.4.39", "monaco-editor": "^0.30.1", "monaco-editor-webpack-plugin": "^6.0.0", "monaco-yaml": "4.0.0", "mousetrap": "1.6.5", "orderedmap": "^2.1.1", "papaparse": "^5.3.1", "patch-package": "6.5.1", "pdfjs-dist": "4.3.136", "pikaday": "^1.8.0", "pinia": "^2.2.2", "popper.js": "^1.16.1", "portal-vue": "^2.1.7", "postcss": "^8.4.35", "postcss-custom-properties": "^13.3.12", "prettier": "^3.3.2", "prosemirror-markdown": "1.13.2", "raphael": "^2.2.7", "raw-loader": "^4.0.2", "react": "^18.3.1", "react-dom": "^18.3.1", "rehype-raw": "^6.1.1", "remark-frontmatter": "^4.0.1", "remark-gfm": "^3.0.1", "remark-parse": "^10.0.2", "remark-rehype": "^10.1.0", "sass": "^1.69.7", "sass-embedded": "^1.89.2", "scrollparent": "^2.0.1", "semver": "^7.3.4", "sortablejs": "^1.10.2", "string-hash": "1.1.3", "style-loader": "^2.0.0", "swagger-client": "^3.26.7", "swagger-ui-dist": "^5.14.0", "thread-loader": "^3.0.4", "three": "^0.143.0", "timeago.js": "^4.0.2", "tippy.js": "^6.3.7", "traverse": "^0.6.7", "unified": "^10.1.2", "unist-builder": "^4.0.0", "unist-util-visit-parents": "5.1.3", "url-loader": "^4.1.1", "uuid": "8.1.0", "visibilityjs": "^1.2.4", "vite": "^7.1.3", "vite-plugin-ruby": "^5.1.1", "vue": "2.7.16", "vue-apollo": "^3.0.7", "vue-loader": "15.11.1", "vue-observe-visibility": "^1.0.0", "vue-resize": "^1.0.1", "vue-router": "3.6.5", "vue-template-compiler": "2.7.16", "vue-virtual-scroll-list": "^1.4.7", "vuedraggable": "^2.23.0", "vuex": "^3.6.2", "web-streams-polyfill": "^3.2.1", "web-vitals": "^0.2.4", "webpack": "^4.47.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^4.10.0", "webpack-stats-plugin": "^0.3.1", "worker-loader": "^3.0.8", "xterm": "3.14.5", "yaml": "^2.0.0-10"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.15.0", "@gitlab/eslint-plugin": "21.2.1", "@gitlab/noop": "^1.0.1", "@gitlab/stylelint-config": "6.2.2", "@graphql-eslint/eslint-plugin": "4.4.0", "@pinia/testing": "^0.1.5", "@testing-library/dom": "^7.16.2", "@types/jest": "^29.5.12", "@types/lodash": "^4.14.197", "@vue/compat": "^3.5.18", "@vue/compiler-sfc": "^3.5.18", "@vue/server-renderer": "^3.5.18", "@vue/test-utils": "1.3.6", "@vue/test-utils-vue3": "npm:@vue/test-utils@^2.4.6", "@vue/vue2-jest": "^28.1.0", "@vue/vue3-jest": "^29.2.3", "ajv": "^8.10.0", "ajv-formats": "^2.1.1", "axios-mock-adapter": "^1.15.0", "babel-jest": "^29.7.0", "babel-plugin-istanbul": "^7.0.0", "chalk": "^2.4.1", "crypto": "^1.0.1", "custom-jquery-matchers": "^2.1.0", "dependency-cruiser": "^16.9.0", "eslint": "9.34.0", "eslint-formatter-gitlab": "^6.0.1", "eslint-import-resolver-jest": "3.0.2", "eslint-import-resolver-webpack": "0.13.10", "eslint-plugin-import": "^2.32.0", "eslint-plugin-local-rules": "^3.0.2", "eslint-plugin-no-jquery": "3.1.1", "eslint-plugin-no-unsanitized": "^4.1.2", "fake-indexeddb": "^4.0.1", "gettext-extractor": "^3.7.0", "gettext-extractor-vue": "^5.1.0", "glob": "^7.1.6", "jest": "^29.7.0", "jest-canvas-mock": "^2.4.0", "jest-diff": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-jasmine2": "^29.7.0", "jest-junit": "^12.3.0", "jest-util": "^29.7.0", "miragejs": "^0.1.40", "mock-apollo-client": "1.2.0", "nodemon": "^2.0.19", "postcss-loader": "^8.1.1", "prettier-plugin-tailwindcss": "^0.6.5", "prosemirror-test-builder": "^1.1.1", "stylelint": "^16.1.0", "swagger-cli": "^4.0.4", "tailwindcss": "^3.4.1", "timezone-mock": "^1.0.8", "vue-loader-vue3": "npm:vue-loader@17.4.2", "vue-test-utils-compat": "0.0.14", "vuex-mock-store": "^0.1.0", "webpack-dev-server": "4.15.2", "xhr-mock": "^2.5.1", "yarn-check-webpack-plugin": "^1.2.0", "yarn-deduplicate": "^6.0.2"}, "resolutions": {"**/glob/jackspeak": "npm:@gitlab/noop@1.0.1", "**/simple-update-notifier/semver": "^7.0.0", "@types/node": "14.17.5", "chokidar": "^3.5.3", "nwsapi": "2.2.2", "tough-cookie": "4.1.3"}, "engines": {"node": ">=12.22.1", "yarn": "^1.10.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}