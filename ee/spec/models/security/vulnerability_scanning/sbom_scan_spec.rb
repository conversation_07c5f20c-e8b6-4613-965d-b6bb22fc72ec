# frozen_string_literal: true

require 'spec_helper'

RSpec.describe Security::VulnerabilityScanning::SbomScan, feature_category: :software_composition_analysis do
  let_it_be(:project) { create(:project) }
  let_it_be(:pipeline) { create(:ci_pipeline, project: project) }
  let_it_be(:job) { create(:ci_build, pipeline: pipeline) }

  let(:sbom_file) do
    fixture_file_upload('ee/spec/fixtures/security/vulnerability_scanning/sbom_scans/sbom.json', 'application/json')
  end

  let(:result_file) do
    fixture_file_upload('ee/spec/fixtures/security/vulnerability_scanning/sbom_scans/findings.json', 'application/json')
  end

  subject(:sbom_scan) { create(:sbom_scan, project: project, build: job) }

  describe 'associations' do
    it { is_expected.to belong_to(:project) }
    it { is_expected.to belong_to(:build).class_name('Ci::Build') }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:project) }
    it { is_expected.to validate_presence_of(:build) }
    it { is_expected.to validate_length_of(:sbom_file_final_path).is_at_most(1024) }
    it { is_expected.to validate_length_of(:error_message).is_at_most(1024) }

    context 'when status is running' do
      before do
        sbom_scan.status = 1
      end

      it 'requires sbom_file', :aggregate_failures do
        expect(sbom_scan).not_to be_valid
        expect(sbom_scan.errors[:sbom_file]).to include("can't be blank")
      end

      it 'does not require result_file' do
        sbom_scan.sbom_file = sbom_file
        expect(sbom_scan).to be_valid
      end
    end

    context 'when status is finished' do
      before do
        sbom_scan.status = 2
      end

      it 'requires both sbom_file and result_file', :aggregate_failures do
        expect(sbom_scan).not_to be_valid
        expect(sbom_scan.errors[:sbom_file]).to include("can't be blank")
        expect(sbom_scan.errors[:result_file]).to include("can't be blank")
      end

      it 'is valid with both files' do
        sbom_scan.sbom_file = sbom_file
        sbom_scan.result_file = result_file
        expect(sbom_scan).to be_valid
      end
    end

    context 'when status is failed' do
      before do
        sbom_scan.status = -1
      end

      it 'does not require sbom_file nor result_file' do
        sbom_scan.sbom_file = sbom_scan.result_file = nil
        expect(sbom_scan).to be_valid
      end
    end
  end

  describe '.expired' do
    let!(:expired_scan) { create(:sbom_scan, :expired, project: project, build: job) }
    let!(:recent_scan) { create(:sbom_scan, project: project, build: job) }

    subject(:scans) { described_class.expired }

    it { is_expected.to contain_exactly(expired_scan) }
  end

  describe 'state machine' do
    around do |example|
      freeze_time { example.run }
    end

    describe 'initial state' do
      it 'has created as initial status' do
        expect(sbom_scan.status_name).to eq(:created)
      end
    end

    context 'when in created state' do
      subject(:sbom_scan) { create(:sbom_scan, :with_sbom_file, project: project, build: job) }

      it 'can transition to running' do
        expect { sbom_scan.start! }.to change { sbom_scan.status_name }.from(:created).to(:running)
      end

      it 'can transition to failed' do
        expect { sbom_scan.failed! }.to change { sbom_scan.status_name }.from(:created).to(:failed)
      end

      it 'cannot transition to finished directly' do
        expect { sbom_scan.finish! }.to raise_error(StateMachines::InvalidTransition)
      end
    end

    context 'when in running state' do
      subject(:sbom_scan) { create(:sbom_scan, :running, :with_sbom_file, project: project, build: job) }

      it 'can transition to finished' do
        sbom_scan.result_file = result_file
        expect { sbom_scan.finish! }.to change { sbom_scan.status_name }.from(:running).to(:finished)
      end

      it 'can transition to failed' do
        expect { sbom_scan.failed! }.to change { sbom_scan.status_name }.from(:running).to(:failed)
      end

      it 'can transition to created' do
        expect { sbom_scan.reset_state! }.to change { sbom_scan.status_name }.from(:running).to(:created)
      end

      it 'cannot transition to running again' do
        expect { sbom_scan.start! }.to raise_error(StateMachines::InvalidTransition)
      end
    end

    context 'when in finished state' do
      subject(:sbom_scan) { create(:sbom_scan, :finished, project: project, build: job) }

      it 'cannot transition to any other state', :aggregate_failures do
        expect { sbom_scan.start! }.to raise_error(StateMachines::InvalidTransition)
        expect { sbom_scan.finish! }.to raise_error(StateMachines::InvalidTransition)
        expect { sbom_scan.failed! }.to raise_error(StateMachines::InvalidTransition)
      end
    end

    context 'when in failed state' do
      subject(:sbom_scan) { create(:sbom_scan, :failed, project: project, build: job) }

      it 'cannot transition to any other state', :aggregate_failures do
        expect { sbom_scan.start! }.to raise_error(StateMachines::InvalidTransition)
        expect { sbom_scan.finish! }.to raise_error(StateMachines::InvalidTransition)
        expect { sbom_scan.failed! }.to raise_error(StateMachines::InvalidTransition)
      end
    end
  end

  describe 'file uploaders' do
    it 'mounts sbom_file uploader' do
      expect(sbom_scan.sbom_file).to be_a(Security::VulnerabilityScanning::SbomScanUploader)
    end

    it 'mounts result_file uploader' do
      expect(sbom_scan.result_file).to be_a(Security::VulnerabilityScanning::SbomScanUploader)
    end
  end

  describe '#fail_with_error_message!' do
    subject(:fail_with_error_message!) { sbom_scan.fail_with_error_message!(message) }

    let(:message) { 'Some error message' }

    it 'sets error_message attribute' do
      fail_with_error_message!
      expect(sbom_scan.error_message).to eq('Some error message')
    end

    it 'marks the scan as failed' do
      fail_with_error_message!
      sbom_scan.reload
      expect(sbom_scan.failed?).to be_truthy
    end

    context 'when message is nil' do
      let(:message) { nil }

      it 'sets error_message to nil' do
        fail_with_error_message!
        expect(sbom_scan.error_message).to be_nil
      end
    end

    context 'when message is over 1024 characters' do
      let(:message) { 'a' * 1025 }

      it 'truncates the message to 1024 characters' do
        fail_with_error_message!
        expect(sbom_scan.error_message.length).to eq(1024)
        expect(sbom_scan.error_message).to end_with('...')
      end
    end
  end

  describe '#delete_files_from_storage' do
    let(:sbom_scan) { create(:sbom_scan, :finished, project: project, build: job) }

    subject(:delete_files) { sbom_scan.delete_files_from_storage }

    context 'when both files are successfully removed' do
      it 'successfully deletes files from storage and returns true', :aggregate_failures do
        sbom_file_path = sbom_scan.sbom_file.file.path
        result_file_path = sbom_scan.result_file.file.path

        expect(File.exist?(sbom_file_path)).to be_truthy
        expect(File.exist?(result_file_path)).to be_truthy

        expect(delete_files).to be true

        expect(File.exist?(sbom_file_path)).to be_falsey
        expect(File.exist?(result_file_path)).to be_falsey
      end
    end

    context 'when file removal fails' do
      let(:error) { StandardError.new('Failed to remove sbom_file') }

      before do
        allow(sbom_scan.sbom_file).to receive(:remove!).and_raise(error)
      end

      it 'returns false and tracks error', :aggregate_failures do
        expect(Gitlab::ErrorTracking).to receive(:track_exception).with(error)
        expect(delete_files).to be false
      end
    end
  end
end
