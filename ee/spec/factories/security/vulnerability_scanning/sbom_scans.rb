# frozen_string_literal: true

FactoryBot.define do
  factory :sbom_scan, class: 'Security::VulnerabilityScanning::SbomScan' do
    project
    build factory: :ci_build
    status { 0 }

    after :build do |sbom_scan|
      sbom_scan.project ||= sbom_scan.build.project

      sbom_scan.build&.valid?
    end

    trait :expired do
      created_at { 3.days.ago }
    end

    # Traits for different states
    trait :created do
      status { 0 }
    end

    trait :running do
      with_sbom_file

      status { 1 }
    end

    trait :finished do
      with_sbom_file
      with_result_file

      status { 2 }
    end

    trait :failed do
      with_sbom_file

      error_message { 'Something went wrong' }
      status { -1 }
    end

    # Trait for with files (useful for testing file uploads)
    trait :with_sbom_file do
      sbom_file do
        fixture_file_upload('ee/spec/fixtures/security/vulnerability_scanning/sbom_scans/sbom.json',
          'application/json')
      end

      # Save file after record is created to have the id value in final file path
      after :create, &:store_sbom_file!
    end

    trait :with_invalid_sbom_file do
      sbom_file do
        fixture_file_upload('ee/spec/fixtures/security/vulnerability_scanning/sbom_scans/invalid_sbom.json',
          'application/json')
      end

      # Save file after record is created to have the id value in final file path
      after :create, &:store_sbom_file!
    end

    trait :with_sbom_file_having_unsupported_cyclonedx_spec do
      sbom_file do
        fixture_file_upload(
          'ee/spec/fixtures/security/vulnerability_scanning/sbom_scans/sbom_with_unsupported_cyclonedx_spec.json',
          'application/json')
      end

      # Save file after record is created to have the id value in final file path
      after :create, &:store_sbom_file!
    end

    trait :with_sbom_missing_ds_taxonomy do
      sbom_file do
        fixture_file_upload('ee/spec/fixtures/security/vulnerability_scanning/sbom_scans/sbom_missing_ds_taxonomy.json',
          'application/json')
      end

      # Save file after record is created to have the id value in final file path
      after :create, &:store_sbom_file!
    end

    trait :with_result_file do
      result_file do
        fixture_file_upload('ee/spec/fixtures/security/vulnerability_scanning/sbom_scans/findings.json',
          'application/json')
      end

      # Save file after record is created to have the id value in final file path
      after :create, &:store_result_file!
    end
  end
end
