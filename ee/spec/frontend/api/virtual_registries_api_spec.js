import MockAdapter from 'axios-mock-adapter';
import * as VirtualRegistry<PERSON>pi from 'ee/api/virtual_registries_api';
import axios from '~/lib/utils/axios_utils';
import { HTTP_STATUS_OK } from '~/lib/utils/http_status';

const dummyApiVersion = 'v3000';
const dummyUrlRoot = '/gitlab';

describe('VirtualRegistriesApi', () => {
  let mock;

  beforeEach(() => {
    mock = new MockAdapter(axios);
    window.gon = {
      api_version: dummyApiVersion,
      relative_url_root: dummyUrlRoot,
    };
    jest.spyOn(axios, 'get');
    jest.spyOn(axios, 'delete');
  });

  afterEach(() => {
    mock.restore();
  });

  describe('updateMavenUpstream', () => {
    it('updates the maven upstream', () => {
      const requestPath = 'virtual_registries/packages/maven/upstreams';
      const upstreamId = 1;
      const expectedUrl = `${dummyUrlRoot}/api/${dummyApiVersion}/${requestPath}/${upstreamId}`;
      const expectedData = {
        id: upstreamId,
        name: 'new name',
        description: 'new description',
      };
      const expectedParams = {
        id: upstreamId,
        data: expectedData,
      };
      const expectResponse = {
        id: upstreamId,
        name: expectedData.name,
        description: expectedData.description,
      };
      mock.onPatch(expectedUrl).reply(HTTP_STATUS_OK, expectResponse);

      return VirtualRegistryApi.updateMavenUpstream(expectedParams).then(({ data }) => {
        expect(data).toEqual(expectResponse);
      });
    });
  });

  describe('updateMavenRegistryUpstreamPosition', () => {
    it('updates the maven upstream registry position', () => {
      const requestPath = 'virtual_registries/packages/maven/registry_upstreams';
      const registryUpstreamId = 1;
      const expectedUrl = `${dummyUrlRoot}/api/${dummyApiVersion}/${requestPath}/${registryUpstreamId}`;
      const expectedParams = {
        id: registryUpstreamId,
        position: 2,
      };
      mock.onPatch(expectedUrl).reply(HTTP_STATUS_OK, 200);

      return VirtualRegistryApi.updateMavenRegistryUpstreamPosition(expectedParams).then(
        ({ data }) => {
          expect(data).toEqual(200);
        },
      );
    });
  });

  describe('deleteMavenUpstream', () => {
    it('deletes the maven upstream', () => {
      const requestPath = 'virtual_registries/packages/maven/upstreams';
      const upstreamId = 1;
      const expectedUrl = `${dummyUrlRoot}/api/${dummyApiVersion}/${requestPath}/${upstreamId}`;
      const expectedParams = {
        id: upstreamId,
      };
      const expectedResponse = {};
      mock.onDelete(expectedUrl).reply(HTTP_STATUS_OK, expectedResponse);

      return VirtualRegistryApi.deleteMavenUpstream(expectedParams).then(({ data }) => {
        expect(data).toEqual(expectedResponse);
      });
    });
  });

  describe('getMavenUpstreamCacheEntries', () => {
    it('fetches the maven upstream cache entries', () => {
      const requestPath = 'virtual_registries/packages/maven/upstreams';
      const upstreamId = '5';
      const expectedUrl = `${dummyUrlRoot}/api/${dummyApiVersion}/${requestPath}/${upstreamId}/cache_entries`;
      const expectedParams = {
        id: upstreamId,
      };
      const expectResponse = [
        {
          id: 'NSAvdGVzdC9iYXI=',
          group_id: 209,
          upstream_id: 5,
          upstream_checked_at: '2025-05-19T14:22:23.048Z',
          file_md5: null,
          file_sha1: '4e1243bd22c66e76c2ba9eddc1f91394e57f9f83',
          size: 15,
          relative_path: '/test/bar',
          upstream_etag: null,
          content_type: 'application/octet-stream',
          created_at: '2025-05-19T14:22:23.050Z',
          updated_at: '2025-05-19T14:22:23.050Z',
        },
      ];
      mock.onGet(expectedUrl).reply(HTTP_STATUS_OK, expectResponse);

      return VirtualRegistryApi.getMavenUpstreamCacheEntries(expectedParams).then(({ data }) => {
        expect(data).toEqual(expectResponse);
      });
    });
  });

  describe('getMavenUpstreamRegistriesList', () => {
    it('fetches the maven upstreams for top-level group', () => {
      const requestPath = 'virtual_registries/packages/maven/upstreams';
      const groupPath = 'full-path';
      const expectedUrl = `${dummyUrlRoot}/api/${dummyApiVersion}/groups/${groupPath}/-/${requestPath}`;
      const expectedParams = {
        id: groupPath,
      };
      const expectResponse = [
        {
          id: 3,
          name: 'test',
          description: '',
          group_id: 122,
          url: 'https://gitlab.com',
          username: '',
          cache_validity_hours: 24,
          metadata_cache_validity_hours: 24,
          created_at: '2025-07-15T04:10:03.060Z',
          updated_at: '2025-07-15T04:11:00.426Z',
        },
      ];
      mock.onGet(expectedUrl).reply(HTTP_STATUS_OK, expectResponse);

      return VirtualRegistryApi.getMavenUpstreamRegistriesList(expectedParams).then(({ data }) => {
        expect(data).toEqual(expectResponse);
      });
    });
  });

  describe('deleteMavenRegistryCache', () => {
    it('deletes upstream cache entry', async () => {
      const requestPath = 'virtual_registries/packages/maven/registries';
      const registryId = '5';
      const expectedUrl = `${dummyUrlRoot}/api/${dummyApiVersion}/${requestPath}/${registryId}/cache`;
      const expectedParams = {
        id: registryId,
      };

      mock.onDelete(expectedUrl).reply(HTTP_STATUS_OK, []);

      const { data } = await VirtualRegistryApi.deleteMavenRegistryCache(expectedParams);

      expect(data).toEqual([]);
      expect(axios.delete).toHaveBeenCalledWith(expectedUrl);
    });
  });

  describe('deleteMavenUpstreamCache', () => {
    it('deletes upstream cache entry', async () => {
      const requestPath = 'virtual_registries/packages/maven/upstreams';
      const upstreamId = '5';
      const expectedUrl = `${dummyUrlRoot}/api/${dummyApiVersion}/${requestPath}/${upstreamId}/cache`;
      const expectedParams = {
        id: upstreamId,
      };

      mock.onDelete(expectedUrl).reply(HTTP_STATUS_OK, []);

      const { data } = await VirtualRegistryApi.deleteMavenUpstreamCache(expectedParams);

      expect(data).toEqual([]);
      expect(axios.delete).toHaveBeenCalledWith(expectedUrl);
    });
  });

  describe('deleteMavenUpstreamCacheEntry', () => {
    it('deletes upstream cache entry', async () => {
      const requestPath = 'virtual_registries/packages/maven/cache_entries';
      const upstreamId = '5';
      const expectedUrl = `${dummyUrlRoot}/api/${dummyApiVersion}/${requestPath}/${upstreamId}`;
      const expectedParams = {
        id: upstreamId,
      };

      mock.onDelete(expectedUrl).reply(HTTP_STATUS_OK, []);

      const { data } = await VirtualRegistryApi.deleteMavenUpstreamCacheEntry(expectedParams);

      expect(data).toEqual([]);
      expect(axios.delete).toHaveBeenCalledWith(expectedUrl);
    });
  });
});
