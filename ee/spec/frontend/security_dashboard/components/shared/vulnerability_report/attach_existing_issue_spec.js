import Vue, { nextTick } from 'vue';
import Vue<PERSON><PERSON>lo from 'vue-apollo';
import RelatedIssuableInput from '~/related_issues/components/related_issuable_input.vue';
import * as Sentry from '~/sentry/sentry_browser_wrapper';
import { shallowMountExtended } from 'helpers/vue_test_utils_helper';
import AttachExistingIssue from 'ee/security_dashboard/components/shared/vulnerability_report/attach_existing_issue.vue';
import createMock<PERSON>pollo from 'helpers/mock_apollo_helper';
import toast from '~/vue_shared/plugins/global_toast';
import createVulnerabilityIssueLinkMutation from 'ee/security_dashboard/graphql/mutations/vulnerability_issue_link_create.mutation.graphql';
import workItemsByReferencesQuery from '~/work_items/graphql/work_items_by_references.query.graphql';
import waitForPromises from 'helpers/wait_for_promises';

const defaultIssueLinkResponse = {
  data: {
    vulnerabilityIssueLinkCreate: { errors: [] },
  },
};
const defaultWorkItemsResponse = {
  data: {
    workItemsByReference: {
      nodes: [
        {
          id: 'gid://gitlab/WorkItem/1',
          iid: '1',
          title: 'Item 1',
          confidential: false,
          webUrl: 'http://127.0.0.1:3000/gitlab-org/gitlab-test/-/work_item/3',
          namespace: {
            id: 'gid://gitlab/Group/1',
            fullPath: 'test-project-path',
            __typename: 'Namespace',
          },
          workItemType: {
            id: 'gid://gitlab/WorkItems::Type/5',
            name: 'Task',
            iconName: 'issue-type-task',
            __typename: 'WorkItemType',
          },
          __typename: 'WorkItem',
        },
        {
          id: 'gid://gitlab/WorkItem/2',
          iid: '2',
          title: 'Item 2',
          confidential: false,
          webUrl: 'http://127.0.0.1:3000/gitlab-org/gitlab-test/-/work_item/4',
          namespace: {
            id: 'gid://gitlab/Group/1',
            fullPath: 'test-project-path',
            __typename: 'Namespace',
          },
          workItemType: {
            id: 'gid://gitlab/WorkItems::Type/5',
            name: 'Task',
            iconName: 'issue-type-task',
            __typename: 'WorkItemType',
          },
          __typename: 'WorkItem',
        },
      ],
    },
  },
};

jest.mock('~/sentry/sentry_browser_wrapper');
jest.mock('~/vue_shared/plugins/global_toast');

Vue.use(VueApollo);

describe('Attach Existing Issue component', () => {
  let wrapper;

  const findForm = () => wrapper.find('form');
  const findRelatedIssuableInput = () => wrapper.findComponent(RelatedIssuableInput);
  const findCancelButton = () => wrapper.findByTestId('cancel-add-to-existing-button');
  const findSubmitButton = () => wrapper.find('[type="submit"]');

  const submitForm = async (untouchedRawReferences = []) => {
    await findRelatedIssuableInput().vm.$emit('addIssuableFormInput', {
      untouchedRawReferences,
      touchedReference: '',
    });

    return findForm().trigger('submit');
  };

  const createComponent = ({
    selectedVulnerabilities = [{ id: 'id_0' }, { id: 'id_1' }],
    apolloProvider,
    vulnerabilitiesQuery,
    vulnerabilitiesCountsQuery,
  } = {}) => {
    wrapper = shallowMountExtended(AttachExistingIssue, {
      apolloProvider,
      propsData: {
        selectedVulnerabilities,
      },
      provide: {
        vulnerabilitiesQuery,
        vulnerabilitiesCountsQuery,
        fullPath: 'gitlab-org/test',
      },
    });
  };

  beforeEach(createComponent);

  it('renders correctly', () => {
    expect(findRelatedIssuableInput().exists()).toBe(true);
    expect(findCancelButton().exists()).toBe(true);
    expect(findSubmitButton().exists()).toBe(true);
    expect(findSubmitButton().attributes('disabled')).toBeUndefined();
    expect(findSubmitButton().classes('js-no-auto-disable')).toBe(true);
  });

  it('clicking cancel emits "cancel"', () => {
    findCancelButton().vm.$emit('click');
    expect(wrapper.emitted('cancel')).toHaveLength(1);
  });

  describe('when "addIssuableFormInput" event is emitted', () => {
    const emitFormInput = (untouchedRawReferences, touchedReference) => {
      findRelatedIssuableInput().vm.$emit('addIssuableFormInput', {
        untouchedRawReferences,
        touchedReference,
      });
      return nextTick();
    };

    it('updates references', async () => {
      await emitFormInput(['#1'], '');
      expect(findRelatedIssuableInput().props('references')).toEqual(['#1']);
    });

    it('removes duplicate references', async () => {
      await emitFormInput(['#1'], '');
      await emitFormInput(['#1'], '');
      expect(findRelatedIssuableInput().props('references')).toEqual(['#1']);
    });

    it('updates value', async () => {
      await emitFormInput([], '#1');
      expect(findRelatedIssuableInput().props('inputValue')).toBe('#1');
    });

    it('adds issue path separator if starts with a number', async () => {
      await emitFormInput([], '1');
      expect(findRelatedIssuableInput().props('inputValue')).toBe('#1');
    });
  });

  describe('when "pendingIssuableRemoveRequest" event is emitted', () => {
    it('removes pending related issue', async () => {
      await findRelatedIssuableInput().vm.$emit('addIssuableFormInput', {
        untouchedRawReferences: ['#1'],
        touchedReference: '',
      });

      expect(findRelatedIssuableInput().props('references')).toHaveLength(1);
      await findRelatedIssuableInput().vm.$emit('pendingIssuableRemoveRequest', 0);
      expect(findRelatedIssuableInput().props('references')).toHaveLength(0);
    });
  });

  describe('form submit', () => {
    const requestHandler = jest.fn().mockResolvedValue(defaultIssueLinkResponse);
    const workItemsRequestHandler = jest.fn().mockResolvedValue(defaultWorkItemsResponse);
    let apolloProvider;

    beforeEach(() => {
      apolloProvider = createMockApollo([
        [createVulnerabilityIssueLinkMutation, requestHandler],
        [workItemsByReferencesQuery, workItemsRequestHandler],
      ]);
      createComponent({ apolloProvider });
    });

    it('does not create issue links or fetch work items when no issues are selected', async () => {
      await submitForm();
      await waitForPromises();
      expect(workItemsRequestHandler).not.toHaveBeenCalled();
      expect(requestHandler).not.toHaveBeenCalled();
    });

    describe('successful issue link API call', () => {
      it('calls the mutation with expected variables', async () => {
        await submitForm(['#1', '#2']);
        await waitForPromises();

        expect(workItemsRequestHandler).toHaveBeenCalledWith({
          refs: ['#1', '#2'],
          contextNamespacePath: 'gitlab-org/test',
        });
        expect(requestHandler).toHaveBeenCalledWith({
          issueId: 'gid://gitlab/WorkItem/1',
          vulnerabilityIds: ['id_0', 'id_1'],
        });
        expect(requestHandler).toHaveBeenCalledWith({
          issueId: 'gid://gitlab/WorkItem/2',
          vulnerabilityIds: ['id_0', 'id_1'],
        });
        expect(wrapper.emitted('vulnerabilities-updated')).toEqual([[['id_0', 'id_1']]]);
        expect(findRelatedIssuableInput().props()).toMatchObject({
          inputValue: '',
          references: [],
        });
      });

      it('emits "clear-rejected" event and "vulnerabilities-updated" event', async () => {
        await submitForm(['#1', '#2']);

        expect(wrapper.emitted('clear-rejected')).toHaveLength(1);
        await waitForPromises();
        expect(wrapper.emitted('vulnerabilities-updated')).toEqual([[['id_0', 'id_1']]]);
      });

      it('sets loading state for submit button', async () => {
        await submitForm(['#1', '#2']);

        expect(findSubmitButton().props('loading')).toBe(true);
        await waitForPromises();
        expect(findSubmitButton().props('loading')).toBe(false);
      });
    });

    describe('failed issue link API call', () => {
      const failedRequestHandler = jest.fn().mockResolvedValue({
        data: {
          vulnerabilityIssueLinkCreate: { errors: ['Error message'] },
        },
      });
      beforeEach(() => {
        apolloProvider = createMockApollo([
          [createVulnerabilityIssueLinkMutation, failedRequestHandler],
          [workItemsByReferencesQuery, workItemsRequestHandler],
        ]);
        createComponent({ apolloProvider });
      });

      it('sends an error to Sentry', async () => {
        expect(Sentry.captureException).not.toHaveBeenCalled();

        await submitForm(['#1']);
        await waitForPromises();

        expect(Sentry.captureException.mock.calls).toMatchObject([[['Error message']]]);
        expect(wrapper.emitted('update-rejected')).toMatchObject([
          [[{ id: 'id_0' }, { id: 'id_1' }]],
        ]);
      });
    });

    describe('failed work items API call', () => {
      const error = new Error('Error');
      const failedWorkItemsRequestHandler = jest.fn().mockRejectedValue(error);
      beforeEach(() => {
        apolloProvider = createMockApollo([
          [workItemsByReferencesQuery, failedWorkItemsRequestHandler],
        ]);
        createComponent({ apolloProvider });
      });

      it('sends an error to Sentry', async () => {
        expect(Sentry.captureException).not.toHaveBeenCalled();

        await submitForm(['#1']);
        await waitForPromises();

        expect(Sentry.captureException.mock.calls[0][0].networkError).toBe(error);
        expect(wrapper.emitted('update-rejected')).toMatchObject([
          [[{ id: 'id_0' }, { id: 'id_1' }]],
        ]);
      });
    });
  });

  describe.each`
    selectedVulnerabilities             | issues          | message
    ${[{ id: 'id_0' }]}                 | ${['#1']}       | ${'1 vulnerability attached to 1 issue'}
    ${[{ id: 'id_0' }]}                 | ${['#1', '#2']} | ${'1 vulnerability attached to 2 issues'}
    ${[{ id: 'id_0' }, { id: 'id_1' }]} | ${['#1']}       | ${'2 vulnerabilities attached to 1 issue'}
    ${[{ id: 'id_0' }, { id: 'id_1' }]} | ${['#1', '#2']} | ${'2 vulnerabilities attached to 2 issues'}
  `('toast message', ({ selectedVulnerabilities, issues, message }) => {
    beforeEach(() => {
      const requestHandler = jest.fn().mockResolvedValue(defaultIssueLinkResponse);
      const workItemsRequestHandler = jest.fn().mockResolvedValue({
        data: {
          workItemsByReference: {
            nodes: defaultWorkItemsResponse.data.workItemsByReference.nodes.slice(0, issues.length),
          },
        },
      });

      const apolloProvider = createMockApollo([
        [createVulnerabilityIssueLinkMutation, requestHandler],
        [workItemsByReferencesQuery, workItemsRequestHandler],
      ]);
      createComponent({ selectedVulnerabilities, apolloProvider });
    });

    it(`shows correct toast message for ${selectedVulnerabilities.length} vulnerabilities and ${issues.length} issues`, async () => {
      await submitForm(issues);
      await waitForPromises();

      expect(toast).toHaveBeenCalledWith(message);
    });
  });
});
