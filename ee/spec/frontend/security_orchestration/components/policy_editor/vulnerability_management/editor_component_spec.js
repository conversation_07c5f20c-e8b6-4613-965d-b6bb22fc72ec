import { nextTick } from 'vue';
import { GlAlert, GlSprintf } from '@gitlab/ui';
import waitForPromises from 'helpers/wait_for_promises';
import { shallowMountExtended } from 'helpers/vue_test_utils_helper';
import { createMockDirective, getBinding } from 'helpers/vue_mock_directive';
import { DEFAULT_ASSIGNED_POLICY_PROJECT } from 'ee/security_orchestration/constants';
import EditorComponent from 'ee/security_orchestration/components/policy_editor/vulnerability_management/editor_component.vue';
import EditorLayout from 'ee/security_orchestration/components/policy_editor/editor_layout.vue';
import RuleSection from 'ee/security_orchestration/components/policy_editor/vulnerability_management/rule/rule_section.vue';
import ActionSection from 'ee/security_orchestration/components/policy_editor/vulnerability_management/action/action_section.vue';
import {
  DEFAULT_VULNERABILITY_MANAGEMENT_POLICY,
  NO_LONGER_DETECTED_RULE_TYPE,
} from 'ee/security_orchestration/components/policy_editor/vulnerability_management/constants';

import { SECURITY_POLICY_ACTIONS } from 'ee/security_orchestration/components/policy_editor/constants';
import { ASSIGNED_POLICY_PROJECT } from 'ee_jest/security_orchestration/mocks/mock_data';
import {
  mockVulnerabilityManagementManifest,
  mockVulnerabilityManagementObject,
  mockVulnerabilityManagementInvalidObject,
  mockVulnerabilityManagementManifestUpdated,
} from 'ee_jest/security_orchestration/mocks/mock_vulnerability_management_policy_data';
import {
  policyBodyToYaml,
  removeIdsFromPolicy,
} from 'ee/security_orchestration/components/policy_editor/utils';
import { fromYaml } from 'ee/security_orchestration/components/utils';
import { POLICY_TYPE_COMPONENT_OPTIONS } from 'ee/security_orchestration/components/constants';

describe('EditorComponent', () => {
  let wrapper;
  const defaultProjectPath = 'path/to/project';

  const factory = ({ propsData = {}, provide = {} } = {}) => {
    wrapper = shallowMountExtended(EditorComponent, {
      directives: {
        GlTooltip: createMockDirective('gl-tooltip'),
      },
      propsData: {
        assignedPolicyProject: DEFAULT_ASSIGNED_POLICY_PROJECT,
        selectedPolicyType: POLICY_TYPE_COMPONENT_OPTIONS.vulnerabilityManagement.urlParameter,
        isCreating: false,
        isDeleting: false,
        isEditing: false,
        ...propsData,
      },
      provide: {
        namespacePath: defaultProjectPath,
        ...provide,
      },
      stubs: {
        GlSprintf,
      },
    });
  };

  const factoryWithExistingPolicy = ({ policy = {} } = {}) => {
    return factory({
      propsData: {
        assignedPolicyProject: ASSIGNED_POLICY_PROJECT,
        existingPolicy: { ...mockVulnerabilityManagementObject, ...policy },
        isEditing: true,
      },
    });
  };

  const findPolicyEditorLayout = () => wrapper.findComponent(EditorLayout);
  const findRuleSection = () => wrapper.findComponent(RuleSection);
  const findLimitAlert = () => wrapper.findComponent(GlAlert);
  const findAllRuleSections = () => wrapper.findAllComponents(RuleSection);
  const findAddRuleButton = () => wrapper.findByTestId('add-rule');
  const findTooltip = () =>
    getBinding(wrapper.findByTestId('add-rule-wrapper').element, 'gl-tooltip');
  const findActionSection = () => wrapper.findComponent(ActionSection);
  const findDisabledAction = () => wrapper.findByTestId('disabled-action');
  const findDisabledRule = () => wrapper.findByTestId('disabled-rule');

  beforeEach(() => {
    factory();
  });

  describe('rule mode', () => {
    const errorAction =
      'The current YAML syntax is invalid so you cannot edit the actions in rule mode. To resolve the issue, switch to YAML mode and fix the syntax.';

    const errorRules =
      'The current YAML syntax is invalid so you cannot edit the rules in rule mode. To resolve the issue, switch to YAML mode and fix the syntax.';

    it('renders the editor', () => {
      expect(findPolicyEditorLayout().exists()).toBe(true);
      expect(findActionSection().exists()).toBe(true);
      expect(findDisabledAction().props()).toEqual({ disabled: false, error: errorAction });
      expect(findDisabledRule().props()).toEqual({ disabled: false, error: errorRules });
    });

    it('disables the corresponding section if there is a validation error', () => {
      factoryWithExistingPolicy({ policy: mockVulnerabilityManagementInvalidObject });
      expect(findActionSection().exists()).toBe(true);
      expect(findDisabledAction().props()).toEqual({ disabled: true, error: errorAction });
      expect(findDisabledRule().props()).toEqual({ disabled: true, error: errorRules });
    });

    it('renders the default policy editor layout', () => {
      expect(findPolicyEditorLayout().props()).toMatchObject({
        yamlEditorValue: DEFAULT_VULNERABILITY_MANAGEMENT_POLICY,
        isEditing: false,
        isRemovingPolicy: false,
        isUpdatingPolicy: false,
      });
    });

    it('updates the general policy properties', async () => {
      const name = 'New name';
      const editorLayout = findPolicyEditorLayout();
      expect(editorLayout.props('policy').name).toBe('');
      expect(editorLayout.props('yamlEditorValue')).toContain("name: ''");
      await editorLayout.vm.$emit('update-property', 'name', name);
      expect(editorLayout.props('policy').name).toBe(name);
      expect(editorLayout.props('yamlEditorValue')).toContain(`name: ${name}`);
    });

    describe('rule section', () => {
      const defaultRule = {
        type: 'no_longer_detected',
        scanners: [],
        severity_levels: [],
      };

      it('passes the correct props', () => {
        const ruleSection = findRuleSection();
        expect(ruleSection.props('index')).toBe(0);
        expect(ruleSection.props('rule')).toEqual({
          ...defaultRule,
          id: ruleSection.props('rule').id,
        });
      });

      it('adds a rule when clicking button', async () => {
        findAddRuleButton().vm.$emit('click');
        await nextTick();
        expect(findAllRuleSections()).toHaveLength(2);
      });

      it('shows correct label for add rule button', () => {
        expect(findAddRuleButton().text()).toBe('Add new rule');
        expect(findAddRuleButton().props('disabled')).toBe(false);
        expect(findTooltip().value.disabled).toBe(true);
      });

      it('disables add button when the limit of 5 rules has been reached', () => {
        const limit = 5;
        const { id, ...rule } = mockVulnerabilityManagementObject.rules[0];
        factoryWithExistingPolicy({ policy: { rules: [rule, rule, rule, rule, rule] } });
        expect(findAllRuleSections()).toHaveLength(limit);
        expect(findAddRuleButton().props('disabled')).toBe(true);
        expect(findTooltip().value).toMatchObject({
          disabled: false,
          title: 'You can add a maximum of 5 rules.',
        });
      });

      it('removes rule when "remove" event is emitted', async () => {
        findAddRuleButton().vm.$emit('click');
        findRuleSection().vm.$emit('remove');
        await nextTick();
        expect(findAllRuleSections()).toHaveLength(1);
      });

      it('updates policy rule when "changed" event is emitted', async () => {
        const ruleSection = findRuleSection();
        const adaptedRule = {
          ...defaultRule,
          severity_levels: ['high'],
          id: ruleSection.props('rule').id,
        };

        ruleSection.vm.$emit('changed', adaptedRule);
        await nextTick();

        expect(ruleSection.props('rule')).toEqual(adaptedRule);
        expect(findPolicyEditorLayout().props('yamlEditorValue')).toBe(
          mockVulnerabilityManagementManifestUpdated,
        );
      });

      it('shows auto-resolve limit message', () => {
        expect(findLimitAlert().text()).toMatchInterpolatedText(
          'In each pipeline, a maximum of 1000 vulnerabilities that are no longer detected will be set to status Resolved until all have been auto-resolved.',
        );
      });
    });

    describe('action section', () => {
      it('passes correct props', () => {
        expect(findActionSection().props('actions')).toEqual([
          { type: 'auto_resolve', id: findActionSection().props('actions')[0].id },
        ]);
      });
    });
  });

  describe('yaml mode', () => {
    it('updates the policy', async () => {
      await findPolicyEditorLayout().vm.$emit('update-yaml', mockVulnerabilityManagementManifest);
      expect(findPolicyEditorLayout().props('policy')).toMatchObject(
        removeIdsFromPolicy(mockVulnerabilityManagementObject),
      );
      expect(findPolicyEditorLayout().props('yamlEditorValue')).toBe(
        mockVulnerabilityManagementManifest,
      );
    });
  });

  describe('modifying a policy', () => {
    it.each`
      status                           | action                            | event              | factoryFn                    | yamlEditorValue
      ${'creating a new policy'}       | ${undefined}                      | ${'save-policy'}   | ${factory}                   | ${policyBodyToYaml(fromYaml({ manifest: DEFAULT_VULNERABILITY_MANAGEMENT_POLICY, type: POLICY_TYPE_COMPONENT_OPTIONS.vulnerabilityManagement.urlParameter }))}
      ${'updating an existing policy'} | ${undefined}                      | ${'save-policy'}   | ${factoryWithExistingPolicy} | ${mockVulnerabilityManagementManifest}
      ${'deleting an existing policy'} | ${SECURITY_POLICY_ACTIONS.REMOVE} | ${'remove-policy'} | ${factoryWithExistingPolicy} | ${mockVulnerabilityManagementManifest}
    `('emits "save" when $status', async ({ action, event, factoryFn, yamlEditorValue }) => {
      factoryFn();
      findPolicyEditorLayout().vm.$emit(event);
      await waitForPromises();
      expect(wrapper.emitted('save')).toEqual([[{ action, policy: yamlEditorValue }]]);
    });
  });

  describe('new yaml format with type as a wrapper', () => {
    beforeEach(() => {
      factory();
    });

    it('renders default yaml in new format', () => {
      expect(findPolicyEditorLayout().props('yamlEditorValue')).toBe(
        DEFAULT_VULNERABILITY_MANAGEMENT_POLICY,
      );
    });

    it('converts new policy format to old policy format when saved', async () => {
      findPolicyEditorLayout().vm.$emit('save-policy');
      await waitForPromises();

      expect(wrapper.emitted('save')).toEqual([
        [
          {
            action: undefined,
            policy: `name: ''
description: ''
enabled: true
rules:
  - type: ${NO_LONGER_DETECTED_RULE_TYPE}
    scanners: []
    severity_levels: []
actions:
  - type: auto_resolve
type: vulnerability_management_policy
`,
          },
        ],
      ]);
    });
  });

  describe('advanced editor', () => {
    it('enables advanced editor mode', () => {
      factory({
        propsData: {
          advancedEditorEnabled: true,
        },
      });

      expect(findPolicyEditorLayout().props('advancedEditorEnabled')).toBe(true);
    });
  });
});
