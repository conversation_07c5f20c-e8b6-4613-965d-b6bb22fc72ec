# frozen_string_literal: true

require 'spec_helper'

RSpec.describe Security::VulnerabilityScanning::DestroySbomScansService, feature_category: :software_composition_analysis do
  let_it_be(:project) { create(:project) }
  let_it_be(:job) { create(:ci_build, project: project) }

  describe '#execute' do
    subject(:execute_service) { described_class.new(sbom_scans).execute }

    context 'when all file deletions succeed' do
      let(:sbom_scans) { create_list(:sbom_scan, 2, :finished, project: project, build: job) }

      it 'returns success response with correct count of destroyed scans', :aggregate_failures do
        expect(execute_service).to be_success
        expect(execute_service.payload[:destroyed_sbom_scans_count]).to eq(2)
      end

      it 'deletes all SBOM scan records from database' do
        scan_ids = sbom_scans.map(&:id)

        execute_service

        expect(Security::VulnerabilityScanning::SbomScan.where(id: scan_ids)).to be_empty
      end

      it 'successfully deletes files from storage', :aggregate_failures do
        files = []
        sbom_scans.each do |sbom_scan|
          files << sbom_scan.sbom_file.file.path
          files << sbom_scan.result_file.file.path
        end

        expect(files.all? { |file| File.exist?(file) }).to be_truthy

        execute_service

        expect(files.any? { |file| File.exist?(file) }).to be_falsey
      end
    end

    context 'when some file deletions fail' do
      let(:sbom_scans) { create_list(:sbom_scan, 3, :finished, project: project, build: job) }

      before do
        # First two scans succeed, last fail
        allow(sbom_scans[0]).to receive(:delete_files_from_storage).and_return(true)
        allow(sbom_scans[1]).to receive(:delete_files_from_storage).and_return(true)
        allow(sbom_scans[2]).to receive(:delete_files_from_storage).and_return(false)
      end

      it 'returns success response with correct count of destroyed scans', :aggregate_failures do
        expect(execute_service).to be_success
        expect(execute_service.payload[:destroyed_sbom_scans_count]).to eq(2)
      end

      it 'only deletes database records for scans with successful file deletion', :aggregate_failures do
        successful_scan_ids = [sbom_scans[0].id, sbom_scans[1].id]
        failed_scan_ids = [sbom_scans[2].id]

        execute_service

        expect(Security::VulnerabilityScanning::SbomScan.where(id: successful_scan_ids)).to be_empty
        expect(Security::VulnerabilityScanning::SbomScan.where(id: failed_scan_ids).count).to eq(1)
      end
    end

    context 'when delete_files_from_storage raises an exception' do
      let(:sbom_scans) { create_list(:sbom_scan, 2, :finished, project: project, build: job) }

      before do
        allow(sbom_scans.first).to receive(:delete_files_from_storage)
          .and_raise(StandardError, 'File deletion failed')
      end

      it 'returns error response', :aggregate_failures do
        response = execute_service

        expect(response).to be_error
        expect(response.message).to eq('File deletion failed')
      end

      it 'does not delete any database records' do
        scan_ids = sbom_scans.map(&:id)

        execute_service

        expect(Security::VulnerabilityScanning::SbomScan.where(id: scan_ids).count).to eq(2)
      end
    end

    context 'when database deletion fails' do
      let(:sbom_scans) { create_list(:sbom_scan, 2, :finished, project: project, build: job) }

      before do
        sbom_scans.each do |scan|
          allow(scan).to receive(:delete_files_from_storage).and_return(true)
        end

        allow(Security::VulnerabilityScanning::SbomScan).to receive(:id_in)
          .and_raise(ActiveRecord::StatementInvalid, 'Database error')
      end

      it 'returns error response', :aggregate_failures do
        response = execute_service

        expect(response).to be_error
        expect(response.message).to eq('Database error')
      end
    end
  end
end
