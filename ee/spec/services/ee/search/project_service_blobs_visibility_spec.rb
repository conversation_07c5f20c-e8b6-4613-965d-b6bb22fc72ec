# frozen_string_literal: true

require 'spec_helper'

RSpec.describe Search::ProjectService, '#visibility', feature_category: :global_search do
  include SearchResultHelpers
  include ProjectHelpers
  include UserHelpers

  describe 'visibility', :sidekiq_inline do
    using RSpec::Parameterized::TableSyntax
    include_context 'ProjectPolicyTable context'

    let_it_be(:group) { create(:group) }
    let_it_be_with_reload(:project) { create(:project, :repository, namespace: group) }

    let(:user) { create_user_from_membership(project, membership) }

    let(:projects) { [project] }
    let(:search_level) { project }

    let(:scope) { 'blobs' }
    let(:search) { '.gitmodules' }

    context 'for blobs' do
      where(:project_level, :feature_access_level, :membership, :admin_mode, :expected_count) do
        permission_table_for_guest_feature_access_and_non_private_project_only
      end

      with_them do
        context 'when using advanced search', :elastic_delete_by_query do
          before do
            stub_ee_application_setting(elasticsearch_search: true, elasticsearch_indexing: true)
            project.repository.index_commits_and_blobs
          end

          it_behaves_like 'search respects visibility'
        end

        context 'when using zoekt', :zoekt_settings_enabled, :zoekt_cache_disabled do
          before do
            zoekt_ensure_namespace_indexed!(group)
          end

          it_behaves_like 'search respects visibility'
        end
      end
    end
  end
end
