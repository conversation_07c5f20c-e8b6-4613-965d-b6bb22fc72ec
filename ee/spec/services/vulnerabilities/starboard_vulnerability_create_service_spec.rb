# frozen_string_literal: true

require 'spec_helper'

RSpec.describe Vulnerabilities::StarboardVulnerabilityCreateService, feature_category: :vulnerability_management do
  let(:agent) { create(:cluster_agent) }
  let(:project) { agent.project }
  let(:user) { agent.created_by_user }

  let_it_be(:params) do
    {
      vulnerability: {
        name: 'CVE-123-4567 in libc',
        description: 'Vulnerability description',
        severity: 'high',
        location: {
          image: 'alpine:latest',
          dependency: {
            version: '0.1.0',
            package: {
              name: 'libc'
            }
          },
          kubernetes_resource: {
            namespace: 'production',
            kind: 'deployment',
            name: 'nginx',
            container: 'nginx'
          }
        },
        identifiers: [
          {
            type: 'cve',
            name: 'CVE-123-4567',
            value: 'CVE-123-4567'
          }
        ]
      },
      scanner: {
        id: 'starboard_trivy',
        name: 'Trivy (via Starboard Operator)',
        vendor: {
          name: 'Git<PERSON>ab'
        }
      }
    }
  end

  let(:service_object) { described_class.new(agent, params: params) }

  subject(:create_vulnerability) { service_object.execute }

  context 'with authorized user' do
    before do
      project.add_maintainer(user)
    end

    context 'with feature enabled' do
      let(:vulnerability) { create_vulnerability.payload[:vulnerability] }

      before do
        stub_licensed_features(security_dashboard: true)
      end

      it 'creates Vulnerability' do
        expect { create_vulnerability }.to change { Vulnerability.count }.by(1)
      end

      it 'has correct data' do
        expect(vulnerability.report_type).to eq("cluster_image_scanning")
        expect(vulnerability.title).to eq(params.dig(:vulnerability, :name))

        finding = vulnerability.finding
        expect(vulnerability.finding_id).to eq(finding.id)
        expect(finding.description).to eq(params.dig(:vulnerability, :description))
        expect(finding.severity).to eq(params.dig(:vulnerability, :severity))
        expect(finding.location['image']).to eq('alpine:latest')
        expect(finding.location.dig('dependency', 'package', 'name')).to eq('libc')
        expect(finding.location.dig('dependency', 'version')).to eq('0.1.0')
        expect(finding.location['kubernetes_resource']).to eq(
          {
            'namespace' => 'production',
            'kind' => 'deployment',
            'name' => 'nginx',
            'container' => 'nginx'
          }
        )

        expect(finding.metadata['location']).to eq(finding.location)

        scanner = finding.scanner
        expect(scanner.external_id).to eq(params.dig(:scanner, :id))
        expect(scanner.name).to eq(params.dig(:scanner, :name))
        expect(scanner.vendor).to eq(params.dig(:scanner, :vendor, :name))
      end

      it 'sets cluster_agent.has_vulnerability to true' do
        expect { create_vulnerability }.to change { agent.reload.has_vulnerabilities }.from(false).to(true)
      end

      it 'marks the project as vulnerable' do
        expect { create_vulnerability }.to change { project.reload.project_setting.has_vulnerabilities? }.to(true)
      end

      it 'increases vulnerability_count by 1' do
        expect { create_vulnerability }.to change { project.reload.security_statistics.vulnerability_count }.by(1)
      end

      it 'sets the `traversal_ids` of the `vulnerability_reads` record' do
        expect(vulnerability.vulnerability_read.traversal_ids).to eq(project.namespace.traversal_ids)
      end

      context 'when there is an identifier for a different project' do
        let_it_be(:name) { params.dig(:vulnerability, :identifiers).first[:name] }
        let_it_be(:other_identifier) { create(:vulnerabilities_identifier, name: name) }

        it "does not reuse another project's identifier" do
          create_vulnerability

          expect(vulnerability.finding.identifiers).to match_array([Vulnerabilities::Identifier.last])
        end

        it 'creates a new identifier' do
          expect { create_vulnerability }.to change { Vulnerabilities::Identifier.count }.by(1)
        end
      end

      context 'when there is a scanner for a different project' do
        let!(:other_scanner) { create(:vulnerabilities_scanner, external_id: params.dig(:scanner, :id)) }

        it "does not reuse another project's scanner" do
          create_vulnerability

          expect(vulnerability.finding.scanner).not_to eq(other_scanner)
        end

        it 'creates a new scanner' do
          expect { create_vulnerability }.to change { Vulnerabilities::Scanner.count }.by(1)
        end
      end

      context 'when the project does not have vulnerability quota' do
        let(:mock_vulnerability_quota) { instance_double(Vulnerabilities::Quota, validate!: false) }

        before do
          allow(project).to receive(:vulnerability_quota).and_return(mock_vulnerability_quota)
        end

        it 'does not create the vulnerability' do
          expect { create_vulnerability }.not_to change { Vulnerability.count }
        end
      end

      context 'when an existing vulnerability is found' do
        using RSpec::Parameterized::TableSyntax

        where(:initial_state, :expected_state) do
          :dismissed | :dismissed
          :confirmed | :confirmed
          :detected  | :detected
          :resolved  | :detected # Resolved vulns are reverted to detected
        end

        with_them do
          let!(:existing_vulnerability) do
            result = described_class.new(agent, params: params).execute
            vulnerability = result.payload[:vulnerability]
            vulnerability.update!(state: initial_state)
            vulnerability
          end

          it 'returns success and only changes state to detected if initially resolved' do
            result = create_vulnerability

            expect(result).to be_success
            expect(result.payload[:vulnerability]).to eq(existing_vulnerability.reload)
            expect(result.payload[:vulnerability].state).to eq(expected_state.to_s)
          end
        end
      end

      context 'when attempt to revert an existing resolved vulnerability fails' do
        let!(:existing_vulnerability) do
          result = described_class.new(agent, params: params).execute
          vulnerability = result.payload[:vulnerability]
          vulnerability.update!(state: :resolved)
          vulnerability
        end

        before do
          allow_next_instance_of(Vulnerabilities::RevertToDetectedService) do |instance|
            allow(instance).to receive(:execute)
                        .and_return(existing_vulnerability)
          end
        end

        it 'returns an error response' do
          result = create_vulnerability

          expect(result).to be_error
          expect(result.message).to eq("Failed to revert vulnerability status")
          expect(result.payload[:vulnerability]).to eq(existing_vulnerability.reload)
          expect(result.payload[:vulnerability]).to be_resolved
        end
      end

      it_behaves_like 'reacting to archived and traversal_ids changes'
    end

    context 'with feature disabled' do
      before do
        stub_licensed_features(security_dashboard: false)
      end

      it 'raises AccessDeniedError' do
        expect { create_vulnerability }.to raise_error(Gitlab::Access::AccessDeniedError)
      end
    end

    context 'with unauthorized user' do
      before do
        project.add_reporter(user)
      end

      it 'raises AccessDeniedError' do
        expect { create_vulnerability }.to raise_error(Gitlab::Access::AccessDeniedError)
      end
    end
  end
end
