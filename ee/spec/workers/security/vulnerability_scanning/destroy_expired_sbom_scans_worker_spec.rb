# frozen_string_literal: true

require 'spec_helper'

RSpec.describe Security::VulnerabilityScanning::DestroyExpiredSbomScansWorker, feature_category: :software_composition_analysis do
  let_it_be(:project) { create(:project) }
  let_it_be(:build) { create(:ci_build, project: project) }

  let(:worker) { described_class.new }

  subject(:perform_worker) { worker.perform }

  describe '#perform' do
    context 'when there are expired SBOM scans' do
      let!(:expired_scans) { create_list(:sbom_scan, 2, :expired, project: project, build: build) }
      let!(:recent_scans) { create_list(:sbom_scan, 3, project: project, build: build) }

      it 'calls DestroySbomScansService with expired scans limited by BATCH_SIZE' do
        expect_next_instance_of(Security::VulnerabilityScanning::DestroySbomScansService, expired_scans) do |instance|
          expect(instance).to receive(:execute).and_call_original
        end

        perform_worker
      end

      it 'schedules next worker execution with delay' do
        expect(described_class).to receive(:perform_in).with(described_class::DELAY)

        perform_worker
      end

      it 'logs the count of processed and deleted records as additional metadata', :aggregate_failures do
        expect(worker).to receive(:log_extra_metadata_on_done).with(:processed_sbom_scans_count, 2)
        expect(worker).to receive(:log_extra_metadata_on_done).with(:destroyed_sbom_scans_count, 2)

        perform_worker
      end
    end

    context 'when there are no expired SBOM scans' do
      let!(:recent_scans) { create_list(:sbom_scan, 3, project: project, build: build) }

      it 'returns early without processing', :aggregate_failures do
        expect(Security::VulnerabilityScanning::DestroySbomScansService).not_to receive(:new)
        expect(described_class).not_to receive(:perform_in)

        perform_worker
      end
    end

    context 'when DestroySbomScansService fails with an unrecoverable error' do
      let!(:expired_scans) { create_list(:sbom_scan, 2, :expired, project: project, build: build) }

      before do
        allow_next_instance_of(Security::VulnerabilityScanning::DestroySbomScansService) do |instance|
          allow(instance).to receive(:execute).and_return(ServiceResponse.error(message: 'Service failed'))
        end
      end

      it 'does not schedule the next execution and raises the exception', :aggregate_failures do
        expect(described_class).not_to receive(:perform_in)
        expect { perform_worker }.to raise_error(StandardError, 'Service failed')
      end
    end

    context 'when DestroySbomScansService does not delete any record (e.g. some recurring failure)' do
      let!(:expired_scans) { create_list(:sbom_scan, 2, :expired, project: project, build: build) }

      before do
        allow_next_instance_of(Security::VulnerabilityScanning::DestroySbomScansService) do |instance|
          allow(instance).to receive(:execute).and_return(
            ServiceResponse.success(payload: { destroyed_sbom_scans_count: 0 })
          )
        end
      end

      it 'does not schedule the next execution' do
        expect(described_class).not_to receive(:perform_in)

        perform_worker
      end
    end
  end
end
