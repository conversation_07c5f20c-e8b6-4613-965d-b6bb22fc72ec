# frozen_string_literal: true

module Security
  module VulnerabilityScanning
    class DestroySbomScansService < ::BaseService
      def initialize(sbom_scans)
        @sbom_scans = sbom_scans
      end

      # Delete stored files for the provided SbomScan records and then bulk delete the DB records.
      def execute
        destroyed_sbom_scan_ids = []

        sbom_scans.each do |sbom_scan|
          destroyed_sbom_scan_ids << sbom_scan.id if sbom_scan.delete_files_from_storage
        end

        Security::VulnerabilityScanning::SbomScan.id_in(destroyed_sbom_scan_ids).delete_all

        ServiceResponse.success(payload: { destroyed_sbom_scans_count: destroyed_sbom_scan_ids.count })

      rescue StandardError => e
        ServiceResponse.error(message: e.message)
      end

      attr_reader :sbom_scans
    end
  end
end
