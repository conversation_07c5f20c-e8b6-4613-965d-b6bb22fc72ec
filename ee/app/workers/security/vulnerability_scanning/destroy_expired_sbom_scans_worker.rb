# frozen_string_literal: true

module Security
  module VulnerabilityScanning
    # This worker is meant to run on a daily cadence, cleaning up expired SbomScan records and related files in
    # object storage. The worker processes batches of records asynchronously every `DELAY` time interval. This
    # throttling is meant to limit resource pressure on file deletion.
    class DestroyExpiredSbomScansWorker
      include ApplicationWorker
      include CronjobQueue # rubocop:disable Scalability/CronWorkerContext -- context is not needed

      BATCH_SIZE = 200
      DELAY = 30.seconds
      MAX_RETRIES = 3

      feature_category :software_composition_analysis
      data_consistency :sticky
      worker_resource_boundary :cpu

      idempotent!
      deduplicate :until_executed

      sidekiq_options retry: MAX_RETRIES

      urgency :throttled

      def perform
        expired_sbom_scans = Security::VulnerabilityScanning::SbomScan.expired.limit(BATCH_SIZE)
        log_extra_metadata_on_done(:processed_sbom_scans_count, expired_sbom_scans.size)

        return if expired_sbom_scans.empty?

        response = Security::VulnerabilityScanning::DestroySbomScansService.new(expired_sbom_scans).execute

        raise response.message if response.error?

        log_extra_metadata_on_done(:destroyed_sbom_scans_count, response.payload[:destroyed_sbom_scans_count])

        # Only schedule next run if we actually processed some records. Provides an exit condition in case of
        # recurring failures to avoid stacking infinite retries that would just keep re-processing the same batch.
        return unless response.payload[:destroyed_sbom_scans_count] > 0

        Security::VulnerabilityScanning::DestroyExpiredSbomScansWorker.perform_in(DELAY)
      end
    end
  end
end
