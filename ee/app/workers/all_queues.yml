# This file is generated automatically by
#   bin/rake gitlab:sidekiq:all_queues_yml:generate
#
# Do not edit it manually!
---
- :name: auto_merge:merge_trains_refresh
  :worker_name: MergeTrains::RefreshWorker
  :feature_category: :merge_trains
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 3
  :idempotent: true
  :tags: []
  :queue_namespace: :auto_merge
- :name: click_house_buffer_sync:click_house_dump_write_buffer
  :worker_name: ClickHouse::DumpWriteBufferWorker
  :feature_category: :value_stream_management
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :click_house_buffer_sync
- :name: cronjob:active_user_count_threshold
  :worker_name: ActiveUserCountThresholdWorker
  :feature_category: :plan_provisioning
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:ai_active_context_bulk_process
  :worker_name: Ai::ActiveContext::BulkProcessWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:ai_active_context_code_scheduling
  :worker_name: Ai::ActiveContext::Code::SchedulingWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:ai_active_context_migration
  :worker_name: Ai::ActiveContext::MigrationWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:ai_duo_workflows_fail_stuck_workflows
  :worker_name: Ai::DuoWorkflows::FailStuckWorkflowsWorker
  :feature_category: :agent_foundations
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:analytics_cycle_analytics_consistency
  :worker_name: Analytics::CycleAnalytics::ConsistencyWorker
  :feature_category: :value_stream_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:analytics_cycle_analytics_incremental
  :worker_name: Analytics::CycleAnalytics::IncrementalWorker
  :feature_category: :value_stream_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:analytics_cycle_analytics_reaggregation
  :worker_name: Analytics::CycleAnalytics::ReaggregationWorker
  :feature_category: :value_stream_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:analytics_cycle_analytics_stage_aggregation
  :worker_name: Analytics::CycleAnalytics::StageAggregationWorker
  :feature_category: :value_stream_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:analytics_devops_adoption_create_all_snapshots
  :worker_name: Analytics::DevopsAdoption::CreateAllSnapshotsWorker
  :feature_category: :devops_reports
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:analytics_dump_ai_user_metrics_write_buffer_cron
  :worker_name: Analytics::DumpAiUserMetricsWriteBufferCronWorker
  :feature_category: :value_stream_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:analytics_value_stream_dashboard_count
  :worker_name: Analytics::ValueStreamDashboard::CountWorker
  :feature_category: :value_stream_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:app_sec_dast_profile_schedule
  :worker_name: AppSec::Dast::ProfileScheduleWorker
  :feature_category: :dynamic_application_security_testing
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:arkose_blocked_users_report
  :worker_name: Arkose::BlockedUsersReportWorker
  :feature_category: :system_access
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:authz_ldap_admin_role
  :worker_name: Authz::LdapAdminRoleWorker
  :feature_category: :permissions
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:ci_cleanup_build_name
  :worker_name: Ci::CleanupBuildNameWorker
  :feature_category: :continuous_integration
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:ci_runners_stale_group_runners_prune_cron
  :worker_name: Ci::Runners::StaleGroupRunnersPruneCronWorker
  :feature_category: :fleet_visibility
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:click_house_ci_finished_builds_sync_cron
  :worker_name: ClickHouse::CiFinishedBuildsSyncCronWorker
  :feature_category: :fleet_visibility
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:click_house_code_suggestion_events_cron
  :worker_name: ClickHouse::CodeSuggestionEventsCronWorker
  :feature_category: :value_stream_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:click_house_dump_all_write_buffers_cron
  :worker_name: ClickHouse::DumpAllWriteBuffersCronWorker
  :feature_category: :value_stream_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:click_house_event_authors_consistency_cron
  :worker_name: ClickHouse::EventAuthorsConsistencyCronWorker
  :feature_category: :value_stream_management
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:click_house_event_paths_consistency_cron
  :worker_name: ClickHouse::EventPathsConsistencyCronWorker
  :feature_category: :value_stream_management
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:click_house_events_sync
  :worker_name: ClickHouse::EventsSyncWorker
  :feature_category: :value_stream_management
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:click_house_rebuild_materialized_view_cron
  :worker_name: ClickHouse::RebuildMaterializedViewCronWorker
  :feature_category: :value_stream_management
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:click_house_user_add_on_assignments_sync
  :worker_name: ClickHouse::UserAddOnAssignmentsSyncWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:click_house_user_addon_assignment_versions_sync
  :worker_name: ClickHouse::UserAddonAssignmentVersionsSyncWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:cloud_connector_sync_service_token
  :worker_name: CloudConnector::SyncServiceTokenWorker
  :feature_category: :system_access
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:compliance_management_framework_evaluation_scheduler
  :worker_name: ComplianceManagement::FrameworkEvaluationSchedulerWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:compliance_management_merge_requests_compliance_violations_consistency
  :worker_name: ComplianceManagement::MergeRequests::ComplianceViolationsConsistencyWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:compliance_management_pipl_block_pipl_users
  :worker_name: ComplianceManagement::Pipl::BlockPiplUsersWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:compliance_management_pipl_delete_pipl_users
  :worker_name: ComplianceManagement::Pipl::DeletePiplUsersWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:compliance_management_pipl_send_recurring_notifications
  :worker_name: ComplianceManagement::Pipl::SendRecurringNotificationsWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:compliance_management_timeout_pending_status_check_responses
  :worker_name: ComplianceManagement::TimeoutPendingStatusCheckResponsesWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :high
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:elastic_cluster_reindexing_cron
  :worker_name: ElasticClusterReindexingCronWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:elastic_index_bulk_cron
  :worker_name: ElasticIndexBulkCronWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:elastic_index_initial_bulk_cron
  :worker_name: ElasticIndexInitialBulkCronWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:elastic_migration
  :worker_name: Elastic::MigrationWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:elastic_remove_expired_namespace_subscriptions_from_index_cron
  :worker_name: ElasticRemoveExpiredNamespaceSubscriptionsFromIndexCronWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:geo_metrics_update
  :worker_name: Geo::MetricsUpdateWorker
  :feature_category: :geo_replication
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:geo_prune_event_log
  :worker_name: Geo::PruneEventLogWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:geo_registry_sync
  :worker_name: Geo::RegistrySyncWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:geo_repository_registry_sync
  :worker_name: Geo::RepositoryRegistrySyncWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:geo_secondary_registry_consistency
  :worker_name: Geo::Secondary::RegistryConsistencyWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:geo_secondary_usage_data_cron
  :worker_name: Geo::SecondaryUsageDataCronWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:geo_sidekiq_cron_config
  :worker_name: Geo::SidekiqCronConfigWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:geo_sync_timeout_cron
  :worker_name: Geo::SyncTimeoutCronWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:geo_verification_cron
  :worker_name: Geo::VerificationCronWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:gitlab_subscriptions_add_on_purchases_cleanup
  :worker_name: GitlabSubscriptions::AddOnPurchases::CleanupWorker
  :feature_category: :subscription_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:gitlab_subscriptions_add_on_purchases_ldap_all_add_on_seat_sync
  :worker_name: GitlabSubscriptions::AddOnPurchases::LdapAllAddOnSeatSyncWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:gitlab_subscriptions_add_on_purchases_offline_cloud_license_provision
  :worker_name: GitlabSubscriptions::AddOnPurchases::OfflineCloudLicenseProvisionWorker
  :feature_category: :add-on_provisioning
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:gitlab_subscriptions_add_on_purchases_schedule_bulk_refresh_user_assignments
  :worker_name: GitlabSubscriptions::AddOnPurchases::ScheduleBulkRefreshUserAssignmentsWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:gitlab_subscriptions_notify_seats_exceeded_batch
  :worker_name: GitlabSubscriptions::NotifySeatsExceededBatchWorker
  :feature_category: :subscription_management
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:gitlab_subscriptions_schedule_refresh_seats
  :worker_name: GitlabSubscriptions::ScheduleRefreshSeatsWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:historical_data
  :worker_name: HistoricalDataWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:import_software_licenses
  :worker_name: ImportSoftwareLicensesWorker
  :feature_category: :software_composition_analysis
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:incident_management_incident_sla_exceeded_check
  :worker_name: IncidentManagement::IncidentSlaExceededCheckWorker
  :feature_category: :incident_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:incident_management_oncall_rotations_persist_all_rotations_shifts_job
  :worker_name: IncidentManagement::OncallRotations::PersistAllRotationsShiftsJob
  :feature_category: :incident_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:incident_management_pending_escalations_schedule_check_cron
  :worker_name: IncidentManagement::PendingEscalations::ScheduleCheckCronWorker
  :feature_category: :incident_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:iterations_cadences_create_iterations
  :worker_name: Iterations::Cadences::CreateIterationsWorker
  :feature_category: :team_planning
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:iterations_cadences_schedule_create_iterations
  :worker_name: Iterations::Cadences::ScheduleCreateIterationsWorker
  :feature_category: :team_planning
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:iterations_update_status
  :worker_name: IterationsUpdateStatusWorker
  :feature_category: :team_planning
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:ldap_all_groups_sync
  :worker_name: LdapAllGroupsSyncWorker
  :feature_category: :system_access
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:ldap_sync
  :worker_name: LdapSyncWorker
  :feature_category: :system_access
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:licenses_reset_submit_license_usage_data_banner
  :worker_name: Licenses::ResetSubmitLicenseUsageDataBannerWorker
  :feature_category: :plan_provisioning
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:namespaces_schedule_dormant_member_removal
  :worker_name: Namespaces::ScheduleDormantMemberRemovalWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:observability_alert_query
  :worker_name: Observability::AlertQueryWorker
  :feature_category: :observability
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:okrs_checkin_reminder_emails_cron
  :worker_name: Okrs::CheckinReminderEmailsCronWorker
  :feature_category: :team_planning
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:package_metadata_advisories_sync
  :worker_name: PackageMetadata::AdvisoriesSyncWorker
  :feature_category: :software_composition_analysis
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:package_metadata_cve_enrichment_sync
  :worker_name: PackageMetadata::CveEnrichmentSyncWorker
  :feature_category: :software_composition_analysis
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:package_metadata_licenses_sync
  :worker_name: PackageMetadata::LicensesSyncWorker
  :feature_category: :software_composition_analysis
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:projects_disable_legacy_open_source_license_for_inactive_projects
  :worker_name: Projects::DisableLegacyOpenSourceLicenseForInactiveProjectsWorker
  :feature_category: :groups_and_projects
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:sbom_delete_expired_exports
  :worker_name: Sbom::DeleteExpiredExportsWorker
  :feature_category: :dependency_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:search_elastic_index_embedding_bulk_cron
  :worker_name: Search::ElasticIndexEmbeddingBulkCronWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:search_elastic_metrics_update_cron
  :worker_name: Search::Elastic::MetricsUpdateCronWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:search_elastic_migration_cleanup_cron
  :worker_name: Search::Elastic::MigrationCleanupCronWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:search_index_curation
  :worker_name: Search::IndexCurationWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:search_zoekt_metrics_update_cron
  :worker_name: Search::Zoekt::MetricsUpdateCronWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:search_zoekt_rollout
  :worker_name: Search::Zoekt::RolloutWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:search_zoekt_scheduling
  :worker_name: Search::Zoekt::SchedulingWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:security_analyzer_namespace_statuses_schedule
  :worker_name: Security::AnalyzerNamespaceStatuses::ScheduleWorker
  :feature_category: :security_asset_inventories
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:security_create_orchestration_policy
  :worker_name: Security::CreateOrchestrationPolicyWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:security_orchestration_policy_rule_schedule
  :worker_name: Security::OrchestrationPolicyRuleScheduleWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:security_pipeline_execution_policies_schedule
  :worker_name: Security::PipelineExecutionPolicies::ScheduleWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:security_policies_report_security_policies_metrics
  :worker_name: Security::Policies::ReportSecurityPoliciesMetricsWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:security_scans_purge
  :worker_name: Security::Scans::PurgeWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:security_vulnerability_scanning_destroy_expired_sbom_scans
  :worker_name: Security::VulnerabilityScanning::DestroyExpiredSbomScansWorker
  :feature_category: :software_composition_analysis
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:sync_seat_link
  :worker_name: SyncSeatLinkWorker
  :feature_category: :plan_provisioning
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:update_all_mirrors
  :worker_name: UpdateAllMirrorsWorker
  :feature_category: :source_code_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:usage_events_dump_write_buffer_cron
  :worker_name: UsageEvents::DumpWriteBufferCronWorker
  :feature_category: :value_stream_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:users_security_policy_bot_cleanup_cron
  :worker_name: Users::SecurityPolicyBotCleanupCronWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:users_unconfirmed_users_deletion_cron
  :worker_name: Users::UnconfirmedUsersDeletionCronWorker
  :feature_category: :user_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:vulnerabilities_archival_schedule
  :worker_name: Vulnerabilities::Archival::ScheduleWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:vulnerabilities_delete_expired_exports
  :worker_name: Vulnerabilities::DeleteExpiredExportsWorker
  :feature_category: :dependency_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:vulnerabilities_historical_statistics_deletion
  :worker_name: Vulnerabilities::HistoricalStatistics::DeletionWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:vulnerabilities_namespace_statistics_schedule
  :worker_name: Vulnerabilities::NamespaceStatistics::ScheduleWorker
  :feature_category: :security_asset_inventories
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:vulnerabilities_orphaned_remediations_cleanup
  :worker_name: Vulnerabilities::OrphanedRemediationsCleanupWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :cronjob
- :name: cronjob:vulnerabilities_statistics_schedule
  :worker_name: Vulnerabilities::Statistics::ScheduleWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :cronjob
- :name: dependency_proxy_blob:virtual_registries_packages_cache_mark_entries_for_destruction
  :worker_name: VirtualRegistries::Packages::Cache::MarkEntriesForDestructionWorker
  :feature_category: :virtual_registry
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :dependency_proxy_blob
- :name: deployment:deployments_approval
  :worker_name: Deployments::ApprovalWorker
  :feature_category: :continuous_delivery
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 3
  :idempotent: true
  :tags: []
  :queue_namespace: :deployment
- :name: deployment:deployments_auto_rollback
  :worker_name: Deployments::AutoRollbackWorker
  :feature_category: :continuous_delivery
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 3
  :idempotent: true
  :tags: []
  :queue_namespace: :deployment
- :name: dora_metrics:dora_daily_metrics_refresh
  :worker_name: Dora::DailyMetrics::RefreshWorker
  :feature_category: :dora_metrics
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :dora_metrics
- :name: epics:epics_update_cached_metadata
  :worker_name: Epics::UpdateCachedMetadataWorker
  :feature_category: :portfolio_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 2
  :idempotent: true
  :tags: []
  :queue_namespace: :epics
- :name: epics:epics_update_epics_dates
  :worker_name: Epics::UpdateEpicsDatesWorker
  :feature_category: :portfolio_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 2
  :idempotent: false
  :tags: []
  :queue_namespace: :epics
- :name: geo:geo_batch_event_create
  :worker_name: Geo::BatchEventCreateWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :geo
- :name: geo:geo_bulk_mark_pending_batch
  :worker_name: Geo::BulkMarkPendingBatchWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :geo
- :name: geo:geo_bulk_mark_verification_pending_batch
  :worker_name: Geo::BulkMarkVerificationPendingBatchWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :geo
- :name: geo:geo_container_repository_sync
  :worker_name: Geo::ContainerRepositorySyncWorker
  :feature_category: :geo_replication
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :geo
- :name: geo:geo_create_repository_updated_event
  :worker_name: Geo::CreateRepositoryUpdatedEventWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :geo
- :name: geo:geo_destroy
  :worker_name: Geo::DestroyWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :geo
- :name: geo:geo_event
  :worker_name: Geo::EventWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :geo
- :name: geo:geo_hashed_storage_attachments_migration
  :worker_name: Geo::HashedStorageAttachmentsMigrationWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :geo
- :name: geo:geo_resync_direct_upload_job_artifact_registry
  :worker_name: Geo::ResyncDirectUploadJobArtifactRegistryWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :geo
- :name: geo:geo_reverification_batch
  :worker_name: Geo::ReverificationBatchWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :geo
- :name: geo:geo_scheduler_scheduler
  :worker_name: Geo::Scheduler::SchedulerWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :geo
- :name: geo:geo_scheduler_secondary_scheduler
  :worker_name: Geo::Scheduler::Secondary::SchedulerWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :geo
- :name: geo:geo_sync
  :worker_name: Geo::SyncWorker
  :feature_category: :geo_replication
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :geo
- :name: geo:geo_verification_batch
  :worker_name: Geo::VerificationBatchWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :geo
- :name: geo:geo_verification_state_backfill
  :worker_name: Geo::VerificationStateBackfillWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :geo
- :name: geo:geo_verification_timeout
  :worker_name: Geo::VerificationTimeoutWorker
  :feature_category: :geo_replication
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :geo
- :name: iterations:iterations_roll_over_issues
  :worker_name: Iterations::RollOverIssuesWorker
  :feature_category: :team_planning
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :iterations
- :name: personal_access_tokens:personal_access_tokens_groups_policy
  :worker_name: PersonalAccessTokens::Groups::PolicyWorker
  :feature_category: :system_access
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :personal_access_tokens
- :name: personal_access_tokens:personal_access_tokens_instance_policy
  :worker_name: PersonalAccessTokens::Instance::PolicyWorker
  :feature_category: :system_access
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :personal_access_tokens
- :name: pipeline_background:ci_minutes_refresh_cached_data
  :worker_name: Ci::Minutes::RefreshCachedDataWorker
  :feature_category: :continuous_integration
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :pipeline_background
- :name: pipeline_background:ci_minutes_update_gitlab_hosted_runner_monthly_usage
  :worker_name: Ci::Minutes::UpdateGitlabHostedRunnerMonthlyUsageWorker
  :feature_category: :continuous_integration
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :pipeline_background
- :name: pipeline_background:ci_minutes_update_project_and_namespace_usage
  :worker_name: Ci::Minutes::UpdateProjectAndNamespaceUsageWorker
  :feature_category: :continuous_integration
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :pipeline_background
- :name: pipeline_background:ci_sync_reports_to_report_approval_rules
  :worker_name: Ci::SyncReportsToReportApprovalRulesWorker
  :feature_category: :continuous_integration
  :has_external_dependencies: false
  :urgency: :high
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :pipeline_background
- :name: pipeline_default:ci_trigger_downstream_subscriptions
  :worker_name: Ci::TriggerDownstreamSubscriptionsWorker
  :feature_category: :continuous_integration
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 3
  :idempotent: false
  :tags: []
  :queue_namespace: :pipeline_default
- :name: sbom_graphs:sbom_build_dependency_graph
  :worker_name: Sbom::BuildDependencyGraphWorker
  :feature_category: :dependency_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :sbom_graphs
- :name: sbom_graphs:sbom_remove_old_dependency_graphs
  :worker_name: Sbom::RemoveOldDependencyGraphsWorker
  :feature_category: :dependency_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :sbom_graphs
- :name: sbom_reports:sbom_ingest_reports
  :worker_name: Sbom::IngestReportsWorker
  :feature_category: :dependency_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace: :sbom_reports
- :name: security_scans:scan_security_report_secrets
  :worker_name: ScanSecurityReportSecretsWorker
  :feature_category: :secret_detection
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 2
  :idempotent: true
  :tags: []
  :queue_namespace: :security_scans
- :name: security_scans:security_process_scan_events
  :worker_name: Security::ProcessScanEventsWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 2
  :idempotent: true
  :tags: []
  :queue_namespace: :security_scans
- :name: security_scans:security_scan_result_policies_sync_findings_to_approval_rules
  :worker_name: Security::ScanResultPolicies::SyncFindingsToApprovalRulesWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 2
  :idempotent: true
  :tags: []
  :queue_namespace: :security_scans
- :name: security_scans:security_scan_result_policies_sync_preexisting_states_approval_rules
  :worker_name: Security::ScanResultPolicies::SyncPreexistingStatesApprovalRulesWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 2
  :idempotent: true
  :tags: []
  :queue_namespace: :security_scans
- :name: security_scans:security_store_scans
  :worker_name: Security::StoreScansWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 2
  :idempotent: false
  :tags: []
  :queue_namespace: :security_scans
- :name: security_scans:security_store_security_reports_by_project
  :worker_name: Security::StoreSecurityReportsByProjectWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :memory
  :weight: 2
  :idempotent: true
  :tags: []
  :queue_namespace: :security_scans
- :name: security_scans:security_track_secure_scans
  :worker_name: Security::TrackSecureScansWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 2
  :idempotent: false
  :tags: []
  :queue_namespace: :security_scans
- :name: todos_destroyer:todos_destroyer_confidential_epic
  :worker_name: TodosDestroyer::ConfidentialEpicWorker
  :feature_category: :portfolio_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace: :todos_destroyer
- :name: admin_emails
  :worker_name: AdminEmailsWorker
  :feature_category: :team_planning
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: ai_active_context_code_mark_repository_as_ready_event
  :worker_name: Ai::ActiveContext::Code::MarkRepositoryAsReadyEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: ai_active_context_code_process_pending_enabled_namespace_event
  :worker_name: Ai::ActiveContext::Code::ProcessPendingEnabledNamespaceEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: ai_active_context_code_repository_index
  :worker_name: Ai::ActiveContext::Code::RepositoryIndexWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: ai_active_context_code_saas_initial_indexing_event
  :worker_name: Ai::ActiveContext::Code::SaasInitialIndexingEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: ai_knowledge_graph_indexing_task
  :worker_name: Ai::KnowledgeGraph::IndexingTaskWorker
  :feature_category: :knowledge_graph
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: ai_repository_xray_scan_dependencies
  :worker_name: Ai::RepositoryXray::ScanDependenciesWorker
  :feature_category: :code_suggestions
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: analytics_ai_usage_events_backfill
  :worker_name: Analytics::AiUsageEventsBackfillWorker
  :feature_category: :value_stream_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: analytics_code_review_metrics
  :worker_name: Analytics::CodeReviewMetricsWorker
  :feature_category: :value_stream_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: analytics_code_suggestions_events_backfill
  :worker_name: Analytics::CodeSuggestionsEventsBackfillWorker
  :feature_category: :value_stream_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: analytics_devops_adoption_create_snapshot
  :worker_name: Analytics::DevopsAdoption::CreateSnapshotWorker
  :feature_category: :devops_reports
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: analytics_duo_chat_events_backfill
  :worker_name: Analytics::DuoChatEventsBackfillWorker
  :feature_category: :value_stream_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: anti_abuse_new_abuse_report
  :worker_name: AntiAbuse::NewAbuseReportWorker
  :feature_category: :instance_resiliency
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: app_config_cascade_duo_features_enabled
  :worker_name: AppConfig::CascadeDuoFeaturesEnabledWorker
  :feature_category: :ai_abstraction_layer
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :memory
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: app_sec_container_scanning_scan_image
  :worker_name: AppSec::ContainerScanning::ScanImageWorker
  :feature_category: :software_composition_analysis
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: app_sec_dast_scanner_profiles_builds_consistency
  :worker_name: AppSec::Dast::ScannerProfilesBuilds::ConsistencyWorker
  :feature_category: :dynamic_application_security_testing
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: app_sec_dast_scans_consistency
  :worker_name: AppSec::Dast::Scans::ConsistencyWorker
  :feature_category: :dynamic_application_security_testing
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: app_sec_dast_site_profiles_builds_consistency
  :worker_name: AppSec::Dast::SiteProfilesBuilds::ConsistencyWorker
  :feature_category: :dynamic_application_security_testing
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: approval_rules_external_approval_rule_payload
  :worker_name: ApprovalRules::ExternalApprovalRulePayloadWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: audit_events_audit_event_streaming
  :worker_name: AuditEvents::AuditEventStreamingWorker
  :feature_category: :audit_events
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: audit_events_user_impersonation_event_create
  :worker_name: AuditEvents::UserImpersonationEventCreateWorker
  :feature_category: :audit_events
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: auth_saml_group_sync
  :worker_name: Auth::SamlGroupSyncWorker
  :feature_category: :system_access
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: authn_cleanup_scim_group_memberships
  :worker_name: Authn::CleanupScimGroupMembershipsWorker
  :feature_category: :system_access
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: authn_sync_group_scim_identity_record
  :worker_name: Authn::SyncGroupScimIdentityRecordWorker
  :feature_category: :system_access
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: authn_sync_group_scim_token_record
  :worker_name: Authn::SyncGroupScimTokenRecordWorker
  :feature_category: :system_access
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: authn_sync_scim_group_members
  :worker_name: Authn::SyncScimGroupMembersWorker
  :feature_category: :system_access
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: authn_sync_scim_identity_record
  :worker_name: Authn::SyncScimIdentityRecordWorker
  :feature_category: :system_access
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: authn_sync_scim_token_record
  :worker_name: Authn::SyncScimTokenRecordWorker
  :feature_category: :system_access
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: authz_user_group_member_roles_destroy_for_group
  :worker_name: Authz::UserGroupMemberRoles::DestroyForGroupWorker
  :feature_category: :permissions
  :has_external_dependencies: false
  :urgency: :high
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: authz_user_group_member_roles_destroy_for_shared_group
  :worker_name: Authz::UserGroupMemberRoles::DestroyForSharedGroupWorker
  :feature_category: :permissions
  :has_external_dependencies: false
  :urgency: :high
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: authz_user_group_member_roles_update_for_group
  :worker_name: Authz::UserGroupMemberRoles::UpdateForGroupWorker
  :feature_category: :permissions
  :has_external_dependencies: false
  :urgency: :high
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: authz_user_group_member_roles_update_for_shared_group
  :worker_name: Authz::UserGroupMemberRoles::UpdateForSharedGroupWorker
  :feature_category: :permissions
  :has_external_dependencies: false
  :urgency: :high
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: ci_runners_export_usage_csv
  :worker_name: Ci::Runners::ExportUsageCsvWorker
  :feature_category: :fleet_visibility
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: ci_update_approval_rules_for_related_mrs
  :worker_name: Ci::UpdateApprovalRulesForRelatedMrsWorker
  :feature_category: :code_review_workflow
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: ci_upstream_projects_subscriptions_cleanup
  :worker_name: Ci::UpstreamProjectsSubscriptionsCleanupWorker
  :feature_category: :continuous_integration
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: click_house_ci_finished_builds_sync
  :worker_name: ClickHouse::CiFinishedBuildsSyncWorker
  :feature_category: :fleet_visibility
  :has_external_dependencies: true
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: clusters_agents_auto_flow_merge_requests_closed_event
  :worker_name: Clusters::Agents::AutoFlow::MergeRequests::ClosedEventWorker
  :feature_category: :deployment_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: clusters_agents_auto_flow_merge_requests_created_event
  :worker_name: Clusters::Agents::AutoFlow::MergeRequests::CreatedEventWorker
  :feature_category: :deployment_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: clusters_agents_auto_flow_merge_requests_merged_event
  :worker_name: Clusters::Agents::AutoFlow::MergeRequests::MergedEventWorker
  :feature_category: :deployment_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: clusters_agents_auto_flow_merge_requests_reopened_event
  :worker_name: Clusters::Agents::AutoFlow::MergeRequests::ReopenedEventWorker
  :feature_category: :deployment_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: clusters_agents_auto_flow_merge_requests_updated_event
  :worker_name: Clusters::Agents::AutoFlow::MergeRequests::UpdatedEventWorker
  :feature_category: :deployment_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: clusters_agents_auto_flow_work_items_closed_event
  :worker_name: Clusters::Agents::AutoFlow::WorkItems::ClosedEventWorker
  :feature_category: :deployment_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: clusters_agents_auto_flow_work_items_created_event
  :worker_name: Clusters::Agents::AutoFlow::WorkItems::CreatedEventWorker
  :feature_category: :deployment_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: clusters_agents_auto_flow_work_items_reopened_event
  :worker_name: Clusters::Agents::AutoFlow::WorkItems::ReopenedEventWorker
  :feature_category: :deployment_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: clusters_agents_auto_flow_work_items_updated_event
  :worker_name: Clusters::Agents::AutoFlow::WorkItems::UpdatedEventWorker
  :feature_category: :deployment_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_chain_of_custody_report
  :worker_name: ComplianceManagement::ChainOfCustodyReportWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: compliance_management_compliance_framework_project_compliance_statuses_removal
  :worker_name: ComplianceManagement::ComplianceFramework::ProjectComplianceStatusesRemovalWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_compliance_framework_project_requirement_statuses_export_mailer
  :worker_name: ComplianceManagement::ComplianceFramework::ProjectRequirementStatusesExportMailerWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_compliance_framework_projects_compliance_enqueue
  :worker_name: ComplianceManagement::ComplianceFramework::ProjectsComplianceEnqueueWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_compliance_violation_detection
  :worker_name: ComplianceManagement::ComplianceViolationDetectionWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_framework_export_mailer
  :worker_name: ComplianceManagement::FrameworkExportMailerWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_groups_compliance_violations_export_mailer
  :worker_name: ComplianceManagement::Groups::ComplianceViolationsExportMailerWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_merge_requests_compliance_violations
  :worker_name: ComplianceManagement::MergeRequests::ComplianceViolationsWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_pending_status_check
  :worker_name: ComplianceManagement::PendingStatusCheckWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :high
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_pipl_update_user_country_access_logs
  :worker_name: ComplianceManagement::Pipl::UpdateUserCountryAccessLogsWorker
  :feature_category: :instance_resiliency
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_pipl_user_paid_status_check
  :worker_name: ComplianceManagement::Pipl::UserPaidStatusCheckWorker
  :feature_category: :instance_resiliency
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_project_compliance_evaluator
  :worker_name: ComplianceManagement::ProjectComplianceEvaluatorWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_project_framework_export_mailer
  :worker_name: ComplianceManagement::ProjectFrameworkExportMailerWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_project_settings_destroy
  :worker_name: ComplianceManagement::ProjectSettingsDestroyWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_standards_adherence_export_mailer
  :worker_name: ComplianceManagement::StandardsAdherenceExportMailerWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_standards_base
  :worker_name: ComplianceManagement::Standards::BaseWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_standards_gitlab_at_least_two_approvals
  :worker_name: ComplianceManagement::Standards::Gitlab::AtLeastTwoApprovalsWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_standards_gitlab_at_least_two_approvals_group
  :worker_name: ComplianceManagement::Standards::Gitlab::AtLeastTwoApprovalsGroupWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_standards_gitlab_dast
  :worker_name: ComplianceManagement::Standards::Gitlab::DastWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_standards_gitlab_dast_group
  :worker_name: ComplianceManagement::Standards::Gitlab::DastGroupWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_standards_gitlab_prevent_approval_by_author
  :worker_name: ComplianceManagement::Standards::Gitlab::PreventApprovalByAuthorWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_standards_gitlab_prevent_approval_by_author_group
  :worker_name: ComplianceManagement::Standards::Gitlab::PreventApprovalByAuthorGroupWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_standards_gitlab_prevent_approval_by_committer
  :worker_name: ComplianceManagement::Standards::Gitlab::PreventApprovalByCommitterWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_standards_gitlab_prevent_approval_by_committer_group
  :worker_name: ComplianceManagement::Standards::Gitlab::PreventApprovalByCommitterGroupWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_standards_gitlab_sast
  :worker_name: ComplianceManagement::Standards::Gitlab::SastWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_standards_gitlab_sast_group
  :worker_name: ComplianceManagement::Standards::Gitlab::SastGroupWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_standards_group_base
  :worker_name: ComplianceManagement::Standards::GroupBaseWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_standards_refresh
  :worker_name: ComplianceManagement::Standards::RefreshWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_standards_soc2_at_least_one_non_author_approval
  :worker_name: ComplianceManagement::Standards::Soc2::AtLeastOneNonAuthorApprovalWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_standards_soc2_at_least_one_non_author_approval_group
  :worker_name: ComplianceManagement::Standards::Soc2::AtLeastOneNonAuthorApprovalGroupWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_timeout_pending_external_controls
  :worker_name: ComplianceManagement::TimeoutPendingExternalControlsWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_update_default_framework
  :worker_name: ComplianceManagement::UpdateDefaultFrameworkWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: compliance_management_violation_export_mailer
  :worker_name: ComplianceManagement::ViolationExportMailerWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: create_github_webhook
  :worker_name: CreateGithubWebhookWorker
  :feature_category: :integrations
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 2
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: dependencies_destroy_export
  :worker_name: Dependencies::DestroyExportWorker
  :feature_category: :dependency_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: dependencies_export
  :worker_name: Dependencies::ExportWorker
  :feature_category: :dependency_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: elastic_association_indexer
  :worker_name: ElasticAssociationIndexerWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: elastic_delete_project
  :worker_name: ElasticDeleteProjectWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: elastic_full_index
  :worker_name: ElasticFullIndexWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: elastic_namespace_indexer
  :worker_name: ElasticNamespaceIndexerWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: elastic_namespace_rollout
  :worker_name: ElasticNamespaceRolloutWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: elastic_namespace_update
  :worker_name: Elastic::NamespaceUpdateWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: elastic_project_transfer
  :worker_name: Elastic::ProjectTransferWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: elastic_wiki_indexer
  :worker_name: ElasticWikiIndexerWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: epics_new_epic_issue
  :worker_name: Epics::NewEpicIssueWorker
  :feature_category: :portfolio_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: export_segmented_export
  :worker_name: Gitlab::Export::SegmentedExportWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: export_segmented_export_finalisation
  :worker_name: Gitlab::Export::SegmentedExportFinalisationWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: gitlab_subscriptions_add_on_purchases_bulk_refresh_user_assignments
  :worker_name: GitlabSubscriptions::AddOnPurchases::BulkRefreshUserAssignmentsWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: gitlab_subscriptions_add_on_purchases_cleanup_user_add_on_assignment
  :worker_name: GitlabSubscriptions::AddOnPurchases::CleanupUserAddOnAssignmentWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: gitlab_subscriptions_add_on_purchases_create_user_add_on_assignment
  :worker_name: GitlabSubscriptions::AddOnPurchases::CreateUserAddOnAssignmentWorker
  :feature_category: :subscription_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: gitlab_subscriptions_add_on_purchases_destroy_user_add_on_assignment
  :worker_name: GitlabSubscriptions::AddOnPurchases::DestroyUserAddOnAssignmentWorker
  :feature_category: :subscription_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: gitlab_subscriptions_add_on_purchases_email_on_duo_bulk_user_assignments
  :worker_name: GitlabSubscriptions::AddOnPurchases::EmailOnDuoBulkUserAssignmentsWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: gitlab_subscriptions_add_on_purchases_ldap_add_on_seat_sync
  :worker_name: GitlabSubscriptions::AddOnPurchases::LdapAddOnSeatSyncWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: gitlab_subscriptions_add_on_purchases_refresh_user_assignments
  :worker_name: GitlabSubscriptions::AddOnPurchases::RefreshUserAssignmentsWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: gitlab_subscriptions_gitlab_com_duo_core_todo_notification
  :worker_name: GitlabSubscriptions::GitlabCom::DuoCoreTodoNotificationWorker
  :feature_category: :acquisition
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: gitlab_subscriptions_member_management_apply_pending_member_approvals
  :worker_name: GitlabSubscriptions::MemberManagement::ApplyPendingMemberApprovalsWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: gitlab_subscriptions_members_added
  :worker_name: GitlabSubscriptions::Members::AddedWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: gitlab_subscriptions_members_destroyed
  :worker_name: GitlabSubscriptions::Members::DestroyedWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: gitlab_subscriptions_members_record_last_activity
  :worker_name: GitlabSubscriptions::Members::RecordLastActivityWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: gitlab_subscriptions_refresh_seats
  :worker_name: GitlabSubscriptions::RefreshSeatsWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: gitlab_subscriptions_seat_assignments_group_links_create_or_update_seats
  :worker_name: GitlabSubscriptions::SeatAssignments::GroupLinks::CreateOrUpdateSeatsWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: gitlab_subscriptions_seat_assignments_member_transfers_create_group_seats
  :worker_name: GitlabSubscriptions::SeatAssignments::MemberTransfers::CreateGroupSeatsWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: gitlab_subscriptions_seat_assignments_member_transfers_create_project_seats
  :worker_name: GitlabSubscriptions::SeatAssignments::MemberTransfers::CreateProjectSeatsWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: gitlab_subscriptions_self_managed_duo_core_todo_notification
  :worker_name: GitlabSubscriptions::SelfManaged::DuoCoreTodoNotificationWorker
  :feature_category: :acquisition
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: gitlab_subscriptions_trials_apply_trial
  :worker_name: GitlabSubscriptions::Trials::ApplyTrialWorker
  :feature_category: :plan_provisioning
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: group_saml_group_sync
  :worker_name: GroupSamlGroupSyncWorker
  :feature_category: :system_access
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: group_wikis_git_garbage_collect
  :worker_name: GroupWikis::GitGarbageCollectWorker
  :feature_category: :gitaly
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: groups_create_event
  :worker_name: Groups::CreateEventWorker
  :feature_category: :onboarding
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: groups_enterprise_users_associate
  :worker_name: Groups::EnterpriseUsers::AssociateWorker
  :feature_category: :user_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: groups_enterprise_users_bulk_associate_by_domain
  :worker_name: Groups::EnterpriseUsers::BulkAssociateByDomainWorker
  :feature_category: :user_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: groups_enterprise_users_disassociate
  :worker_name: Groups::EnterpriseUsers::DisassociateWorker
  :feature_category: :user_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: groups_export_memberships
  :worker_name: Groups::ExportMembershipsWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: groups_reset_seat_callouts
  :worker_name: Groups::ResetSeatCalloutsWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: groups_schedule_bulk_repository_shard_moves
  :worker_name: Groups::ScheduleBulkRepositoryShardMovesWorker
  :feature_category: :gitaly
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: groups_update_repository_storage
  :worker_name: Groups::UpdateRepositoryStorageWorker
  :feature_category: :gitaly
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: incident_management_apply_incident_sla_exceeded_label
  :worker_name: IncidentManagement::ApplyIncidentSlaExceededLabelWorker
  :feature_category: :incident_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: incident_management_oncall_rotations_persist_shifts_job
  :worker_name: IncidentManagement::OncallRotations::PersistShiftsJob
  :feature_category: :incident_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: incident_management_pending_escalations_alert_check
  :worker_name: IncidentManagement::PendingEscalations::AlertCheckWorker
  :feature_category: :incident_management
  :has_external_dependencies: false
  :urgency: :high
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: incident_management_pending_escalations_alert_create
  :worker_name: IncidentManagement::PendingEscalations::AlertCreateWorker
  :feature_category: :incident_management
  :has_external_dependencies: false
  :urgency: :high
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: incident_management_pending_escalations_issue_check
  :worker_name: IncidentManagement::PendingEscalations::IssueCheckWorker
  :feature_category: :incident_management
  :has_external_dependencies: false
  :urgency: :high
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: incident_management_pending_escalations_issue_create
  :worker_name: IncidentManagement::PendingEscalations::IssueCreateWorker
  :feature_category: :incident_management
  :has_external_dependencies: false
  :urgency: :high
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: ldap_group_sync
  :worker_name: LdapGroupSyncWorker
  :feature_category: :system_access
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 2
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: llm_completion
  :worker_name: Llm::CompletionWorker
  :feature_category: :ai_abstraction_layer
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: llm_namespace_access_cache_reset
  :worker_name: Llm::NamespaceAccessCacheResetWorker
  :feature_category: :ai_abstraction_layer
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: members_delete_pending_members
  :worker_name: Members::DeletePendingMembersWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: members_destroyer_clean_up_group_protected_branch_rules
  :worker_name: MembersDestroyer::CleanUpGroupProtectedBranchRulesWorker
  :feature_category: :groups_and_projects
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: members_groups_base_memberships_export
  :worker_name: Members::Groups::BaseMembershipsExportWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: members_groups_export_detailed_memberships
  :worker_name: Members::Groups::ExportDetailedMembershipsWorker
  :feature_category: :compliance_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: merge_request_reset_approvals
  :worker_name: MergeRequestResetApprovalsWorker
  :feature_category: :source_code_management
  :has_external_dependencies: false
  :urgency: :high
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: merge_requests_approval_metrics_event
  :worker_name: MergeRequests::ApprovalMetricsEventWorker
  :feature_category: :code_review_workflow
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: merge_requests_audit_update_status_check_response
  :worker_name: MergeRequests::AuditUpdateStatusCheckResponseWorker
  :feature_category: :compliance_management
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: merge_requests_create_approvals_reset_note
  :worker_name: MergeRequests::CreateApprovalsResetNoteWorker
  :feature_category: :code_review_workflow
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: merge_requests_duo_code_review_chat
  :worker_name: MergeRequests::DuoCodeReviewChatWorker
  :feature_category: :code_review_workflow
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: merge_requests_notify_approvers
  :worker_name: MergeRequests::NotifyApproversWorker
  :feature_category: :code_review_workflow
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: merge_requests_policy_violations_detected_audit_event
  :worker_name: MergeRequests::PolicyViolationsDetectedAuditEventWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: merge_requests_policy_violations_resolved_audit_event
  :worker_name: MergeRequests::PolicyViolationsResolvedAuditEventWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: merge_requests_process_merge_audit_event
  :worker_name: MergeRequests::ProcessMergeAuditEventWorker
  :feature_category: :compliance_management
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: merge_requests_remove_user_approval_rules
  :worker_name: MergeRequests::RemoveUserApprovalRulesWorker
  :feature_category: :code_review_workflow
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: merge_requests_stream_approval_audit_event
  :worker_name: MergeRequests::StreamApprovalAuditEventWorker
  :feature_category: :code_review_workflow
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: merge_requests_sync_code_owner_approval_rules
  :worker_name: MergeRequests::SyncCodeOwnerApprovalRulesWorker
  :feature_category: :source_code_management
  :has_external_dependencies: false
  :urgency: :high
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: namespaces_cascade_duo_features_enabled
  :worker_name: Namespaces::CascadeDuoFeaturesEnabledWorker
  :feature_category: :ai_abstraction_layer
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :memory
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: namespaces_cascade_web_based_commit_signing_enabled
  :worker_name: Namespaces::CascadeWebBasedCommitSigningEnabledWorker
  :feature_category: :source_code_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: namespaces_free_user_cap_group_over_limit_notification
  :worker_name: Namespaces::FreeUserCap::GroupOverLimitNotificationWorker
  :feature_category: :user_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: namespaces_remove_dormant_members
  :worker_name: Namespaces::RemoveDormantMembersWorker
  :feature_category: :seat_cost_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: namespaces_storage_usage_export
  :worker_name: Namespaces::StorageUsageExportWorker
  :feature_category: :consumables_cost_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :memory
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: namespaces_sync_namespace_name
  :worker_name: Namespaces::SyncNamespaceNameWorker
  :feature_category: :plan_provisioning
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: onboarding_add_on_seat_assignment_iterable_trigger
  :worker_name: Onboarding::AddOnSeatAssignmentIterableTriggerWorker
  :feature_category: :onboarding
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: onboarding_create_iterable_trigger
  :worker_name: Onboarding::CreateIterableTriggerWorker
  :feature_category: :onboarding
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: onboarding_progress_tracking
  :worker_name: Onboarding::ProgressTrackingWorker
  :feature_category: :onboarding
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: package_metadata_global_advisory_scan
  :worker_name: PackageMetadata::GlobalAdvisoryScanWorker
  :feature_category: :software_composition_analysis
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: product_analytics_initialize_snowplow_product_analytics
  :worker_name: ProductAnalytics::InitializeSnowplowProductAnalyticsWorker
  :feature_category: :product_analytics
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: product_analytics_move_funnels
  :worker_name: ProductAnalytics::MoveFunnelsWorker
  :feature_category: :product_analytics
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: product_analytics_post_push
  :worker_name: ProductAnalytics::PostPushWorker
  :feature_category: :product_analytics
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: product_analytics_sync_funnels
  :worker_name: ProductAnalytics::SyncFunnelsWorker
  :feature_category: :product_analytics
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: project_import_schedule
  :worker_name: ProjectImportScheduleWorker
  :feature_category: :source_code_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: project_template_export
  :worker_name: ProjectTemplateExportWorker
  :feature_category: :importers
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :memory
  :weight: 1
  :idempotent: false
  :tags:
  - :import_shared_storage
  :queue_namespace:
- :name: projects_repository_destroy
  :worker_name: Projects::RepositoryDestroyWorker
  :feature_category: :importers
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: pull_mirrors_reenable_configuration
  :worker_name: PullMirrors::ReenableConfigurationWorker
  :feature_category: :source_code_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: refresh_license_compliance_checks
  :worker_name: RefreshLicenseComplianceChecksWorker
  :feature_category: :software_composition_analysis
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 2
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: repository_update_mirror
  :worker_name: RepositoryUpdateMirrorWorker
  :feature_category: :source_code_management
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: requirements_management_import_requirements_csv
  :worker_name: RequirementsManagement::ImportRequirementsCsvWorker
  :feature_category: :requirements_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: requirements_management_process_requirements_reports
  :worker_name: RequirementsManagement::ProcessRequirementsReportsWorker
  :feature_category: :requirements_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: sbom_create_occurrences_vulnerabilities
  :worker_name: Sbom::CreateOccurrencesVulnerabilitiesWorker
  :feature_category: :software_composition_analysis
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: sbom_process_transfer_events
  :worker_name: Sbom::ProcessTransferEventsWorker
  :feature_category: :dependency_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: sbom_process_vulnerabilities
  :worker_name: Sbom::ProcessVulnerabilitiesWorker
  :feature_category: :software_composition_analysis
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: sbom_sync_archived_status
  :worker_name: Sbom::SyncArchivedStatusWorker
  :feature_category: :dependency_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: sbom_sync_project_traversal_ids
  :worker_name: Sbom::SyncProjectTraversalIdsWorker
  :feature_category: :dependency_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_elastic_commit_indexer
  :worker_name: Search::Elastic::CommitIndexerWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_elastic_default_branch_changed
  :worker_name: Search::ElasticDefaultBranchChangedWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_elastic_delete
  :worker_name: Search::Elastic::DeleteWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_elastic_group_association_deletion
  :worker_name: Search::ElasticGroupAssociationDeletionWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_elastic_trigger_indexing
  :worker_name: Search::Elastic::TriggerIndexingWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_namespace_index_integrity
  :worker_name: Search::NamespaceIndexIntegrityWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_project_index_integrity
  :worker_name: Search::ProjectIndexIntegrityWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_wiki_elastic_delete_group_wiki
  :worker_name: Search::Wiki::ElasticDeleteGroupWikiWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_default_branch_changed
  :worker_name: Search::Zoekt::DefaultBranchChangedWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_delete_project_event
  :worker_name: Search::Zoekt::DeleteProjectEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_force_update_overprovisioned_index_event
  :worker_name: Search::Zoekt::ForceUpdateOverprovisionedIndexEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_index_mark_as_pending_eviction_event
  :worker_name: Search::Zoekt::IndexMarkAsPendingEvictionEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_index_marked_as_ready_event
  :worker_name: Search::Zoekt::IndexMarkedAsReadyEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_index_marked_as_to_delete_event
  :worker_name: Search::Zoekt::IndexMarkedAsToDeleteEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_index_to_evict_event
  :worker_name: Search::Zoekt::IndexToEvictEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_indexing_task
  :worker_name: Search::Zoekt::IndexingTaskWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_initial_indexing_event
  :worker_name: Search::Zoekt::InitialIndexingEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_lost_node_event
  :worker_name: Search::Zoekt::LostNodeEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_node_with_negative_unclaimed_storage_event
  :worker_name: Search::Zoekt::NodeWithNegativeUnclaimedStorageEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_orphaned_index_event
  :worker_name: Search::Zoekt::OrphanedIndexEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_orphaned_repo_event
  :worker_name: Search::Zoekt::OrphanedRepoEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_project_features_changed_event
  :worker_name: Search::Zoekt::ProjectFeaturesChangedEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_project_marked_as_archived_event
  :worker_name: Search::Zoekt::ProjectMarkedAsArchivedEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_project_transfer
  :worker_name: Search::Zoekt::ProjectTransferWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_project_visibility_changed_event
  :worker_name: Search::Zoekt::ProjectVisibilityChangedEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_repo_marked_as_to_delete_event
  :worker_name: Search::Zoekt::RepoMarkedAsToDeleteEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_repo_to_index_event
  :worker_name: Search::Zoekt::RepoToIndexEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_repo_to_reindex_event
  :worker_name: Search::Zoekt::RepoToReindexEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_saas_rollout_event
  :worker_name: Search::Zoekt::SaasRolloutEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_task_failed_event
  :worker_name: Search::Zoekt::TaskFailedEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: search_zoekt_update_index_used_storage_bytes_event
  :worker_name: Search::Zoekt::UpdateIndexUsedStorageBytesEventWorker
  :feature_category: :global_search
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: secrets_management_provision_project_secrets_manager
  :worker_name: SecretsManagement::ProvisionProjectSecretsManagerWorker
  :feature_category: :secrets_management
  :has_external_dependencies: false
  :urgency: :high
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_analyzer_namespace_statuses_adjustment
  :worker_name: Security::AnalyzerNamespaceStatuses::AdjustmentWorker
  :feature_category: :security_asset_inventories
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_analyzer_namespace_statuses_process_group_deleted_events
  :worker_name: Security::AnalyzerNamespaceStatuses::ProcessGroupDeletedEventsWorker
  :feature_category: :security_asset_inventories
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_analyzers_status_process_archived_events
  :worker_name: Security::AnalyzersStatus::ProcessArchivedEventsWorker
  :feature_category: :security_asset_inventories
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_analyzers_status_process_deleted_events
  :worker_name: Security::AnalyzersStatus::ProcessDeletedEventsWorker
  :feature_category: :security_asset_inventories
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_analyzers_status_process_group_transfer_events
  :worker_name: Security::AnalyzersStatus::ProcessGroupTransferEventsWorker
  :feature_category: :security_asset_inventories
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_analyzers_status_process_project_transfer_events
  :worker_name: Security::AnalyzersStatus::ProcessProjectTransferEventsWorker
  :feature_category: :security_asset_inventories
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_analyzers_status_schedule_setting_changed_update
  :worker_name: Security::AnalyzersStatus::ScheduleSettingChangedUpdateWorker
  :feature_category: :security_asset_inventories
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_analyzers_status_setting_changed_update
  :worker_name: Security::AnalyzersStatus::SettingChangedUpdateWorker
  :feature_category: :security_asset_inventories
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_collect_policies_limit_audit_events
  :worker_name: Security::CollectPoliciesLimitAuditEventsWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_configuration_set_group_secret_push_protection
  :worker_name: Security::Configuration::SetGroupSecretPushProtectionWorker
  :feature_category: :security_testing_configuration
  :has_external_dependencies: false
  :urgency: :high
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_create_security_policy_project
  :worker_name: Security::CreateSecurityPolicyProjectWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :high
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_delete_approval_policy_rules
  :worker_name: Security::DeleteApprovalPolicyRulesWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_delete_orchestration_configuration
  :worker_name: Security::DeleteOrchestrationConfigurationWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_delete_security_policy
  :worker_name: Security::DeleteSecurityPolicyWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_generate_policy_violation_comment
  :worker_name: Security::GeneratePolicyViolationCommentWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_orchestration_configuration_create_bot
  :worker_name: Security::OrchestrationConfigurationCreateBotWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_orchestration_configuration_create_bot_for_namespace
  :worker_name: Security::OrchestrationConfigurationCreateBotForNamespaceWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_orchestration_configuration_remove_bot
  :worker_name: Security::OrchestrationConfigurationRemoveBotWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_orchestration_configuration_remove_bot_for_namespace
  :worker_name: Security::OrchestrationConfigurationRemoveBotForNamespaceWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_orchestration_policy_rule_schedule_namespace
  :worker_name: Security::OrchestrationPolicyRuleScheduleNamespaceWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_persist_security_policies
  :worker_name: Security::PersistSecurityPoliciesWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_pipeline_analyzers_status_update
  :worker_name: Security::PipelineAnalyzersStatusUpdateWorker
  :feature_category: :security_asset_inventories
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_pipeline_execution_policies_run_schedule
  :worker_name: Security::PipelineExecutionPolicies::RunScheduleWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_policies_failed_pipelines_audit
  :worker_name: Security::Policies::FailedPipelinesAuditWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_policies_group_project_transfer
  :worker_name: Security::Policies::GroupProjectTransferWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_policies_group_transfer
  :worker_name: Security::Policies::GroupTransferWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_policies_project_transfer
  :worker_name: Security::Policies::ProjectTransferWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_policies_skip_pipelines_audit
  :worker_name: Security::Policies::SkipPipelinesAuditWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_process_scan_result_policy
  :worker_name: Security::ProcessScanResultPolicyWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_recreate_orchestration_configuration
  :worker_name: Security::RecreateOrchestrationConfigurationWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_refresh_project_policies
  :worker_name: Security::RefreshProjectPoliciesWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_scan_execution_policies_create_pipeline
  :worker_name: Security::ScanExecutionPolicies::CreatePipelineWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :throttled
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: security_scan_execution_policies_rule_schedule
  :worker_name: Security::ScanExecutionPolicies::RuleScheduleWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: security_scan_result_policies_add_approvers_to_rules
  :worker_name: Security::ScanResultPolicies::AddApproversToRulesWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_scan_result_policies_cleanup_merge_request_violations
  :worker_name: Security::ScanResultPolicies::CleanupMergeRequestViolationsWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_scan_result_policies_delete_scan_result_policy_reads
  :worker_name: Security::ScanResultPolicies::DeleteScanResultPolicyReadsWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_scan_result_policies_fallback_behavior_tracking
  :worker_name: Security::ScanResultPolicies::FallbackBehaviorTrackingWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_scan_result_policies_sync_any_merge_request_approval_rules
  :worker_name: Security::ScanResultPolicies::SyncAnyMergeRequestApprovalRulesWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_scan_result_policies_sync_merge_request_approvals
  :worker_name: Security::ScanResultPolicies::SyncMergeRequestApprovalsWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_scan_result_policies_sync_project
  :worker_name: Security::ScanResultPolicies::SyncProjectWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_scan_result_policies_unblock_fail_open_approval_rules
  :worker_name: Security::ScanResultPolicies::UnblockFailOpenApprovalRulesWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_scan_result_policies_unblock_pending_merge_request_violations
  :worker_name: Security::ScanResultPolicies::UnblockPendingMergeRequestViolationsWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_scans_ingest_reports
  :worker_name: Security::Scans::IngestReportsWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_scans_purge_by_job_id
  :worker_name: Security::Scans::PurgeByJobIdWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_secret_detection_gitlab_token_verification
  :worker_name: Security::SecretDetection::GitlabTokenVerificationWorker
  :feature_category: :secret_detection
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_secret_detection_partner_token_verification
  :worker_name: Security::SecretDetection::PartnerTokenVerificationWorker
  :feature_category: :secret_detection
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_sync_linked_pipeline_execution_policy_configs
  :worker_name: Security::SyncLinkedPipelineExecutionPolicyConfigsWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_sync_pipeline_execution_policy_metadata
  :worker_name: Security::SyncPipelineExecutionPolicyMetadataWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_sync_policy
  :worker_name: Security::SyncPolicyWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_sync_policy_event
  :worker_name: Security::SyncPolicyEventWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_sync_policy_violation_comment
  :worker_name: Security::SyncPolicyViolationCommentWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_sync_project_policies
  :worker_name: Security::SyncProjectPoliciesWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_sync_project_policy
  :worker_name: Security::SyncProjectPolicyWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_sync_scan_policies
  :worker_name: Security::SyncScanPoliciesWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_unassign_redundant_policy_configurations
  :worker_name: Security::UnassignRedundantPolicyConfigurationsWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_unenforceable_policy_rules_notification
  :worker_name: Security::UnenforceablePolicyRulesNotificationWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_unenforceable_policy_rules_pipeline_notification
  :worker_name: Security::UnenforceablePolicyRulesPipelineNotificationWorker
  :feature_category: :security_policy_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: security_vulnerability_scanning_process_sbom_scan
  :worker_name: Security::VulnerabilityScanning::ProcessSbomScanWorker
  :feature_category: :software_composition_analysis
  :has_external_dependencies: false
  :urgency: :high
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: set_user_status_based_on_user_cap_setting
  :worker_name: SetUserStatusBasedOnUserCapSettingWorker
  :feature_category: :user_profile
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: status_page_publish
  :worker_name: StatusPage::PublishWorker
  :feature_category: :incident_management
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: sync_seat_link_request
  :worker_name: SyncSeatLinkRequestWorker
  :feature_category: :plan_provisioning
  :has_external_dependencies: true
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: system_access_group_saml_microsoft_group_sync
  :worker_name: SystemAccess::GroupSamlMicrosoftGroupSyncWorker
  :feature_category: :system_access
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: system_access_saml_microsoft_group_sync
  :worker_name: SystemAccess::SamlMicrosoftGroupSyncWorker
  :feature_category: :system_access
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: users_experimental_communication_opt_in
  :worker_name: Users::ExperimentalCommunicationOptInWorker
  :feature_category: :integrations
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerabilities_archival_archive
  :worker_name: Vulnerabilities::Archival::ArchiveWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerabilities_archival_export_export
  :worker_name: Vulnerabilities::Archival::Export::ExportWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerabilities_archival_export_purge
  :worker_name: Vulnerabilities::Archival::Export::PurgeWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerabilities_mark_dropped_as_resolved
  :worker_name: Vulnerabilities::MarkDroppedAsResolvedWorker
  :feature_category: :static_application_security_testing
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerabilities_namespace_historical_statistics_process_transfer_events
  :worker_name: Vulnerabilities::NamespaceHistoricalStatistics::ProcessTransferEventsWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerabilities_namespace_historical_statistics_update_traversal_ids
  :worker_name: Vulnerabilities::NamespaceHistoricalStatistics::UpdateTraversalIdsWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerabilities_namespace_statistics_adjustment
  :worker_name: Vulnerabilities::NamespaceStatistics::AdjustmentWorker
  :feature_category: :security_asset_inventories
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerabilities_namespace_statistics_process_group_delete_events
  :worker_name: Vulnerabilities::NamespaceStatistics::ProcessGroupDeleteEventsWorker
  :feature_category: :security_asset_inventories
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerabilities_namespace_statistics_process_group_transfer_events
  :worker_name: Vulnerabilities::NamespaceStatistics::ProcessGroupTransferEventsWorker
  :feature_category: :security_asset_inventories
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerabilities_namespace_statistics_process_project_delete_events
  :worker_name: Vulnerabilities::NamespaceStatistics::ProcessProjectDeleteEventsWorker
  :feature_category: :security_asset_inventories
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerabilities_namespace_statistics_process_project_transfer_events
  :worker_name: Vulnerabilities::NamespaceStatistics::ProcessProjectTransferEventsWorker
  :feature_category: :security_asset_inventories
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerabilities_process_archived_events
  :worker_name: Vulnerabilities::ProcessArchivedEventsWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerabilities_process_bulk_dismissed_events
  :worker_name: Vulnerabilities::ProcessBulkDismissedEventsWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerabilities_process_bulk_redetected_events
  :worker_name: Vulnerabilities::ProcessBulkRedetectedEventsWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerabilities_process_transfer_events
  :worker_name: Vulnerabilities::ProcessTransferEventsWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerabilities_remove_all_vulnerabilities
  :worker_name: Vulnerabilities::RemoveAllVulnerabilitiesWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerabilities_statistics_adjustment
  :worker_name: Vulnerabilities::Statistics::AdjustmentWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: false
  :tags: []
  :queue_namespace:
- :name: vulnerabilities_update_archived_attribute_of_vulnerability_reads
  :worker_name: Vulnerabilities::UpdateArchivedAttributeOfVulnerabilityReadsWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :memory
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerabilities_update_namespace_ids_of_vulnerability_reads
  :worker_name: Vulnerabilities::UpdateNamespaceIdsOfVulnerabilityReadsWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerabilities_update_traversal_ids_of_vulnerability_statistic
  :worker_name: Vulnerabilities::UpdateTraversalIdsOfVulnerabilityStatisticWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerability_exports_export
  :worker_name: VulnerabilityExports::ExportWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :cpu
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerability_exports_export_deletion
  :worker_name: VulnerabilityExports::ExportDeletionWorker
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: vulnerability_external_issue_links_update_vulnerability_read
  :worker_name: VulnerabilityExternalIssueLinks::UpdateVulnerabilityRead
  :feature_category: :vulnerability_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: work_items_rolledup_dates_bulk_update_handler
  :worker_name: WorkItems::RolledupDates::BulkUpdateHandler
  :feature_category: :portfolio_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: work_items_rolledup_dates_update_milestone_related_work_item_dates_event_handler
  :worker_name: WorkItems::RolledupDates::UpdateMilestoneRelatedWorkItemDatesEventHandler
  :feature_category: :portfolio_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: work_items_rolledup_dates_update_multiple_rolledup_dates
  :worker_name: WorkItems::RolledupDates::UpdateMultipleRolledupDatesWorker
  :feature_category: :portfolio_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: work_items_rolledup_dates_update_rolledup_dates
  :worker_name: WorkItems::RolledupDates::UpdateRolledupDatesWorker
  :feature_category: :portfolio_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: work_items_rolledup_dates_update_rolledup_dates_event_handler
  :worker_name: WorkItems::RolledupDates::UpdateRolledupDatesEventHandler
  :feature_category: :portfolio_management
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: work_items_update_parent_objectives_progress
  :worker_name: WorkItems::UpdateParentObjectivesProgressWorker
  :feature_category: :team_planning
  :has_external_dependencies: false
  :urgency: :high
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: work_items_weights_update_rolled_up_weights_event_handler
  :worker_name: WorkItems::Weights::UpdateRolledUpWeightsEventHandler
  :feature_category: :team_planning
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
- :name: work_items_weights_update_weights
  :worker_name: WorkItems::Weights::UpdateWeightsWorker
  :feature_category: :team_planning
  :has_external_dependencies: false
  :urgency: :low
  :resource_boundary: :unknown
  :weight: 1
  :idempotent: true
  :tags: []
  :queue_namespace:
