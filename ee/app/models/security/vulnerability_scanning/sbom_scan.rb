# frozen_string_literal: true

module Security
  module VulnerabilityScanning
    class SbomScan < ::SecApplicationRecord
      self.table_name = :sbom_vulnerability_scans

      EXPIRED_AGE = 2.days

      belongs_to :project
      belongs_to :build, class_name: 'Ci::Build'

      validates :project, :build, presence: true
      validates :sbom_file, presence: true, if: -> { running? || finished? }
      validates :result_file, presence: true, if: -> { finished? }
      validates :sbom_file_final_path, length: { maximum: 1024 }
      validates :error_message, length: { maximum: 1024 }

      # This model holds two file attachments and uses a custom uploader that don't create Upload records.
      # See https://gitlab.com/gitlab-org/gitlab/-/issues/425484 about why creating Upload records is now discouraged.
      mount_uploader :sbom_file, Security::VulnerabilityScanning::SbomScanUploader
      mount_uploader :result_file, Security::VulnerabilityScanning::SbomScanUploader

      # Avoid storing large files during DB transaction. We call these methods manually instead.
      skip_callback :save, :after, :store_sbom_file!
      skip_callback :save, :after, :store_result_file!

      # Setting default store dynamically based on the instance storage configuration
      attribute :sbom_file_store, default: -> { ::Security::VulnerabilityScanning::SbomScanUploader.default_store }
      attribute :result_file_store, default: -> { ::Security::VulnerabilityScanning::SbomScanUploader.default_store }

      scope :expired, -> { where(created_at: ...EXPIRED_AGE.ago) }

      state_machine :status, initial: :created do
        state :created, value: 0
        state :running, value: 1
        state :finished, value: 2
        state :failed, value: -1

        event :start do
          transition created: :running
        end

        event :finish do
          transition running: :finished
        end

        event :failed do
          transition [:created, :running] => :failed
        end

        event :reset_state do
          transition running: :created
        end
      end

      def fail_with_error_message!(message)
        self.error_message = message&.truncate(1024)
        failed!
      end

      def delete_files_from_storage
        sbom_file.remove!
        result_file.remove!
        true
      rescue StandardError => e
        Gitlab::ErrorTracking.track_exception(e)
        false
      end
    end
  end
end
