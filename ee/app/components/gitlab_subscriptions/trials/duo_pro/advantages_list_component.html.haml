.md:gl-row-span-2.md:gl-col-span-3.md:gl-py-13.gl-col-span-6{ data: { testid: 'trial-reassurances-column' } }
  .gl-mb-6.gl-text-left.gl-text-neutral-50
    = s_('DuoProTrial|GitLab Duo Pro is designed to make teams more efficient throughout the software development lifecycle with:')

  %ul.gl-pl-0.gl-mb-6.gl-text-neutral-50
    - advantages.each do |advantage|
      %li.gl-flex.gl-mb-3{ data: { testid: 'advantage-item' } }
        %span.gl-mr-3= sprite_icon 'check-circle', css_class: 'gl-icon gl-fill-icon-success'
        = advantage
  .gl-mt-5.gl-text-sm.gl-text-neutral-50
    = s_('DuoProTrial|GitLab Duo Pro is only available for purchase for Premium and Ultimate users.')
