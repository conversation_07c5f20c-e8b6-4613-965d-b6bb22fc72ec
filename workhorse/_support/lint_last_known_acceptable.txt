internal/channel/channel.go:128:31: response body must be closed (bodyclose)
internal/zipartifacts/open_archive.go:74:28: response body must be closed (bodyclose)
internal/git/diff.go:1: 1-47 lines are duplicate of `internal/git/format-patch.go:1-48` (dupl)
internal/git/format-patch.go:1: 1-48 lines are duplicate of `internal/git/diff.go:1-47` (dupl)
internal/testhelper/gitaly.go:277: 277-296 lines are duplicate of `internal/testhelper/gitaly.go:315-336` (dupl)
internal/testhelper/gitaly.go:315: 315-336 lines are duplicate of `internal/testhelper/gitaly.go:338-357` (dupl)
internal/testhelper/gitaly.go:338: 338-357 lines are duplicate of `internal/testhelper/gitaly.go:277-296` (dupl)
internal/git/io.go:141:22: Error return value of `tempfile.Close` is not checked (errcheck)
internal/git/io.go:173:17: Error return value of `tempfile.Close` is not checked (errcheck)
internal/git/receive-pack.go:21:16: Error return value of `cw.Flush` is not checked (errcheck)
internal/git/upload-pack.go:37:16: Error return value of `cw.Flush` is not checked (errcheck)
internal/imageresizer/image_resizer.go:182:30: Error return value of `imageFile.reader.Close` is not checked (errcheck)
internal/imageresizer/image_resizer.go:205:32: Error return value of `command.KillProcessGroup` is not checked (errcheck)
internal/imageresizer/image_resizer.go:356:17: Error return value of `res.Body.Close` is not checked (errcheck)
internal/imageresizer/image_resizer.go:369:13: Error return value of `file.Close` is not checked (errcheck)
cmd/gitlab-workhorse/main.go:202:6: Function 'run' has too many statements (59 > 40) (funlen)
internal/imageresizer/image_resizer.go:152:19: Function 'Inject' is too long (61 > 60) (funlen)
internal/upload/destination/destination.go:117:6: Function 'Upload' is too long (62 > 60) (funlen)
internal/upstream/routes.go:277:6: Function 'configureRoutes' is too long (284 > 60) (funlen)
internal/git/archive.go:67:1: cognitive complexity 23 of func `(*archive).Inject` is high (> 20) (gocognit)
internal/transport/transport.go:147:1: cognitive complexity 52 of func `validateIPAddress` is high (> 20) (gocognit)
internal/upload/artifacts_upload_test.go:49:1: cognitive complexity 32 of func `testArtifactsUploadServer` is high (> 20) (gocognit)
internal/git/io_test.go:89:14: string `test data` has 3 occurrences, make it a constant (goconst)
internal/git/error.go:38:4: singleCaseSwitch: should rewrite switch statement to if statement (gocritic)
cmd/gitlab-workhorse/listener.go:26:16: G402: TLS MinVersion too low. (gosec)
cmd/gitlab-workhorse/main.go:298:10: G112: Potential Slowloris Attack because ReadHeaderTimeout is not configured in the http.Server (gosec)
internal/ai_assist/duoworkflow/actions.go:79:24: G115: integer overflow conversion int -> int32 (gosec)
internal/ai_assist/duoworkflow/client.go:56:73: G402: TLS MinVersion too low. (gosec)
internal/api/channel_settings.go:57:28: G402: TLS MinVersion too low. (gosec)
internal/config/config.go:267:18: G204: Subprocess launched with variable (gosec)
internal/config/config.go:359:8: G101: Potential hardcoded credentials (gosec)
internal/imageresizer/image_resizer.go:184:35: G115: integer overflow conversion uint -> int (gosec)
internal/imageresizer/image_resizer.go:266:28: G115: integer overflow conversion uint64 -> int64 (gosec)
internal/imageresizer/image_resizer.go:274:41: G115: integer overflow conversion uint32 -> int32 (gosec)
internal/imageresizer/image_resizer.go:316:46: G115: integer overflow conversion uint -> int (gosec)
internal/imageresizer/image_resizer.go:362:15: G304: Potential file inclusion via variable (gosec)
internal/testhelper/testhelper.go:245:21: G302: Expect file permissions to be 0600 or less (gosec)
internal/upload/artifacts_uploader.go:82:11: G204: Subprocess launched with a potential tainted input or cmd arguments (gosec)
internal/upload/destination/multi_hash.go:4:2: G501: Blocklisted import crypto/md5: weak cryptographic primitive (gosec)
internal/upload/destination/multi_hash.go:5:2: G505: Blocklisted import crypto/sha1: weak cryptographic primitive (gosec)
internal/upload/destination/objectstore/test/objectstore_stub.go:4:2: G501: Blocklisted import crypto/md5: weak cryptographic primitive (gosec)
internal/upload/destination/objectstore/test/objectstore_stub.go:169:13: G401: Use of weak cryptographic primitive (gosec)
internal/upload/destination/objectstore/uploader.go:5:2: G501: Blocklisted import crypto/md5: weak cryptographic primitive (gosec)
internal/upload/destination/objectstore/uploader.go:95:12: G401: Use of weak cryptographic primitive (gosec)
internal/upload/exif/exif.go:103:10: G204: Subprocess launched with variable (gosec)
internal/zipartifacts/metadata.go:118:54: G115: integer overflow conversion int -> uint32 (gosec)
cmd/gitlab-zip-metadata/limit/reader.go:24:5: redefines-builtin-id: redefinition of the built-in function max (revive)
internal/git/blob.go:21:5: exported: exported var SendBlob should have comment or be unexported (revive)
internal/git/diff.go:22:5: exported: exported var SendDiff should have comment or be unexported (revive)
internal/git/error.go:29:45: context-as-argument: context.Context should be the first parameter of a function (revive)
internal/git/format-patch.go:22:5: exported: exported var SendPatch should have comment or be unexported (revive)
internal/git/git-http.go:21:2: exported: comment on exported const GitConfigShowAllRefs should be of the form "GitConfigShowAllRefs ..." (revive)
internal/git/git-http.go:26:1: exported: exported function ReceivePack should have comment or be unexported (revive)
internal/git/git-http.go:30:1: exported: exported function UploadPack should have comment or be unexported (revive)
internal/git/info-refs.go:20:1: exported: exported function GetInfoRefsHandler should have comment or be unexported (revive)
internal/git/responsewriter.go:41:6: exported: exported type HTTPResponseWriter should have comment or be unexported (revive)
internal/git/responsewriter.go:45:1: exported: exported function NewHTTPResponseWriter should have comment or be unexported (revive)
internal/git/responsewriter.go:52:1: exported: exported method HTTPResponseWriter.Log should have comment or be unexported (revive)
internal/git/snapshot.go:27:2: exported: exported var SendSnapshot should have comment or be unexported (revive)
internal/imageresizer/image_resizer.go:1:1: package-comments: should have a package comment (revive)
internal/imageresizer/image_resizer.go:33:6: exported: exported type Resizer should have comment or be unexported (revive)
internal/imageresizer/image_resizer.go:144:1: exported: exported function NewResizer should have comment or be unexported (revive)
internal/upstream/upstream.go:285:56: redefines-builtin-id: redefinition of the built-in type error (revive)
cmd/gitlab-workhorse/gitaly_integration_test.go:96:36: QF1008: could remove embedded field "RepositoryServiceClient" from selector (staticcheck)
cmd/gitlab-workhorse/gitaly_integration_test.go:103:13: QF1001: could apply De Morgan's law (staticcheck)
cmd/gitlab-workhorse/gitaly_integration_test.go:120:36: QF1008: could remove embedded field "RepositoryServiceClient" from selector (staticcheck)
cmd/gitlab-workhorse/gitaly_integration_test.go:127:13: QF1001: could apply De Morgan's law (staticcheck)
cmd/gitlab-workhorse/gitaly_test.go:165:16: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
cmd/gitlab-workhorse/gitaly_test.go:305:16: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
cmd/gitlab-workhorse/gitaly_test.go:476:16: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
cmd/gitlab-workhorse/gitaly_test.go:596:16: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
cmd/gitlab-workhorse/gitaly_test.go:661:16: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
cmd/gitlab-workhorse/gitaly_test.go:684:16: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
cmd/gitlab-workhorse/gitaly_test.go:707:16: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
cmd/gitlab-workhorse/gitaly_test.go:752:16: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
internal/bodylimit/bodylimit_test.go:606:4: QF1003: could use tagged switch on tt.expectedCode (staticcheck)
internal/git/archive_cleaner.go:61:2: QF1002: could use tagged switch on s.path (staticcheck)
internal/git/io.go:115:7: QF1008: could remove embedded field "busyReader" from selector (staticcheck)
internal/httprs/httprs_test.go:89:4: QF1012: Use fmt.Fprintf(...) instead of WriteString(fmt.Sprintf(...)) (staticcheck)
internal/imageresizer/image_resizer.go:199:76: QF1008: could remove embedded field "Config" from selector (staticcheck)
internal/lsif_transformer/parser/parser_test.go:64:33: QF1008: could remove embedded field "Reader" from selector (staticcheck)
internal/proxy/proxy.go:142:14: SA6002: argument should be pointer-like to avoid allocations (staticcheck)
internal/testhelper/gitaly.go:77:4: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
internal/testhelper/gitaly.go:78:10: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
internal/testhelper/gitaly.go:107:4: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
internal/testhelper/gitaly.go:108:10: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
internal/testhelper/gitaly.go:158:4: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
internal/testhelper/gitaly.go:159:10: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
internal/testhelper/gitaly.go:205:4: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
internal/testhelper/gitaly.go:206:10: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
internal/testhelper/gitaly.go:244:4: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
internal/testhelper/gitaly.go:245:10: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
internal/testhelper/gitaly.go:278:4: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
internal/testhelper/gitaly.go:279:10: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
internal/testhelper/gitaly.go:316:4: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
internal/testhelper/gitaly.go:317:10: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
internal/testhelper/gitaly.go:339:4: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
internal/testhelper/gitaly.go:340:10: QF1008: could remove embedded field "WaitGroup" from selector (staticcheck)
internal/upstream/routes.go:661:7: QF1008: could remove embedded field "Config" from selector (staticcheck)
internal/upstream/upstream.go:217:3: QF1006: could lift into loop condition (staticcheck)
internal/upstream/upstream.go:242:2: QF1007: could merge conditional assignment into variable declaration (staticcheck)
cmd/gitlab-workhorse/main_test.go:224:4: empty: use require.Empty (testifylint)
cmd/gitlab-workhorse/proxy_test.go:48:3: regexp: remove unnecessary regexp.MustCompile (testifylint)
cmd/gitlab-workhorse/proxy_test.go:128:2: regexp: remove unnecessary regexp.MustCompile (testifylint)
internal/git/upload-pack_test.go:72:2: error-is-as: use require.ErrorIs (testifylint)
internal/helper/nginx/nginx_test.go:27:2: empty: use require.Empty (testifylint)
internal/lsif_transformer/parser/hovers_test.go:16:2: encoded-compare: use require.JSONEq (testifylint)
internal/senddata/contentprocessor/contentprocessor_test.go:29:2: empty: use require.Empty (testifylint)
internal/senddata/contentprocessor/contentprocessor_test.go:30:2: empty: use require.Empty (testifylint)
internal/upload/destination/multi_hash_test.go:42:3: len: use require.Len (testifylint)
internal/upload/destination/objectstore/gocloud_object_test.go:42:2: empty: use require.Empty (testifylint)
internal/upload/destination/objectstore/test/gocloud_stub.go:46:2: empty: use require.Empty (testifylint)
internal/upload/uploads_test.go:527:3: formatter: using msgAndArgs with non-string first element (msg) causes panic (testifylint)
internal/upload/uploads_test.go:545:3: formatter: using msgAndArgs with non-string first element (msg) causes panic (testifylint)
internal/upstream/routes.go:217:74: (*upstream).wsRoute - matchers always receives nil (unparam)
