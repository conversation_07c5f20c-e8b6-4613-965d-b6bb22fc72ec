PATH
  remote: gems/activerecord-gitlab
  specs:
    activerecord-gitlab (0.2.0)
      activerecord (>= 7)

PATH
  remote: gems/bundler-checksum
  specs:
    bundler-checksum (0.1.0)
      bundler

PATH
  remote: gems/csv_builder
  specs:
    csv_builder (0.1.0)

PATH
  remote: gems/error_tracking_open_api
  specs:
    error_tracking_open_api (1.0.0)
      typhoeus (~> 1.0, >= 1.0.1)

PATH
  remote: gems/gitlab-active-context
  specs:
    gitlab-active-context (0.0.1)
      activerecord
      activesupport
      connection_pool
      elasticsearch
      opensearch-ruby
      pg
      zeitwerk

PATH
  remote: gems/gitlab-backup-cli
  specs:
    gitlab-backup-cli (0.0.1)
      activerecord (>= 7)
      activesupport (>= 7)
      addressable (~> 2.8)
      bigdecimal (~> 3.1)
      concurrent-ruby (~> 1.1)
      faraday (~> 2)
      google-cloud-storage_transfer (~> 1.2.0)
      google-protobuf (~> 3.25, >= 3.25.3)
      googleauth (~> 1.14)
      grpc (~> 1.74.0)
      json (~> 2.7)
      jwt (~> 2.5)
      logger (~> 1.5)
      minitest (~> 5.11.0)
      mutex_m (~> 0.3)
      parallel (~> 1.19)
      pg (~> 1.6.1)
      rack (~> 2.2.9)
      rainbow (~> 3.0)
      rexml (~> 3.4.0)
      thor (~> 1.3)

PATH
  remote: gems/gitlab-housekeeper
  specs:
    gitlab-housekeeper (0.1.0)
      activesupport
      awesome_print
      httparty
      rubocop

PATH
  remote: gems/gitlab-http
  specs:
    gitlab-http (0.1.0)
      activesupport (~> 7)
      concurrent-ruby (~> 1.2)
      httparty (~> 0.21)
      ipaddress (~> 0.8.3)
      net-http (= 0.6.0)
      railties (~> 7)

PATH
  remote: gems/gitlab-rspec_flaky
  specs:
    gitlab-rspec_flaky (0.1.0)
      activesupport (>= 6.1, < 8)
      rspec (~> 3.0)

PATH
  remote: gems/gitlab-rspec
  specs:
    gitlab-rspec (0.1.0)
      activerecord (>= 6.1, < 8)
      activesupport (>= 6.1, < 8)
      rspec (~> 3.0)

PATH
  remote: gems/gitlab-safe_request_store
  specs:
    gitlab-safe_request_store (0.1.0)
      rack (~> 2.2.8)
      request_store

PATH
  remote: gems/gitlab-schema-validation
  specs:
    gitlab-schema-validation (0.1.0)
      diffy
      pg_query

PATH
  remote: gems/gitlab-utils
  specs:
    gitlab-utils (0.1.0)
      actionview (>= *******)
      activesupport (>= *******)
      addressable (~> 2.8)
      rake (~> 13.0)

PATH
  remote: gems/ipynbdiff
  specs:
    ipynbdiff (0.4.8)
      diffy (~> 3.4)
      oj (~> 3.16, >= 3.16.10)

PATH
  remote: gems/mail-smtp_pool
  specs:
    mail-smtp_pool (0.1.0)
      connection_pool (~> 2.0)
      mail (~> 2.8)

PATH
  remote: vendor/gems/devise-pbkdf2-encryptable
  specs:
    devise-pbkdf2-encryptable (0.0.0)
      devise (~> 4.0)
      devise-two-factor (~> 4.1.1)

PATH
  remote: vendor/gems/diff_match_patch
  specs:
    diff_match_patch (0.1.0)

PATH
  remote: vendor/gems/gitlab-*********************client
  specs:
    gitlab-*********************client (0.2)
      grpc

PATH
  remote: vendor/gems/gitlab-topology-service-client
  specs:
    gitlab-topology-service-client (0.1)
      google-protobuf (~> 3)
      grpc

PATH
  remote: vendor/gems/microsoft_graph_mailer
  specs:
    microsoft_graph_mailer (0.1.0)
      mail (~> 2.7)
      oauth2 (>= 1.4.4, < 3)

PATH
  remote: vendor/gems/omniauth-gitlab
  specs:
    omniauth-gitlab (4.0.0)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.8)

PATH
  remote: vendor/gems/omniauth-salesforce
  specs:
    omniauth-salesforce (1.0.5)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.0)

PATH
  remote: vendor/gems/omniauth_crowd
  specs:
    omniauth_crowd (2.4.0)
      activesupport
      nokogiri (>= 1.4.4)
      omniauth (~> 2.0)

PATH
  remote: vendor/gems/sidekiq-reliable-fetch
  specs:
    gitlab-sidekiq-fetcher (0.12.1)
      json (>= 2.5)
      sidekiq (~> 7.0)

PATH
  remote: vendor/gems/sidekiq
  specs:
    sidekiq (7.3.9)
      base64
      connection_pool (>= 2.3.0)
      logger
      rack (>= 2.2.4)
      redis-client (>= 0.22.2)

GEM
  remote: https://rubygems.org/
  specs:
    CFPropertyList (3.0.7)
      base64
      nkf
      rexml
    RedCloth (4.3.4)
    acme-client (2.0.25)
      base64 (~> 0.2)
      faraday (>= 1.0, < 3.0.0)
      faraday-retry (>= 1.0, < 3.0.0)
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      mutex_m
      securerandom (>= 0.3)
      tzinfo (~> 2.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    aes_key_wrap (1.1.0)
    akismet (3.0.0)
    aliyun-sdk (0.8.0)
      nokogiri (~> 1.6)
      rest-client (~> 2.0)
    amatch (0.4.1)
      mize
      tins (~> 1.0)
    android_key_attestation (0.3.0)
    apollo_upload_server (2.1.6)
      actionpack (>= 6.1.6)
      graphql (>= 1.8)
    app_store_connect (0.38.0)
      activesupport (>= 6.0.0)
      jwt (>= 1.4)
      zeitwerk (>= 2.6.7)
    arr-pm (0.0.12)
    asciidoctor (2.0.23)
    asciidoctor-include-ext (0.4.0)
      asciidoctor (>= 1.5.6, < 3.0.0)
    asciidoctor-kroki (0.10.0)
      asciidoctor (~> 2.0)
    asciidoctor-plantuml (0.0.16)
      asciidoctor (>= 2.0.17, < 3.0.0)
    ast (2.4.2)
    async (2.27.0)
      console (~> 1.29)
      fiber-annotation
      io-event (~> 1.11)
      metrics (~> 0.12)
      traces (~> 0.15)
    atlassian-jwt (0.2.1)
      jwt (~> 2.1)
    attr_encrypted (4.2.0)
      encryptor (~> 3.0.0)
    attr_required (1.0.2)
    awesome_print (1.9.2)
    awrence (1.2.1)
    aws-eventstream (1.3.0)
    aws-partitions (1.1001.0)
    aws-sdk-cloudformation (1.133.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-core (3.226.3)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-kms (1.76.0)
      aws-sdk-core (~> 3, >= 3.188.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3 (1.193.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.9.1)
      aws-eventstream (~> 1, >= 1.0.2)
    axe-core-api (4.10.3)
      dumb_delegator
      ostruct
      virtus
    axe-core-rspec (4.10.3)
      axe-core-api (= 4.10.3)
      dumb_delegator
      ostruct
      virtus
    axiom-types (0.1.1)
      descendants_tracker (~> 0.0.4)
      ice_nine (~> 0.11.0)
      thread_safe (~> 0.3, >= 0.3.1)
    babosa (2.0.0)
    backport (1.2.0)
    base32 (0.3.4)
    base64 (0.2.0)
    batch-loader (2.0.5)
    bcrypt (3.1.20)
    benchmark (0.4.0)
    benchmark-ips (2.14.0)
    benchmark-malloc (0.2.0)
    benchmark-memory (0.2.0)
      memory_profiler (~> 1)
    benchmark-perf (0.6.0)
    benchmark-trend (0.4.0)
    better_errors (2.10.1)
      erubi (>= 1.0.0)
      rack (>= 0.9.0)
      rouge (>= 1.0.0)
    bigdecimal (3.1.7)
    bindata (2.4.11)
    binding_of_caller (1.0.0)
      debug_inspector (>= 0.0.1)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    browser (5.3.1)
    builder (3.2.4)
    bullet (8.0.8)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    byebug (12.0.0)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    capybara-screenshot (1.0.26)
      capybara (>= 1.0, < 4)
      launchy
    carrierwave (1.3.4)
      activemodel (>= 4.0.0)
      activesupport (>= 4.0.0)
      mime-types (>= 1.16)
      ssrf_filter (~> 1.0, < 1.1.0)
    cbor (*******)
    character_set (1.8.0)
    charlock_holmes (0.7.9)
    chef-config (18.3.0)
      addressable
      chef-utils (= 18.3.0)
      fuzzyurl
      mixlib-config (>= 2.2.12, < 4.0)
      mixlib-shellout (>= 2.0, < 4.0)
      tomlrb (~> 1.2)
    chef-utils (18.3.0)
      concurrent-ruby
    chunky_png (1.4.0)
    circuitbox (2.0.0)
    citrus (3.0.2)
    claide (1.1.0)
    claide-plugins (0.9.2)
      cork
      nap
      open4 (~> 1.3)
    click_house-client (0.5.1)
      activerecord (>= 7.0, < 9.0)
      activesupport (>= 7.0, < 9.0)
      addressable (~> 2.8)
      json (~> 2.7)
    coderay (1.1.3)
    coercible (1.0.0)
      descendants_tracker (~> 0.0.1)
    colored2 (3.1.2)
    commonmarker (0.23.11)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    console (1.29.2)
      fiber-annotation
      fiber-local (~> 1.1)
      json
    cork (0.3.0)
      colored2 (~> 3.1)
    cose (1.3.0)
      cbor (~> 0.5.9)
      openssl-signature_algorithm (~> 1.0)
    countries (4.0.1)
      i18n_data (~> 0.13.0)
      sixarm_ruby_unaccent (~> 1.1)
    coverband (6.1.5)
      base64
      redis (>= 3.0)
    crack (0.4.3)
      safe_yaml (~> 1.0.0)
    crass (1.0.6)
    creole (0.5.0)
    css_parser (1.14.0)
      addressable
    cssbundling-rails (1.4.3)
      railties (>= 6.0.0)
    csv (3.3.0)
    cvss-suite (3.3.0)
    danger (9.4.2)
      claide (~> 1.0)
      claide-plugins (>= 0.9.2)
      colored2 (~> 3.1)
      cork (~> 0.1)
      faraday (>= 0.9.0, < 3.0)
      faraday-http-cache (~> 2.0)
      git (~> 1.13)
      kramdown (~> 2.3)
      kramdown-parser-gfm (~> 1.0)
      no_proxy_fix
      octokit (>= 4.0)
      terminal-table (>= 1, < 4)
    danger-gitlab (8.0.0)
      danger
      gitlab (~> 4.2, >= 4.2.0)
    database_cleaner-active_record (2.2.1)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.4.1)
    deb_version (1.0.2)
    debug (1.11.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    debug_inspector (1.1.0)
    deckar01-task_list (2.3.4)
      html-pipeline (~> 2.0)
    declarative (0.0.20)
    declarative_policy (2.0.1)
    deprecation_toolkit (2.2.4)
      activesupport (>= 6.1)
    derailed_benchmarks (2.2.1)
      base64
      benchmark-ips (~> 2)
      bigdecimal
      drb
      get_process_mem
      heapy (~> 0)
      logger
      memory_profiler (>= 0, < 2)
      mini_histogram (>= 0.3.0)
      mutex_m
      ostruct
      rack (>= 1)
      rack-test
      rake (> 10, < 14)
      ruby-statistics (>= 4.0.1)
      ruby2_keywords
      thor (>= 0.19, < 2)
    descendants_tracker (0.0.4)
      thread_safe (~> 0.3, >= 0.3.1)
    devfile (0.4.7)
    device_detector (1.1.3)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise-two-factor (4.1.1)
      activesupport (~> 7.0)
      attr_encrypted (>= 1.3, < 5, != 2)
      devise (~> 4.0)
      railties (~> 7.0)
      rotp (~> 6.0)
    diff-lcs (1.5.0)
    diffy (3.4.4)
    digest-crc (0.6.5)
      rake (>= 12.0.0, < 14.0.0)
    docile (1.4.0)
    domain_name (0.5.20190701)
      unf (>= 0.0.5, < 1.0.0)
    doorkeeper (5.8.2)
      railties (>= 5)
    doorkeeper-device_authorization_grant (1.0.3)
      doorkeeper (~> 5.5)
    doorkeeper-openid_connect (1.8.11)
      doorkeeper (>= 5.5, < 5.9)
      jwt (>= 2.5)
      ostruct (>= 0.5)
    dotenv (2.7.6)
    drb (2.2.3)
    dry-cli (1.0.0)
    dry-core (1.0.1)
      concurrent-ruby (~> 1.0)
      zeitwerk (~> 2.6)
    dry-inflector (1.0.0)
    dry-logic (1.5.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.0, < 2)
      zeitwerk (~> 2.6)
    dry-types (1.7.1)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.0)
      dry-inflector (~> 1.0)
      dry-logic (~> 1.4)
      zeitwerk (~> 2.6)
    dumb_delegator (1.0.0)
    duo_api (1.4.0)
    ed25519 (1.4.0)
    elasticsearch (7.17.11)
      elasticsearch-api (= 7.17.11)
      elasticsearch-transport (= 7.17.11)
    elasticsearch-api (7.17.11)
      multi_json
    elasticsearch-model (7.2.1)
      activesupport (> 3)
      elasticsearch (~> 7)
      hashie
    elasticsearch-rails (7.2.1)
    elasticsearch-transport (7.17.11)
      base64
      faraday (>= 1, < 3)
      multi_json
    email_reply_trimmer (0.1.6)
    email_spec (2.3.0)
      htmlentities (~> 4.3.3)
      launchy (>= 2.1, < 4.0)
      mail (~> 2.7)
    email_validator (2.2.4)
      activemodel
    encryptor (3.0.0)
    erubi (1.12.0)
    escape_utils (1.3.0)
    et-orbi (1.2.11)
      tzinfo
    ethon (0.16.0)
      ffi (>= 1.15.0)
    excon (1.3.0)
      logger
    execjs (2.8.1)
    expgen (0.1.1)
      parslet
    expression_parser (0.9.0)
    extended-markdown-filter (0.7.0)
      html-pipeline (~> 2.9)
    factory_bot (6.5.0)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.5.0)
      factory_bot (~> 6.5)
      railties (>= 6.1.0)
    faraday (2.13.4)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-follow_redirects (0.3.0)
      faraday (>= 1, < 3)
    faraday-http-cache (2.5.0)
      faraday (>= 0.8)
    faraday-multipart (1.1.1)
      multipart-post (~> 2.0)
    faraday-net_http (3.1.0)
      net-http
    faraday-net_http_persistent (2.1.0)
      faraday (~> 2.5)
      net-http-persistent (~> 4.0)
    faraday-retry (2.2.1)
      faraday (~> 2.0)
    faraday-typhoeus (1.1.0)
      faraday (~> 2.0)
      typhoeus (~> 1.4)
    faraday_middleware-aws-sigv4 (1.0.1)
      aws-sigv4 (~> 1.0)
      faraday (>= 2.0, < 3)
    fast_blank (1.0.1)
    fast_gettext (4.1.0)
      prime
      racc
    ffaker (2.24.0)
    ffi (1.17.2)
    ffi-compiler (1.0.1)
      ffi (>= 1.0.0)
      rake
    ffi-yajl (2.6.0)
      libyajl2 (>= 1.2)
    fiber-annotation (0.2.0)
    fiber-local (1.1.0)
      fiber-storage
    fiber-storage (0.1.2)
    find_a_port (1.0.1)
    flipper (0.28.3)
      concurrent-ruby (< 2)
    flipper-active_record (0.28.3)
      activerecord (>= 4.2, < 8)
      flipper (~> 0.28.3)
    flipper-active_support_cache_store (0.28.3)
      activesupport (>= 4.2, < 8)
      flipper (~> 0.28.3)
    fog-aliyun (0.4.0)
      addressable (~> 2.8.0)
      aliyun-sdk (~> 0.8.0)
      fog-core
      fog-json
      ipaddress (~> 0.8)
      xml-simple (~> 1.1)
    fog-aws (3.33.0)
      base64 (>= 0.2, < 0.4)
      fog-core (~> 2.6)
      fog-json (~> 1.1)
      fog-xml (~> 0.1)
    fog-core (2.6.0)
      builder
      excon (~> 1.0)
      formatador (>= 0.2, < 2.0)
      mime-types
    fog-google (1.25.0)
      addressable (>= 2.7.0)
      fog-core (~> 2.5)
      fog-json (~> 1.2)
      fog-xml (~> 0.1.0)
      google-apis-compute_v1 (~> 0.53)
      google-apis-dns_v1 (~> 0.28)
      google-apis-iamcredentials_v1 (~> 0.15)
      google-apis-monitoring_v3 (~> 0.37)
      google-apis-pubsub_v1 (~> 0.30)
      google-apis-sqladmin_v1beta4 (~> 0.38)
      google-apis-storage_v1 (>= 0.19, < 1)
      google-cloud-env (>= 1.2, < 3.0)
    fog-json (1.2.0)
      fog-core
      multi_json (~> 1.10)
    fog-local (0.9.0)
      fog-core (>= 1.27, < 3.0)
    fog-xml (0.1.5)
      fog-core
      nokogiri (>= 1.5.11, < 2.0.0)
    formatador (0.2.5)
    forwardable (1.3.3)
    fugit (1.11.2)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    fuzzyurl (0.9.0)
    gapic-common (0.20.0)
      faraday (>= 1.9, < 3.a)
      faraday-retry (>= 1.0, < 3.a)
      google-protobuf (~> 3.14)
      googleapis-common-protos (>= 1.3.12, < 2.a)
      googleapis-common-protos-types (>= 1.3.1, < 2.a)
      googleauth (~> 1.0)
      grpc (~> 1.36)
    gdk-toogle (0.9.5)
      haml
      rails (>= *******)
    gemoji (3.0.1)
    get_process_mem (0.2.7)
      ffi (~> 1.0)
    gettext (3.5.1)
      erubi
      locale (>= 2.0.5)
      prime
      racc
      text (>= 1.3.0)
    gettext_i18n_rails (1.13.0)
      fast_gettext (>= 0.9.0)
    git (1.19.1)
      addressable (~> 2.8)
      rchardet (~> 1.8)
    gitaly (18.2.1)
      grpc (~> 1.0)
    gitlab (4.19.0)
      httparty (~> 0.20)
      terminal-table (>= 1.5.1)
    gitlab-chronic (0.10.6)
      numerizer (~> 0.2)
    gitlab-cloud-connector (1.29.0)
      activesupport (~> 7.0)
      jwt (~> 2.9)
    gitlab-crystalball (1.1.1)
      git (< 4)
      ostruct (< 1)
    gitlab-dangerfiles (4.10.0)
      danger (>= 9.3.0)
      danger-gitlab (>= 8.0.0)
      rake (~> 13.0)
    gitlab-experiment (0.9.1)
      activesupport (>= 3.0)
      request_store (>= 1.0)
    gitlab-fog-azure-rm (2.2.0)
      faraday (~> 2.0)
      faraday-follow_redirects (~> 0.3.0)
      faraday-net_http_persistent (~> 2.0)
      fog-core (~> 2.1)
      fog-json (~> 1.2)
      mime-types
      net-http-persistent (~> 4.0)
      nokogiri (~> 1, >= 1.10.8)
    gitlab-glfm-markdown (0.0.33)
      rb_sys (~> 0.9.109)
    gitlab-kas-grpc (18.2.1)
      grpc (~> 1.0)
    gitlab-labkit (0.40.0)
      actionpack (>= 5.0.0, < 8.1.0)
      activesupport (>= 5.0.0, < 8.1.0)
      google-protobuf (~> 3)
      grpc (>= 1.62)
      jaeger-client (~> 1.1.0)
      json-schema (~> 5.1)
      opentracing (~> 0.4)
      pg_query (>= 6.1.0, < 7.0)
      prometheus-client-mmap (~> 1.2.9)
      redis (> 3.0.0, < 6.0.0)
    gitlab-license (2.6.0)
    gitlab-mail_room (0.0.27)
      jwt (>= 2.0)
      net-imap (>= 0.2.1)
      oauth2 (>= 1.4.4, < 3)
      redis (>= 5, < 6)
      redis-namespace (>= 1.8.2)
    gitlab-markup (2.0.0)
    gitlab-net-dns (0.15.0)
      logger
    gitlab-sdk (0.3.1)
      activesupport (>= 5.2.0)
      rake (~> 13.0)
      snowplow-tracker (~> 0.8.0)
    gitlab-secret_detection (0.33.3)
      grpc (>= 1.63.0, < 2)
      grpc_reflection (~> 0.1)
      parallel (~> 1)
      re2 (~> 2.7)
      sentry-ruby (~> 5.22)
      stackprof (~> 0.2.27)
      toml-rb (~> 2.2)
    gitlab-security_report_schemas (0.1.3.min15.0.0.max15.2.3)
      activesupport (>= 6, < 8)
      json_schemer (~> 2.3.0)
      mutex_m (~> 0.3.0)
    gitlab-styles (13.1.0)
      rubocop (= 1.71.1)
      rubocop-capybara (~> 2.21.0)
      rubocop-factory_bot (~> 2.26.1)
      rubocop-graphql (~> 1.5.4)
      rubocop-performance (~> 1.21.1)
      rubocop-rails (~> 2.26.0)
      rubocop-rspec (~> 3.0.4)
      rubocop-rspec_rails (~> 2.30.0)
    gitlab_chronic_duration (0.12.0)
      numerizer (~> 0.2)
    gitlab_omniauth-ldap (2.3.0)
      net-ldap (~> 0.16)
      omniauth (>= 1.3, < 3)
      pyu-ruby-sasl (>= *******, < 0.1)
      rubyntlm (~> 0.5)
    gitlab_quality-test_tooling (2.20.0)
      activesupport (>= 7.0, < 7.3)
      amatch (~> 0.4.1)
      fog-google (~> 1.24, >= 1.24.1)
      gitlab (>= 4.19, < 7.0)
      http (~> 5.0)
      influxdb-client (~> 3.1)
      nokogiri (~> 1.10)
      parallel (>= 1, < 2)
      rainbow (>= 3, < 4)
      rspec-parameterized (>= 1.0, < 3.0)
      table_print (= 1.5.7)
      zeitwerk (>= 2, < 3)
    globalid (1.1.0)
      activesupport (>= 5.0)
    gon (6.5.0)
      actionpack (>= 3.0.20)
      i18n (>= 0.7)
      multi_json
      request_store (>= 1.0)
    google-apis-androidpublisher_v3 (0.85.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-bigquery_v2 (0.90.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-cloudbilling_v1 (0.22.0)
      google-apis-core (>= 0.9.1, < 2.a)
    google-apis-cloudresourcemanager_v1 (0.31.0)
      google-apis-core (>= 0.9.1, < 2.a)
    google-apis-compute_v1 (0.129.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-container_v1 (0.100.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-container_v1beta1 (0.89.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-core (0.18.0)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (~> 1.9)
      httpclient (>= 2.8.3, < 3.a)
      mini_mime (~> 1.0)
      mutex_m
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
    google-apis-dns_v1 (0.36.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-apis-iam_v1 (0.73.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-iamcredentials_v1 (0.15.0)
      google-apis-core (>= 0.9.0, < 2.a)
    google-apis-monitoring_v3 (0.54.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-apis-pubsub_v1 (0.45.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-apis-serviceusage_v1 (0.28.0)
      google-apis-core (>= 0.9.1, < 2.a)
    google-apis-sqladmin_v1beta4 (0.41.0)
      google-apis-core (>= 0.9.1, < 2.a)
    google-apis-storage_v1 (0.29.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-cloud-artifact_registry-v1 (0.11.0)
      gapic-common (>= 0.20.0, < 2.a)
      google-cloud-errors (~> 1.0)
      google-cloud-location (>= 0.4, < 2.a)
      grpc-google-iam-v1 (~> 1.1)
    google-cloud-bigquery (1.52.1)
      bigdecimal (~> 3.0)
      concurrent-ruby (~> 1.0)
      google-apis-bigquery_v2 (~> 0.71)
      google-apis-core (~> 0.13)
      google-cloud-core (~> 1.6)
      googleauth (~> 1.9)
      mini_mime (~> 1.0)
    google-cloud-common (1.1.0)
      google-protobuf (~> 3.14)
      googleapis-common-protos-types (~> 1.2)
    google-cloud-compute-v1 (2.6.0)
      gapic-common (>= 0.20.0, < 2.a)
      google-cloud-common (~> 1.0)
      google-cloud-errors (~> 1.0)
    google-cloud-core (1.7.0)
      google-cloud-env (>= 1.0, < 3.a)
      google-cloud-errors (~> 1.0)
    google-cloud-env (2.2.1)
      faraday (>= 1.0, < 3.a)
    google-cloud-errors (1.3.0)
    google-cloud-location (0.6.0)
      gapic-common (>= 0.20.0, < 2.a)
      google-cloud-errors (~> 1.0)
    google-cloud-storage (1.45.0)
      addressable (~> 2.8)
      digest-crc (~> 0.4)
      google-apis-iamcredentials_v1 (~> 0.1)
      google-apis-storage_v1 (~> 0.29.0)
      google-cloud-core (~> 1.6)
      googleauth (>= 0.16.2, < 2.a)
      mini_mime (~> 1.0)
    google-cloud-storage_transfer (1.2.0)
      google-cloud-core (~> 1.6)
      google-cloud-storage_transfer-v1 (>= 0.5, < 2.a)
    google-cloud-storage_transfer-v1 (0.8.0)
      gapic-common (>= 0.20.0, < 2.a)
      google-cloud-errors (~> 1.0)
    google-logging-utils (0.1.0)
    google-protobuf (3.25.8)
    googleapis-common-protos (1.4.0)
      google-protobuf (~> 3.14)
      googleapis-common-protos-types (~> 1.2)
      grpc (~> 1.27)
    googleapis-common-protos-types (1.20.0)
      google-protobuf (>= 3.18, < 5.a)
    googleauth (1.14.0)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.2)
      google-logging-utils (~> 0.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    gpgme (2.0.25)
      mini_portile2 (~> 2.7)
    grape (2.0.0)
      activesupport (>= 5)
      builder
      dry-types (>= 1.1)
      mustermann-grape (~> 1.0.0)
      rack (>= 1.3.0)
      rack-accept
    grape-entity (1.0.1)
      activesupport (>= 3.0.0)
      multi_json (>= 1.3.2)
    grape-path-helpers (2.0.1)
      activesupport
      grape (~> 2.0)
      rake (> 12)
      ruby2_keywords (~> 0.0.2)
    grape-swagger (2.1.2)
      grape (>= 1.7, < 3.0)
      rack-test (~> 2)
    grape-swagger-entity (0.5.5)
      grape-entity (~> 1)
      grape-swagger (~> 2)
    grape_logging (1.8.4)
      grape
      rack
    graphlyte (1.0.0)
    graphql (2.5.11)
      base64
      fiber-storage
      logger
    graphql-docs (5.2.0)
      commonmarker (~> 0.23, >= 0.23.6)
      escape_utils (~> 1.2)
      extended-markdown-filter (~> 0.4)
      gemoji (~> 3.0)
      graphql (~> 2.0)
      html-pipeline (~> 2.14, >= 2.14.3)
      logger (~> 1.6)
      ostruct (~> 0.6)
      sass-embedded (~> 1.58)
    grpc (1.74.1)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc-google-iam-v1 (1.5.0)
      google-protobuf (~> 3.18)
      googleapis-common-protos (~> 1.4)
      grpc (~> 1.41)
    grpc_reflection (0.1.1)
      grpc
    gssapi (1.3.1)
      ffi (>= 1.0.1)
    guard (2.16.2)
      formatador (>= 0.2.4)
      listen (>= 2.7, < 4.0)
      lumberjack (>= 1.0.12, < 2.0)
      nenv (~> 0.1)
      notiffany (~> 0.0)
      pry (>= 0.9.12)
      shellany (~> 0.0)
      thor (>= 0.18.1)
    guard-compat (1.2.1)
    guard-rspec (4.7.3)
      guard (~> 2.1)
      guard-compat (~> 1.1)
      rspec (>= 2.99.0, < 4.0)
    haml (5.2.2)
      temple (>= 0.8.0)
      tilt
    haml_lint (0.64.0)
      haml (>= 5.0)
      parallel (~> 1.10)
      rainbow
      rubocop (>= 1.0)
      sysexits (~> 1.1)
    hamlit (3.0.3)
      temple (>= 0.8.2)
      thor
      tilt
    hana (1.3.7)
    hashdiff (1.2.0)
    hashie (5.0.0)
    health_check (3.1.0)
      railties (>= 5.0)
    heapy (0.2.0)
      thor
    html-pipeline (2.14.3)
      activesupport (>= 2)
      nokogiri (>= 1.4)
    html2text (0.4.0)
      nokogiri (>= 1.0, < 2.0)
    htmlbeautifier (1.4.2)
    htmlentities (4.3.4)
    http (5.1.1)
      addressable (~> 2.8)
      http-cookie (~> 1.0)
      http-form_data (~> 2.2)
      llhttp-ffi (~> 0.4.0)
    http-accept (1.7.0)
    http-cookie (1.0.5)
      domain_name (~> 0.5)
    http-form_data (2.3.0)
    httparty (0.23.1)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    httpclient (2.8.3)
    i18n (1.14.4)
      concurrent-ruby (~> 1.0)
    i18n_data (0.13.1)
    icalendar (2.10.3)
      ice_cube (~> 0.16)
      ostruct
    ice_cube (0.16.4)
    ice_nine (0.11.2)
    imagen (0.2.0)
      parser (>= 2.5, != *******)
    influxdb-client (3.2.0)
      csv
    invisible_captcha (2.3.0)
      rails (>= 5.2)
    io-console (0.8.0)
    io-event (1.12.1)
    ipaddress (0.8.3)
    irb (1.15.1)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jaeger-client (1.1.0)
      opentracing (~> 0.3)
      thrift
    jaro_winkler (1.6.1)
    jira-ruby (2.3.0)
      activesupport
      atlassian-jwt
      multipart-post
      oauth (~> 0.5, >= 0.5.0)
    jmespath (1.6.2)
    js_regex (3.13.0)
      character_set (~> 1.4)
      regexp_parser (~> 2.10)
      regexp_property_values (~> 1.0)
    json (2.13.1)
    json-jwt (1.16.6)
      activesupport (>= 4.2)
      aes_key_wrap
      base64
      bindata
      faraday (~> 2.0)
      faraday-follow_redirects
    json-schema (5.2.2)
      addressable (~> 2.8)
      bigdecimal (~> 3.1)
    json_schemer (2.3.0)
      bigdecimal
      hana (~> 1.3)
      regexp_parser (~> 2.0)
      simpleidn (~> 0.2)
    jsonb_accessor (1.4)
      activerecord (>= 6.1)
      activesupport (>= 6.1)
      pg (>= 0.18.1)
    jsonpath (1.1.2)
      multi_json
    jwt (2.10.2)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    knapsack (4.0.0)
      rake
    kramdown (2.5.1)
      rexml (>= 3.3.9)
    kramdown-parser-gfm (1.1.0)
      kramdown (~> 2.0)
    kubeclient (4.12.0)
      http (>= 3.0, < 6.0)
      jsonpath (~> 1.0)
      recursive-open-struct (~> 1.1, >= 1.1.1)
      rest-client (~> 2.0)
    language_server-protocol (********)
    launchy (2.5.2)
      addressable (~> 2.8)
    lefthook (1.12.3)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    letter_opener_web (3.0.0)
      actionmailer (>= 6.1)
      letter_opener (~> 1.9)
      railties (>= 6.1)
      rexml
    libyajl2 (2.1.0)
    license_finder (7.2.1)
      bundler
      csv (~> 3.2)
      rubyzip (>= 1, < 3)
      thor (~> 1.2)
      tomlrb (>= 1.3, < 2.1)
      with_env (= 1.1.0)
      xml-simple (~> 1.1.9)
    licensee (9.18.0)
      dotenv (>= 2, < 4)
      octokit (>= 4.20, < 10.0)
      reverse_markdown (>= 1, < 4)
      rugged (>= 0.24, < 2.0)
      thor (>= 0.19, < 2.0)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    llhttp-ffi (0.4.0)
      ffi-compiler (~> 1.0)
      rake (~> 13.0)
    locale (2.1.4)
    lockbox (1.4.1)
    logger (1.7.0)
    lograge (0.11.2)
      actionpack (>= 4)
      activesupport (>= 4)
      railties (>= 4)
      request_store (~> 1.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    lookbook (2.3.4)
      activemodel
      css_parser
      htmlbeautifier (~> 1.3)
      htmlentities (~> 4.3.4)
      marcel (~> 1.0)
      railties (>= 5.0)
      redcarpet (~> 3.5)
      rouge (>= 3.26, < 5.0)
      view_component (>= 2.0)
      yard (~> 0.9)
      zeitwerk (~> 2.5)
    lru_redux (1.1.0)
    lumberjack (1.2.7)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    marginalia (1.11.1)
      actionpack (>= 5.2)
      activerecord (>= 5.2)
    matrix (0.4.2)
    memory_profiler (1.1.0)
    method_source (1.0.0)
    metrics (0.12.1)
    mime-types (3.5.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2023.1003)
    mini_histogram (0.3.1)
    mini_magick (4.12.0)
    mini_mime (1.1.2)
    mini_portile2 (2.8.8)
    minitest (5.11.3)
    mixlib-cli (2.1.8)
    mixlib-config (3.0.27)
      tomlrb
    mixlib-log (3.2.3)
      ffi (>= 1.15.5)
    mixlib-shellout (3.2.7)
      chef-utils
    mize (0.6.1)
    msgpack (1.5.4)
    multi_json (1.14.1)
    multi_xml (0.6.0)
    multipart-post (2.2.3)
    murmurhash3 (0.1.7)
    mustermann (3.0.0)
      ruby2_keywords (~> 0.0.1)
    mustermann-grape (1.0.2)
      mustermann (>= 1.0.0)
    mutex_m (0.3.0)
    nap (1.1.0)
    nenv (0.3.0)
    net-http (0.6.0)
      uri
    net-http-persistent (4.0.5)
      connection_pool (~> 2.2)
    net-imap (0.5.9)
      date
      net-protocol
    net-ldap (0.17.1)
    net-ntp (2.1.3)
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.0.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-smtp (0.3.3)
      net-protocol
    net-ssh (7.3.0)
    netrc (0.11.0)
    nio4r (2.7.0)
    nkf (0.2.0)
    no_proxy_fix (0.1.2)
    nokogiri (1.18.9)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    notiffany (0.1.3)
      nenv (~> 0.1)
      shellany (~> 0.0)
    numerizer (0.2.0)
    oauth (0.5.6)
    oauth2 (2.0.10)
      faraday (>= 0.17.3, < 4.0)
      jwt (>= 1.0, < 4.0)
      logger (~> 1.2)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0)
      version_gem (>= 1.1.8, < 3)
    observer (0.1.2)
    octokit (9.2.0)
      faraday (>= 1, < 3)
      sawyer (~> 0.9)
    ohai (18.1.18)
      chef-config (>= 14.12, < 19)
      chef-utils (>= 16.0, < 19)
      ffi (~> 1.9)
      ffi-yajl (~> 2.2)
      ipaddress
      mixlib-cli (>= 1.7.0)
      mixlib-config (>= 2.0, < 4.0)
      mixlib-log (>= 2.0.1, < 4.0)
      mixlib-shellout (~> 3.2, >= 3.2.5)
      plist (~> 3.1)
      train-core
      wmi-lite (~> 1.0)
    oj (3.16.11)
      bigdecimal (>= 3.0)
      ostruct (>= 0.2)
    oj-introspect (0.8.0)
      oj (>= 3.16.10)
    omniauth (2.1.3)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-alicloud (3.0.0)
      omniauth-oauth2 (~> 1.8)
    omniauth-atlassian-oauth2 (0.2.0)
      omniauth (>= 1.1.1)
      omniauth-oauth2 (>= 1.5)
    omniauth-auth0 (3.1.1)
      omniauth (~> 2)
      omniauth-oauth2 (~> 1)
    omniauth-azure-activedirectory-v2 (2.0.0)
      omniauth-oauth2 (~> 1.8)
    omniauth-github (2.0.1)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.8)
    omniauth-google-oauth2 (1.1.1)
      jwt (>= 2.0)
      oauth2 (~> 2.0.6)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.8.0)
    omniauth-oauth2 (1.8.0)
      oauth2 (>= 1.4, < 3)
      omniauth (~> 2.0)
    omniauth-oauth2-generic (0.2.8)
      omniauth-oauth2 (~> 1.0)
      rake
    omniauth-saml (2.2.4)
      omniauth (~> 2.1)
      ruby-saml (~> 1.18)
    omniauth-shibboleth-redux (2.0.0)
      omniauth (>= 2.0.0)
    omniauth_openid_connect (0.8.0)
      omniauth (>= 1.9, < 3)
      openid_connect (~> 2.2)
    open4 (1.3.4)
    openid_connect (2.3.1)
      activemodel
      attr_required (>= 1.0.0)
      email_validator
      faraday (~> 2.0)
      faraday-follow_redirects
      json-jwt (>= 1.16)
      mail
      rack-oauth2 (~> 2.2)
      swd (~> 2.0)
      tzinfo
      validate_url
      webfinger (~> 2.0)
    opensearch-ruby (3.4.0)
      faraday (>= 1.0, < 3)
      multi_json (>= 1.0)
    openssl (3.3.0)
    openssl-signature_algorithm (1.3.0)
      openssl (> 2.0)
    opentelemetry-api (1.2.5)
    opentelemetry-common (0.21.0)
      opentelemetry-api (~> 1.0)
    opentelemetry-exporter-otlp (0.29.1)
      google-protobuf (>= 3.18)
      googleapis-common-protos-types (~> 1.3)
      opentelemetry-api (~> 1.1)
      opentelemetry-common (~> 0.20)
      opentelemetry-sdk (~> 1.2)
      opentelemetry-semantic_conventions
    opentelemetry-helpers-sql-obfuscation (0.1.0)
      opentelemetry-common (~> 0.20)
    opentelemetry-instrumentation-action_mailer (0.2.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-active_support (~> 0.1)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-action_pack (0.10.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
      opentelemetry-instrumentation-rack (~> 0.21)
    opentelemetry-instrumentation-action_view (0.7.3)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-active_support (~> 0.6)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-active_job (0.7.8)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-active_record (0.8.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-active_support (0.6.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-aws_sdk (0.7.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-base (0.22.3)
      opentelemetry-api (~> 1.0)
      opentelemetry-registry (~> 0.1)
    opentelemetry-instrumentation-concurrent_ruby (0.21.4)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-ethon (0.21.9)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-excon (0.22.5)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-faraday (0.24.7)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-grape (0.2.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
      opentelemetry-instrumentation-rack (~> 0.21)
    opentelemetry-instrumentation-graphql (0.28.4)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-http (0.23.5)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-http_client (0.22.8)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-net_http (0.22.8)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-pg (0.29.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-helpers-sql-obfuscation
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-rack (0.25.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-rails (0.33.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-action_mailer (~> 0.2.0)
      opentelemetry-instrumentation-action_pack (~> 0.10.0)
      opentelemetry-instrumentation-action_view (~> 0.7.0)
      opentelemetry-instrumentation-active_job (~> 0.7.0)
      opentelemetry-instrumentation-active_record (~> 0.8.0)
      opentelemetry-instrumentation-active_support (~> 0.6.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-rake (0.2.2)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-redis (0.25.7)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-instrumentation-sidekiq (0.25.7)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.22.1)
    opentelemetry-registry (0.3.0)
      opentelemetry-api (~> 1.1)
    opentelemetry-sdk (1.6.0)
      opentelemetry-api (~> 1.1)
      opentelemetry-common (~> 0.20)
      opentelemetry-registry (~> 0.2)
      opentelemetry-semantic_conventions
    opentelemetry-semantic_conventions (1.10.0)
      opentelemetry-api (~> 1.0)
    opentracing (0.5.0)
    optimist (3.0.1)
    org-ruby (0.9.12)
      rubypants (~> 0.2)
    orm_adapter (0.5.0)
    os (1.1.4)
    ostruct (0.6.1)
    pact (1.64.0)
      pact-mock_service (~> 3.0, >= 3.3.1)
      pact-support (~> 1.16, >= 1.16.9)
      rack-test (>= 0.6.3, < 3.0.0)
      rspec (~> 3.0)
      term-ansicolor (~> 1.7)
      thor (>= 0.20, < 2.0)
      webrick (~> 1.8)
    pact-mock_service (3.11.2)
      find_a_port (~> 1.0.1)
      json
      pact-support (~> 1.16, >= 1.16.4)
      rack (~> 2.0)
      rspec (>= 2.14)
      thor (>= 0.19, < 2.0)
      webrick (~> 1.8)
    pact-support (1.20.0)
      awesome_print (~> 1.9)
      diff-lcs (~> 1.5)
      expgen (~> 0.1)
      rainbow (~> 3.1.1)
    paper_trail (16.0.0)
      activerecord (>= 6.1)
      request_store (~> 1.4)
    parallel (1.27.0)
    parser (3.3.9.0)
      ast (~> 2.4.1)
      racc
    parslet (1.8.2)
    pastel (0.8.0)
      tty-color (~> 0.5)
    pdf-core (0.10.0)
    peek (1.1.0)
      railties (>= 4.0.0)
    pg (1.6.1)
    pg_query (6.1.0)
      google-protobuf (>= 3.25.3)
    plist (3.7.0)
    png_quantizator (0.2.1)
    pp (0.6.2)
      prettyprint
    prawn (2.5.0)
      matrix (~> 0.4)
      pdf-core (~> 0.10.0)
      ttfunk (~> 1.8)
    prawn-svg (0.37.0)
      css_parser (~> 1.6)
      matrix (~> 0.4.2)
      prawn (>= 0.11.1, < 3)
      rexml (>= 3.3.9, < 4)
    premailer (1.23.0)
      addressable
      css_parser (>= 1.12.0)
      htmlentities (>= 4.0.0)
    premailer-rails (1.12.0)
      actionmailer (>= 3)
      net-smtp
      premailer (~> 1.7, >= 1.7.9)
    prettyprint (0.2.0)
    prime (0.1.3)
      forwardable
      singleton
    prism (1.2.0)
    proc_to_ast (0.1.0)
      coderay
      parser
      unparser
    prometheus-client-mmap (1.2.10)
      base64
      bigdecimal
      logger
      rb_sys (~> 0.9.109)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-byebug (3.11.0)
      byebug (~> 12.0)
      pry (>= 0.13, < 0.16)
    pry-rails (0.3.11)
      pry (>= 0.13.0)
    pry-shell (0.6.4)
      pry (>= 0.13.0)
      tty-markdown
      tty-prompt
    psych (5.2.3)
      date
      stringio
    public_suffix (6.0.1)
    puma (6.6.1)
      nio4r (~> 2.0)
    pyu-ruby-sasl (*******)
    raabro (1.4.0)
    racc (1.8.1)
    rack (2.2.17)
    rack-accept (0.4.5)
      rack (>= 0.4)
    rack-attack (6.7.0)
      rack (>= 1.0, < 4)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-oauth2 (2.2.1)
      activesupport
      attr_required
      faraday (~> 2.0)
      faraday-follow_redirects
      json-jwt (>= 1.11.0)
      rack (>= 2.1.0)
    rack-protection (2.2.2)
      rack
    rack-proxy (0.7.7)
      rack
    rack-session (1.0.2)
      rack (< 3)
    rack-test (2.1.0)
      rack (>= 1.3)
    rack-timeout (0.7.0)
    rackup (1.0.1)
      rack (< 3)
      webrick
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.1)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails-i18n (7.0.10)
      i18n (>= 0.7, < 2)
      railties (>= 6.0.0, < 8)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.0.6)
    rake-compiler-dock (1.9.1)
    rb-fsevent (0.11.2)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    rb_sys (0.9.110)
      rake-compiler-dock (= 1.9.1)
    rbs (3.6.1)
      logger
    rbtrace (0.5.2)
      ffi (>= 1.0.6)
      msgpack (>= 0.4.3)
      optimist (>= 3.0.0)
    rchardet (1.8.0)
    rdoc (6.13.0)
      psych (>= 4.0.0)
    re2 (2.19.0)
      mini_portile2 (~> 2.8.7)
    recaptcha (5.12.3)
      json
    recursive-open-struct (1.1.3)
    redcarpet (3.6.0)
    redis (5.4.1)
      redis-client (>= 0.22.0)
    redis-actionpack (5.5.0)
      actionpack (>= 5)
      redis-rack (>= 2.1.0, < 4)
      redis-store (>= 1.1.0, < 2)
    redis-client (0.25.2)
      connection_pool
    redis-cluster-client (0.13.5)
      redis-client (~> 0.24)
    redis-clustering (5.4.1)
      redis (= 5.4.1)
      redis-cluster-client (>= 0.10.0)
    redis-namespace (1.11.0)
      redis (>= 4)
    redis-rack (3.0.0)
      rack-session (>= 0.2.0)
      redis-store (>= 1.2, < 2)
    redis-store (1.11.0)
      redis (>= 4, < 6)
    regexp_parser (2.10.0)
    regexp_property_values (1.0.0)
    reline (0.6.0)
      io-console (~> 0.5)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    request_store (1.7.0)
      rack (>= 1.4)
    responders (3.0.1)
      actionpack (>= 5.0)
      railties (>= 5.0)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    retriable (3.1.2)
    reverse_markdown (3.0.0)
      nokogiri
    rexml (3.4.2)
    rinku (2.0.0)
    rotp (6.3.0)
    rouge (4.6.0)
    rqrcode (2.2.0)
      chunky_png (~> 1.0)
      rqrcode_core (~> 1.0)
    rqrcode_core (1.2.0)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-benchmark (0.6.0)
      benchmark-malloc (~> 0.2)
      benchmark-perf (~> 0.6)
      benchmark-trend (~> 0.4)
      rspec (>= 3.0)
    rspec-core (3.13.1)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-parameterized (1.0.2)
      rspec-parameterized-core (< 2)
      rspec-parameterized-table_syntax (< 2)
    rspec-parameterized-core (1.0.0)
      parser
      proc_to_ast
      rspec (>= 2.13, < 4)
      unparser
    rspec-parameterized-table_syntax (1.0.0)
      binding_of_caller
      rspec-parameterized-core (< 2)
    rspec-rails (7.1.1)
      actionpack (>= 7.0)
      activesupport (>= 7.0)
      railties (>= 7.0)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-retry (0.6.2)
      rspec-core (> 3.3)
    rspec-support (3.13.1)
    rspec_junit_formatter (0.6.0)
      rspec-core (>= 2, < 4, != 2.12.0)
    rspec_profiling (0.0.9)
      activerecord
      get_process_mem
      rails
    rubocop (1.71.1)
      json (~> 2.3)
      language_server-protocol (>= 3.17.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.38.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.38.0)
      parser (>= 3.3.1.0)
    rubocop-capybara (2.21.0)
      rubocop (~> 1.41)
    rubocop-factory_bot (2.26.1)
      rubocop (~> 1.61)
    rubocop-graphql (1.5.4)
      rubocop (>= 1.50, < 2)
    rubocop-performance (1.21.1)
      rubocop (>= 1.48.1, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rails (2.26.2)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.52.0, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rspec (3.0.5)
      rubocop (~> 1.61)
    rubocop-rspec_rails (2.30.0)
      rubocop (~> 1.61)
      rubocop-rspec (~> 3, >= 3.0.1)
    ruby-lsp (0.23.20)
      language_server-protocol (~> 3.17.0)
      prism (>= 1.2, < 2.0)
      rbs (>= 3, < 4)
      sorbet-runtime (>= 0.5.10782)
    ruby-lsp-rails (0.3.31)
      ruby-lsp (>= 0.23.0, < 0.24.0)
    ruby-lsp-rspec (0.1.24)
      ruby-lsp (~> 0.23.19)
    ruby-magic (0.6.0)
      mini_portile2 (~> 2.8)
    ruby-progressbar (1.11.0)
    ruby-saml (1.18.0)
      nokogiri (>= 1.13.10)
      rexml
    ruby-statistics (4.1.0)
    ruby2_keywords (0.0.5)
    rubyntlm (0.6.3)
    rubypants (0.2.0)
    rubyzip (2.4.1)
    rugged (1.6.3)
    safe_yaml (1.0.4)
    safety_net_attestation (0.4.0)
      jwt (~> 2.0)
    sanitize (6.0.2)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    sass-embedded (1.77.5)
      google-protobuf (>= 3.25, < 5.0)
      rake (>= 13)
    sawyer (0.9.2)
      addressable (>= 2.3.5)
      faraday (>= 0.17.3, < 3)
    sd_notify (0.1.1)
    securerandom (0.4.1)
    seed-fu (2.3.9)
      activerecord (>= 3.1)
      activesupport (>= 3.1)
    selenium-webdriver (4.32.0)
      base64 (~> 0.2)
      logger (~> 1.4)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    semver_dialects (3.7.0)
      deb_version (~> 1.0.1)
      pastel (~> 0.8.0)
      thor (~> 1.3)
      tty-command (~> 0.10.1)
    sentry-rails (5.23.0)
      railties (>= 5.0)
      sentry-ruby (~> 5.23.0)
    sentry-ruby (5.23.0)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
    sentry-sidekiq (5.23.0)
      sentry-ruby (~> 5.23.0)
      sidekiq (>= 3.0)
    shellany (0.0.1)
    shoulda-matchers (6.4.0)
      activesupport (>= 5.2.0)
    sidekiq-cron (1.12.0)
      fugit (~> 1.8)
      globalid (>= 1.0.1)
      sidekiq (>= 6)
    sigdump (0.2.5)
    signet (0.19.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simple_po_parser (1.1.6)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-cobertura (2.1.0)
      rexml
      simplecov (~> 0.19)
    simplecov-html (0.12.3)
    simplecov-lcov (0.8.0)
    simplecov_json_formatter (0.1.4)
    simpleidn (0.2.3)
    singleton (0.3.0)
    sixarm_ruby_unaccent (1.2.0)
    slack-messenger (2.3.6)
      re2 (~> 2.7, >= 2.7.0)
    snaky_hash (2.0.0)
      hashie
      version_gem (~> 1.1)
    snowplow-tracker (0.8.0)
    solargraph (0.54.4)
      backport (~> 1.2)
      benchmark (~> 0.4)
      bundler (~> 2.0)
      diff-lcs (~> 1.4)
      jaro_winkler (~> 1.6, >= 1.6.1)
      kramdown (~> 2.3)
      kramdown-parser-gfm (~> 1.1)
      logger (~> 1.6)
      observer (~> 0.1)
      ostruct (~> 0.6)
      parser (~> 3.0)
      rbs (~> 3.3)
      reverse_markdown (~> 3.0)
      rubocop (~> 1.38)
      thor (~> 1.0)
      tilt (~> 2.0)
      yard (~> 0.9, >= 0.9.24)
      yard-solargraph (~> 0.1)
    solargraph-rspec (0.5.2)
      solargraph (~> 0.52, >= 0.52.0)
    sorbet-runtime (0.5.11647)
    spamcheck (1.3.3)
      grpc (~> 1.63)
    spring (4.3.0)
    spring-commands-rspec (1.0.4)
      spring (>= 0.9.1)
    sprite-factory (1.7.1)
    sprockets (3.7.5)
      base64
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    ssh_data (1.3.0)
    ssrf_filter (1.0.8)
    stackprof (0.2.27)
    state_machines (0.5.0)
    state_machines-activemodel (0.8.0)
      activemodel (>= 5.1)
      state_machines (>= 0.5.0)
    state_machines-activerecord (0.8.0)
      activerecord (>= 5.1)
      state_machines-activemodel (>= 0.8.0)
    state_machines-rspec (0.6.0)
      activesupport
      rspec (~> 3.3)
      state_machines
    stringio (3.1.7)
    strings (0.2.1)
      strings-ansi (~> 0.2)
      unicode-display_width (>= 1.5, < 3.0)
      unicode_utils (~> 1.4)
    strings-ansi (0.2.0)
    swd (2.0.3)
      activesupport (>= 3)
      attr_required (>= 0.0.5)
      faraday (~> 2.0)
      faraday-follow_redirects
    sync (0.5.0)
    sys-filesystem (1.4.3)
      ffi (~> 1.1)
    sysexits (1.2.0)
    table_print (1.5.7)
    tanuki_emoji (0.13.0)
      i18n (~> 1.14)
    telesign (2.4.0)
      net-http-persistent (>= 3.0.0, < 5.0)
    telesignenterprise (2.6.0)
      telesign (~> 2.4.0)
    temple (0.8.2)
    term-ansicolor (1.7.1)
      tins (~> 1.0)
    terminal-table (3.0.2)
      unicode-display_width (>= 1.1.1, < 3)
    terser (1.0.2)
      execjs (>= 0.3.0, < 3)
    test-prof (1.4.4)
    test_file_finder (0.3.1)
      faraday (>= 1.0, < 3.0, != 2.0.0)
    text (1.3.1)
    thor (1.3.1)
    thread_safe (0.3.6)
    thrift (0.22.0)
    tilt (2.0.11)
    timeout (0.4.3)
    timfel-krb5-auth (0.8.3)
    tins (1.31.1)
      sync
    toml-rb (2.2.0)
      citrus (~> 3.0, > 3.0)
    tomlrb (1.3.0)
    tpm-key_attestation (0.12.0)
      bindata (~> 2.4)
      openssl (> 2.0)
      openssl-signature_algorithm (~> 1.0)
    traces (0.15.2)
    trailblazer-option (0.1.2)
    train-core (3.10.8)
      addressable (~> 2.5)
      ffi (!= 1.13.0)
      json (>= 1.8, < 3.0)
      mixlib-shellout (>= 2.0, < 4.0)
      net-scp (>= 1.2, < 5.0)
      net-ssh (>= 2.9, < 8.0)
    truncato (0.7.13)
      htmlentities (~> 4.3.1)
      nokogiri (>= 1.7.0, <= 2.0)
    ttfunk (1.8.0)
      bigdecimal (~> 3.1)
    tty-color (0.6.0)
    tty-command (0.10.1)
      pastel (~> 0.8)
    tty-cursor (0.7.1)
    tty-markdown (0.7.2)
      kramdown (>= 1.16.2, < 3.0)
      pastel (~> 0.8)
      rouge (>= 3.14, < 5.0)
      strings (~> 0.2.0)
      tty-color (~> 0.5)
      tty-screen (~> 0.8)
    tty-prompt (0.23.1)
      pastel (~> 0.8)
      tty-reader (~> 0.8)
    tty-reader (0.9.0)
      tty-cursor (~> 0.7)
      tty-screen (~> 0.8)
      wisper (~> 2.0)
    tty-screen (0.8.1)
    typhoeus (1.4.1)
      ethon (>= 0.9.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    undercover (0.7.4)
      base64
      bigdecimal
      imagen (>= 0.2.0)
      rainbow (>= 2.1, < 4.0)
      rugged (>= 0.27, < 1.10)
      simplecov
      simplecov_json_formatter
    unf (0.1.4)
      unf_ext
    unf_ext (*******)
    unicode-display_width (2.4.2)
    unicode-emoji (4.0.4)
    unicode_utils (1.4.0)
    uniform_notifier (1.16.0)
    unleash (3.2.2)
      murmurhash3 (~> 0.1.6)
    unparser (0.6.7)
      diff-lcs (~> 1.3)
      parser (>= 3.2.0)
    uri (0.13.2)
    valid_email (0.1.3)
      activemodel
      mail (>= 2.6.1)
    validate_url (1.0.15)
      activemodel (>= 3.0.0)
      public_suffix
    validates_hostname (1.0.13)
      activerecord (>= 3.0)
      activesupport (>= 3.0)
    version_gem (1.1.8)
    version_sorter (2.3.0)
    view_component (3.23.2)
      activesupport (>= 5.2.0, < 8.1)
      concurrent-ruby (~> 1)
      method_source (~> 1.0)
    virtus (2.0.0)
      axiom-types (~> 0.1)
      coercible (~> 1.0)
      descendants_tracker (~> 0.0, >= 0.0.3)
    vite_rails (3.0.19)
      railties (>= 5.1, < 9)
      vite_ruby (~> 3.0, >= 3.2.2)
    vite_ruby (3.9.2)
      dry-cli (>= 0.7, < 2)
      logger (~> 1.6)
      mutex_m
      rack-proxy (~> 0.6, >= 0.6.1)
      zeitwerk (~> 2.2)
    vmstat (2.3.1)
    warden (1.2.9)
      rack (>= 2.0.9)
    warning (1.5.0)
    webauthn (3.0.0)
      android_key_attestation (~> 0.3.0)
      awrence (~> 1.1)
      bindata (~> 2.4)
      cbor (~> 0.5.9)
      cose (~> 1.1)
      openssl (>= 2.2)
      safety_net_attestation (~> 0.4.0)
      tpm-key_attestation (~> 0.12.0)
    webfinger (2.1.3)
      activesupport
      faraday (~> 2.0)
      faraday-follow_redirects
    webmock (3.25.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.9.1)
    websocket (1.2.10)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    wikicloth (0.8.1)
      builder
      expression_parser
      rinku
    wisper (2.0.1)
    with_env (1.1.0)
    wmi-lite (1.0.7)
    xml-simple (1.1.9)
      rexml
    xpath (3.2.0)
      nokogiri (~> 1.8)
    yajl-ruby (1.4.3)
    yard (0.9.37)
    yard-solargraph (0.1.0)
      yard (~> 0.9)
    zeitwerk (2.6.7)

PLATFORMS
  ruby

DEPENDENCIES
  CFPropertyList (~> 3.0.0)
  RedCloth (~> 4.3.3)
  acme-client (~> 2.0.19)
  activerecord-gitlab!
  addressable (~> 2.8)
  akismet (~> 3.0)
  apollo_upload_server (~> 2.1.6)
  app_store_connect
  arr-pm (~> 0.0.12)
  asciidoctor (~> 2.0.18)
  asciidoctor-include-ext (~> 0.4.0)
  asciidoctor-kroki (~> 0.10.0)
  asciidoctor-plantuml (~> 0.0.16)
  async (~> 2.27.0)
  atlassian-jwt (~> 0.2.1)
  attr_encrypted (~> 4.2)
  awesome_print
  aws-sdk-cloudformation (~> 1)
  aws-sdk-core (~> 3.226.0)
  aws-sdk-s3 (~> 1.193.0)
  axe-core-rspec (~> 4.10.0)
  babosa (~> 2.0)
  base32 (~> 0.3.0)
  base64 (~> 0.2.0)
  batch-loader (~> 2.0.5)
  bcrypt (~> 3.1, >= 3.1.14)
  benchmark-ips (~> 2.14.0)
  benchmark-memory (~> 0.1)
  better_errors (~> 2.10.1)
  bootsnap (~> 1.18.6)
  browser (~> 5.3.1)
  bullet (~> 8.0.0)
  bundler-checksum (~> 0.1.0)!
  capybara (~> 3.40)
  capybara-screenshot (~> 1.0.26)
  carrierwave (~> 1.3)
  charlock_holmes (~> 0.7.9)
  circuitbox (= 2.0.0)
  click_house-client (= 0.5.1)
  commonmarker (~> 0.23.10)
  concurrent-ruby (~> 1.1)
  connection_pool (~> 2.5.3)
  countries (~> 4.0.0)
  coverband (= 6.1.5)
  creole (~> 0.5.0)
  cssbundling-rails (= 1.4.3)
  csv_builder!
  cvss-suite (~> 3.3.0)
  database_cleaner-active_record (~> 2.2.0)
  debug (~> 1.11.0)
  deckar01-task_list (= 2.3.4)
  declarative_policy (~> 2.0.1)
  deprecation_toolkit (~> 2.2.3)
  derailed_benchmarks
  devfile (~> 0.4.7)
  device_detector
  devise (~> 4.9.3)
  devise-pbkdf2-encryptable (~> 0.0.0)!
  devise-two-factor (~> 4.1.1)
  diff_match_patch (~> 0.1.0)!
  diffy (~> 3.4)
  doorkeeper (~> 5.8, >= 5.8.1)
  doorkeeper-device_authorization_grant (~> 1.0.0)
  doorkeeper-openid_connect (~> 1.8.10)
  drb (~> 2.2)
  duo_api (~> 1.3)
  ed25519 (~> 1.4.0)
  elasticsearch-api (= 7.17.11)
  elasticsearch-model (~> 7.2)
  elasticsearch-rails (~> 7.2)
  email_reply_trimmer (~> 0.1)
  email_spec (~> 2.3.0)
  error_tracking_open_api!
  factory_bot_rails (~> 6.5.0)
  faraday (~> 2)
  faraday-multipart (~> 1.0)
  faraday-retry (~> 2)
  faraday-typhoeus (~> 1.1)
  faraday_middleware-aws-sigv4 (~> 1.0.1)
  fast_blank (~> 1.0.1)
  ffaker (~> 2.24)
  ffi (~> 1.17.2)
  flipper (~> 0.28.0)
  flipper-active_record (~> 0.28.0)
  flipper-active_support_cache_store (~> 0.28.0)
  fog-aliyun (~> 0.4)
  fog-aws (~> 3.26)
  fog-core (~> 2.5)
  fog-google (~> 1.25)
  fog-local (~> 0.8)
  fugit (~> 1.11.1)
  gdk-toogle (~> 0.9, >= 0.9.5)
  gettext (~> 3.5, >= 3.5.1)
  gettext_i18n_rails (~> 1.13.0)
  git (~> 1.8)
  gitaly (~> 18.2.0)
  gitlab-active-context!
  gitlab-backup-cli!
  gitlab-chronic (~> 0.10.5)
  gitlab-cloud-connector (~> 1.26)
  gitlab-crystalball (~> 1.1.0)
  gitlab-dangerfiles (~> 4.10.0)
  gitlab-*********************client (~> 0.2)!
  gitlab-experiment (~> 0.9.1)
  gitlab-fog-azure-rm (~> 2.2.0)
  gitlab-glfm-markdown (~> 0.0.33)
  gitlab-housekeeper!
  gitlab-http!
  gitlab-kas-grpc (~> 18.2.0)
  gitlab-labkit (~> 0.40.0)
  gitlab-license (~> 2.6)
  gitlab-mail_room (~> 0.0.24)
  gitlab-markup (~> 2.0.0)
  gitlab-net-dns (~> 0.15.0)
  gitlab-rspec!
  gitlab-rspec_flaky!
  gitlab-safe_request_store!
  gitlab-schema-validation!
  gitlab-sdk (~> 0.3.0)
  gitlab-secret_detection (< 1.0)
  gitlab-security_report_schemas (= 0.1.3.min15.0.0.max15.2.3)
  gitlab-sidekiq-fetcher!
  gitlab-styles (~> 13.1.0)
  gitlab-topology-service-client (~> 0.1)!
  gitlab-utils!
  gitlab_chronic_duration (~> 0.12)
  gitlab_omniauth-ldap (~> 2.3.0)
  gitlab_quality-test_tooling (~> 2.20.0)
  gon (~> 6.5.0)
  google-apis-androidpublisher_v3 (~> 0.85.0)
  google-apis-cloudbilling_v1 (~> 0.22.0)
  google-apis-cloudresourcemanager_v1 (~> 0.31.0)
  google-apis-compute_v1 (~> 0.129.0)
  google-apis-container_v1 (~> 0.100.0)
  google-apis-container_v1beta1 (~> 0.89.0)
  google-apis-core (~> 0.18.0, >= 0.18.0)
  google-apis-iam_v1 (~> 0.73.0)
  google-apis-serviceusage_v1 (~> 0.28.0)
  google-apis-sqladmin_v1beta4 (~> 0.41.0)
  google-apis-storage_v1 (~> 0.29)
  google-cloud-artifact_registry-v1 (~> 0.11.0)
  google-cloud-bigquery (~> 1.0)
  google-cloud-compute-v1 (~> 2.6.0)
  google-cloud-storage (~> 1.45.0)
  google-protobuf (~> 3.25, >= 3.25.3)
  googleauth (~> 1.14)
  gpgme (~> 2.0.24)
  grape (~> 2.0.0)
  grape-entity (~> 1.0.1)
  grape-path-helpers (~> 2.0.1)
  grape-swagger (~> 2.1.2)
  grape-swagger-entity (~> 0.5.5)
  grape_logging (~> 1.8, >= 1.8.4)
  graphlyte (~> 1.0.0)
  graphql (= 2.5.11)
  graphql-docs (~> 5.2.0)
  grpc (~> 1.74.0)
  gssapi (~> 1.3.1)
  guard-rspec
  haml_lint (~> 0.58)
  hamlit (~> 3.0.0)
  hashdiff (~> 1.2.0)
  hashie (~> 5.0.0)
  health_check (~> 3.0)
  html-pipeline (~> 2.14.3)
  html2text
  httparty (~> 0.23.0)
  i18n_data (~> 0.13.1)
  icalendar (~> 2.10.1)
  invisible_captcha (~> 2.3.0)
  io-event (~> 1.12)
  ipaddress (~> 0.8.3)
  ipynbdiff!
  jira-ruby (~> 2.3.0)
  js_regex (~> 3.8)
  json (~> 2.13.0)
  json_schemer (~> 2.3.0)
  jsonb_accessor (~> 1.4)
  jwt (~> 2.10.0)
  kaminari (~> 1.2.2)
  knapsack (~> 4.0.0)
  kramdown (~> 2.5.0)
  kubeclient (~> 4.12.0)
  lefthook (~> 1.12.0)
  letter_opener_web (~> 3.0.0)
  license_finder (~> 7.0)
  licensee (~> 9.16)
  listen (~> 3.7)
  lockbox (~> 1.4.0)
  logger (~> 1.7.0)
  lograge (~> 0.5)
  loofah (~> 2.24.0)
  lookbook (~> 2.3)
  lru_redux
  mail (= 2.8.1)
  mail-smtp_pool (~> 0.1.0)!
  marcel (~> 1.0.4)
  marginalia (~> 1.11.1)
  memory_profiler (~> 1.0)
  microsoft_graph_mailer (~> 0.1.0)!
  mini_magick (~> 4.12)
  minitest (~> 5.11.0)
  multi_json (~> 1.14.1)
  mutex_m (~> 0.3)
  net-http (= 0.6.0)
  net-ldap (~> 0.17.1)
  net-ntp
  net-protocol (~> 0.2.2)
  nkf (~> 0.2.0)
  nokogiri (~> 1.18)
  oauth2 (~> 2.0)
  octokit (~> 9.0)
  ohai (~> 18.1)
  oj (~> 3.16.0, >= 3.16.10)
  oj-introspect (~> 0.8)
  omniauth (~> 2.1.0)
  omniauth-alicloud (~> 3.0.0)
  omniauth-atlassian-oauth2 (~> 0.2.0)
  omniauth-auth0 (~> 3.1)
  omniauth-azure-activedirectory-v2 (~> 2.0)
  omniauth-github (= 2.0.1)
  omniauth-gitlab (~> 4.0.0)!
  omniauth-google-oauth2 (~> 1.1)
  omniauth-oauth2-generic (~> 0.2.2)
  omniauth-salesforce (~> 1.0.5)!
  omniauth-saml (~> 2.2.1)
  omniauth-shibboleth-redux (~> 2.0)
  omniauth_crowd (~> 2.4.0)!
  omniauth_openid_connect (~> 0.8.0)
  openid_connect (~> 2.3.0)
  openssl (~> 3.0)
  opentelemetry-exporter-otlp
  opentelemetry-instrumentation-action_pack
  opentelemetry-instrumentation-action_view
  opentelemetry-instrumentation-active_job
  opentelemetry-instrumentation-active_record
  opentelemetry-instrumentation-active_support
  opentelemetry-instrumentation-aws_sdk
  opentelemetry-instrumentation-concurrent_ruby
  opentelemetry-instrumentation-ethon
  opentelemetry-instrumentation-excon
  opentelemetry-instrumentation-faraday
  opentelemetry-instrumentation-grape
  opentelemetry-instrumentation-graphql
  opentelemetry-instrumentation-http
  opentelemetry-instrumentation-http_client
  opentelemetry-instrumentation-net_http
  opentelemetry-instrumentation-pg
  opentelemetry-instrumentation-rack
  opentelemetry-instrumentation-rails
  opentelemetry-instrumentation-rake
  opentelemetry-instrumentation-redis
  opentelemetry-instrumentation-sidekiq
  opentelemetry-sdk
  org-ruby (~> 0.9.12)
  os (~> 1.1, >= 1.1.4)
  pact (~> 1.64)
  paper_trail (~> 16.0)
  parallel (~> 1.19)
  parser (= 3.3.9.0)
  parslet (~> 1.8)
  peek (~> 1.1)
  pg (~> 1.6.1)
  pg_query (~> 6.1.0)
  png_quantizator (~> 0.2.1)
  prawn
  prawn-svg
  premailer-rails (~> 1.12.0)
  prometheus-client-mmap (~> 1.2.8)
  pry-byebug
  pry-rails (~> 0.3.9)
  pry-shell (~> 0.6.4)
  puma (= 6.6.1)
  rack (~> 2.2.9)
  rack-attack (~> 6.7.0)
  rack-cors (~> 2.0.1)
  rack-oauth2 (~> 2.2.1)
  rack-proxy (~> 0.7.7)
  rack-timeout (~> 0.7.0)
  rails (~> *******)
  rails-controller-testing
  rails-i18n (~> 7.0, >= 7.0.9)
  rainbow (~> 3.0)
  rbtrace (~> 0.4)
  re2 (~> 2.15)
  recaptcha (~> 5.12)
  redis (~> 5.4.0)
  redis-actionpack (~> 5.5.0)
  redis-client (~> 0.25)
  redis-cluster-client (~> 0.13)
  redis-clustering (~> 5.4.0)
  request_store (~> 1.7.0)
  responders (~> 3.0)
  retriable (~> 3.1.2)
  rexml (~> 3.4.0)
  rouge (~> 4.6.0)
  rqrcode (~> 2.2)
  rspec-benchmark (~> 0.6.0)
  rspec-parameterized (~> 1.0, >= 1.0.2)
  rspec-rails (~> 7.1.0)
  rspec-retry (~> 0.6.2)
  rspec_junit_formatter
  rspec_profiling (~> 0.0.9)
  rubocop
  ruby-lsp (~> 0.23.0)
  ruby-lsp-rails (~> 0.3.6)
  ruby-lsp-rspec (~> 0.1.10)
  ruby-magic (~> 0.6)
  ruby-progressbar (~> 1.10)
  ruby-saml (~> 1.18)
  rubyzip (~> 2.4.0)
  rugged (~> 1.6)
  sanitize (~> 6.0.2)
  sd_notify (~> 0.1.0)
  seed-fu (~> 2.3.7)
  selenium-webdriver (~> 4.21, >= 4.21.1)
  semver_dialects (~> 3.7)
  sentry-rails (~> 5.23.0)
  sentry-ruby (~> 5.23.0)
  sentry-sidekiq (~> 5.23.0)
  shoulda-matchers (~> 6.4.0)
  sidekiq!
  sidekiq-cron (~> 1.12.0)
  sigdump (~> 0.2.4)
  simple_po_parser (~> 1.1.6)
  simplecov (~> 0.22)
  simplecov-cobertura (~> 2.1.0)
  simplecov-lcov (~> 0.8.0)
  slack-messenger (~> 2.3.5)
  snowplow-tracker (~> 0.8.0)
  solargraph (~> 0.54.0)
  solargraph-rspec (~> 0.5.1)
  spamcheck (~> 1.3.0)
  spring (~> 4.3.0)
  spring-commands-rspec (~> 1.0.4)
  sprite-factory (~> 1.7)
  sprockets (~> 3.7.0)
  sprockets-rails (~> 3.5.1)
  ssh_data (~> 1.3)
  stackprof (~> 0.2.26)
  state_machines-activerecord (~> 0.8.0)
  state_machines-rspec (~> 0.6)
  sys-filesystem (~> 1.4.3)
  tanuki_emoji (~> 0.13)
  telesignenterprise (~> 2.6)
  terser (= 1.0.2)
  test-prof (~> 1.4.0)
  test_file_finder (~> 0.3.1)
  thrift (~> 0.22.0)
  timfel-krb5-auth (~> 0.8)
  toml-rb (~> 2.2.0)
  truncato (~> 0.7.13)
  tty-prompt (~> 0.23)
  typhoeus (~> 1.4.0)
  undercover (~> 0.7.0)
  unicode-emoji (~> 4.0)
  unleash (~> 3.2.2)
  uri (= 0.13.2)
  valid_email (~> 0.1)
  validates_hostname (~> 1.0.13)
  version_sorter (~> 2.3)
  view_component (~> 3.23.2)
  vite_rails (~> 3.0.17)
  vite_ruby (~> 3.9.0)
  vmstat (~> 2.3.0)
  warning (~> 1.5.0)
  webauthn (~> 3.0)
  webmock (~> 3.25.0)
  webrick (~> 1.9.0)
  wikicloth (= 0.8.1)
  yajl-ruby (~> 1.4.3)
  yard (~> 0.9)

BUNDLED WITH
   2.7.1
